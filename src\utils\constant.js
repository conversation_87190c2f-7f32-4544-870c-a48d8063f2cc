
const global = {
  imgBasePath: process.env.VUE_APP_IMG_BASE_PATH, // 图片基础地址
  baseApi: process.env.VUE_APP_BASE_URL, // 接口基础地址
  uploadApi: "",
  signKey: "bc37cc63-9d14-4013-8255-9168f24ceacd",
  loadingOption: {
    lock: true,
    text: "拼命加载中",
    background: "rgba(255, 255, 255, 0)",
    customClass: "request-loading"
  },
  disabledDatePickerOptions: {
    disabledDate(time) {
      return time.getTime() > Date.now();
    }
  },
  datePickerOptions: {
    disabledDate(time) {
      return time.getTime() > Date.now();
    },
    shortcuts: [
      {
        text: "今天",
        onClick(picker) {
          picker.$emit("pick", new Date());
        }
      },
      {
        text: "昨天",
        onClick(picker) {
          const date = new Date();
          date.setTime(date.getTime() - 3600 * 1000 * 24);
          picker.$emit("pick", date);
        }
      },
      {
        text: "一周前",
        onClick(picker) {
          const date = new Date();
          date.setTime(date.getTime() - 3600 * 1000 * 24 * 7);
          picker.$emit("pick", date);
        }
      }
    ]
  },
  dateRangePickerOptions: {
    disabledDate(time) {
      return time.getTime() > Date.now();
    },
    shortcuts: [
      {
        text: "最近三天",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 3);
          picker.$emit("pick", [start, end]);
        }
      },
      {
        text: "最近一周",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
          picker.$emit("pick", [start, end]);
        }
      },
      {
        text: "最近一个月",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
          picker.$emit("pick", [start, end]);
        }
      }
    ]
  },
  threePickerOptions: {
    shortcuts: [
      {
        text: "最近一周",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
          picker.$emit("pick", [start, end]);
        }
      },
      {
        text: "最近一个月",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
          picker.$emit("pick", [start, end]);
        }
      },
      {
        text: "最近三个月",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
          picker.$emit("pick", [start, end]);
        }
      },
      {
        text: "最近半年",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 180);
          picker.$emit("pick", [start, end]);
        }
      }
    ]
  },
  /* 表单支持生成的标签 */
  formItemType: {
    text: "text",
    input: "input",
    select: "select",
    date: "date",
    daterange: "daterange",
    datetimerange: "datetimerange",
    radio: "radio",
    radiocancel: "radiocancel",
    textarea: "textarea",
    cascader: "cascader",
    upload: "upload",
    uploadfile: "uploadFile",
    regioncascader: "regioncascader",
    switch: "switch",
    checkbox: "checkbox"
  },
  /* 字典写死的词库 */
  denfindDictionary: {
    SENSITIVE_CODE: "SENSITIVE_CODE", // 敏感词
    EXEMPTION_CODE: "EXEMPTION_CODE", // 豁免词
    SERVICE_UNIT: "SERVICE_UNIT", // 服务项的单位
    LAWYER_SPECIALITY: "LAWYER_SPECIALITY", // 律师擅长领域
    // LAWYER_SPECIALITY: 'The_type_of_channel_problem', // 咨询问题
    CHANNEL_THIRD_TYPE: "CHANNEL_THIRD_TYPE", // 渠道三方类型
    THE_TYPE_OF_LANGDING_PAGE: "The_type_of_landing_page", //  落地页类型
    RETURN_EVENT_TYPE: "RETURN_EVENT_TYPE", // 回传事件类型
    LKY_FOLLOW_MODE: "LKY_FOLLOW_MODE", // 律客云跟进方式
    THIRD_CHANNEL_EVENT_TYPE: "THIRD_CHANNEL_EVENT_TYPE", // 三方案源回传事件
    CASE_TYPES: "CASE_TYPES", // 案源类型
    LAWFIRM_LEVEL: "LAWFIRM_LEVEL", // 律所类型
    CUSTOMER_LEVEL: "CUSTOMER_LEVEL", // 客户类型
    CUSTOMER_VALUE: "CUSTOMER_VALUE", // 客户是否有效
    SERVICE_CATEGORY: "SERVICE_CATEGORY", // 平台服务类型
    CHANNEL_PUT_TYPE: "CHANNEL_PUT_TYPE", // 投放类型
    PRIVILEGE_TYPE: "PRIVILEGE_TYPE", // 会员分类
    T_ORDER_LIST: "T_ORDER_LIST", // 拒绝原因
    OS_VERSION: "OS_VERSION", // 端口
    CMS_LAWYER_BILL_ACTION_TYPE: "CMS_LAWYER_BILL_ACTION_TYPE", // 账单类型（大类）
    CMS_LAWYER_BILL_HIDE_TYPE: "CMS_LAWYER_BILL_HIDE_TYPE", // 账单隐藏类型（小类）
    KEY_IM_WORDS_SENSITIVE: "KEY_IM_WORDS_SENSITIVE"// IM敏感词
  },
  /* 功能权限字段 */
  functionField: {
  },
  /* 查看会话的，类型 */
  chartType: {
    PUBLIC_FREE: 1, // 公共免费案源
    PUBLIC_DEEP: 2, // 公共深度案源
    PUBLIC_PAY: 3, // 公共付费案源
    ONE_TO_ONE: 4 // 一对一付费案源
  },
  // 所有下拉静态数据
  adminConfig: {
    logStatus: [
      {
        value: 0,
        label: "成功"
      },
      {
        value: 1,
        label: "失败"
      }
    ],
    selectOptions: [
      {
        value: 0,
        label: "禁用"
      },
      {
        value: 1,
        label: "启用"
      }
    ],
    officialAccountRole: [
      {
        value: 1,
        label: "群众"
      },
      {
        value: 2,
        label: "律师"
      },
      {
        value: 3,
        label: "官方"
      }
    ],
    accountStatus: [
      {
        value: 0,
        label: "全部"
      },
      {
        value: 1,
        label: "正常"
      },
      {
        value: 2,
        label: "禁用"
      }
    ],
    openStatus: [
      {
        value: 0,
        label: "开启"
      },
      {
        value: 1,
        label: "禁用"
      }
    ],
    yesNo: [
      {
        value: 1,
        label: "是"
      },
      {
        value: 0,
        label: "否"
      }
    ],
    yesNoBoolean: [
      {
        value: true,
        label: "是"
      },
      {
        value: false,
        label: "否"
      }
    ],
    switchType: [
      {
        value: 0,
        label: "下架"
      },
      {
        value: 1,
        label: "上架"
      }
    ],
    columnHidden: [
      {
        value: 0,
        label: "显示"
      },
      {
        value: 1,
        label: "隐藏"
      }
    ],
    versionStatus: [
      {
        value: 0,
        label: "未发布"
      },
      {
        value: 1,
        label: "已发布"
      }
    ],
    versionUpdateStatus: [
      {
        value: 0,
        label: "否"
      },
      {
        value: 1,
        label: "是"
      }
    ],
    feekStatus: [
      {
        value: 1,
        label: "使用体验"
      },
      {
        value: 2,
        label: "功能建议"
      },
      {
        value: 3,
        label: "其他"
      }
    ],
    feekResonStatus: [
      {
        value: 1,
        label: "使立即注销"
      },
      {
        value: 2,
        label: "反馈给专属客服"
      }
    ],
    feekDevice: [
      {
        value: 1,
        label: "iOS"
      },
      {
        value: 2,
        label: "Android"
      },
      {
        value: 3,
        label: "H5"
      },
      {
        value: 4,
        label: "PCWEB"
      },
      {
        value: 5,
        label: "小程序"
      },
      {
        value: 6,
        label: "H5渠道落地页面"
      },
      {
        value: 7,
        label: "长律-iOS"
      },
      {
        value: 8,
        label: "长律-Android"
      }
    ],
    commentStatus: [
      {
        value: 0,
        label: "待审核"
      },
      {
        value: 1,
        label: "审核通过"
      },
      {
        value: 2,
        label: "审核不通过"
      }
    ],
    answerStatus: [
      {
        value: 1,
        label: "正常"
      },
      {
        value: 2,
        label: "已关闭"
      }
    ],
    commentShStatus: [
      {
        value: 1,
        label: "通过"
      },
      {
        value: 2,
        label: "不通过"
      }
    ],
    checkStatus: [
      {
        value: 1,
        label: "未审核"
      },
      {
        value: 2,
        label: "通过"
      },
      {
        value: 3,
        label: "未通过"
      }
    ],
    feekSolveStatus: [
      {
        value: 0,
        label: "未解决"
      },
      {
        value: 1,
        label: "已解决"
      }
    ],
    reportTypesOne: [
      {
        value: 1,
        label: "内容"
      },
      {
        value: 2,
        label: "评论"
      }
    ],
    reportTypes: [
      {
        value: 0,
        label: "开启"
      },
      {
        value: 1,
        label: "禁用"
      }
    ],
    detailAttribute: [
      {
        value: 1,
        label: "普通"
      },
      {
        value: 2,
        label: "热门"
      },
      {
        value: 3,
        label: "精选"
      }
    ],
    delStatus: [
      {
        value: 0,
        label: "正常"
      },
      {
        value: 1,
        label: "已删除"
      }
    ],
    contentMode: [
      {
        value: 1,
        label: "游客模式"
      },
      {
        value: 2,
        label: "登录模式"
      }
    ],
    channelStatus: [
      {
        value: 0,
        label: "上架"
      },
      {
        value: 1,
        label: "下架"
      }
    ],
    marketStatus: [
      {
        value: 0,
        label: "正常"
      },
      {
        value: 1,
        label: "冻结"
      }
    ],
    advertType: [
      {
        value: 1,
        label: "banner广告"
      },
      {
        value: 2,
        label: "信息流广告"
      },
      {
        value: 3,
        label: "弹窗广告"
      }
    ],
    advertOs: [
      {
        value: 1,
        label: "pc"
      },
      {
        value: 2,
        label: "app"
      },
      {
        value: 3,
        label: "wap"
      },
      {
        value: 4,
        label: "小程序"
      }
    ],
    advertStatus: [
      {
        value: 0,
        label: "冻结"
      },
      {
        value: 1,
        label: "正常"
      }
    ],
    advertIssueStatus: [
      {
        value: 1,
        label: "未发布"
      },
      {
        value: 2,
        label: "已发布 "
      },
      {
        value: 3,
        label: "已过期 "
      }
    ],
    vipStatusType: [
      {
        value: 0,
        label: "已过期"
      },
      {
        value: 1,
        label: "未过期 "
      }
    ],
    dictionariesType: [
      {
        value: 1,
        label: "文本"
      },
      {
        value: 2,
        label: "数值"
      }
    ],
    handleBusinessType: [
      {
        value: 0,
        label: "其他"
      },
      {
        value: 1,
        label: "新增"
      },
      {
        value: 2,
        label: "修改"
      },
      {
        value: 3,
        label: "删除"
      },
      {
        value: 4,
        label: "导出"
      },
      {
        value: 5,
        label: "导入"
      }
    ],
    lawyerAuthStatus: [
      {
        value: 1,
        label: "待认证"
      },
      {
        value: 2,
        label: "已认证"
      },
      {
        value: 3,
        label: "未认证"
      },
      {
        value: 4,
        label: "认证失败"
      }
    ],
    lawyerExamineStatus: [
      {
        value: 1,
        label: "暂未上传"
      },
      {
        value: 2,
        label: "机审未通过"
      },
      {
        value: 3,
        label: "机审通过待人审"
      },
      {
        value: 4,
        label: "人审通过"
      },
      {
        value: 5,
        label: "人审不通过"
      }
    ],
    feekUserRole: [
      {
        value: 1,
        label: "群众"
      },
      {
        value: 2,
        label: "律师"
      }
    ],
    orderStatus: [
      {
        value: 0,
        label: "全部"
      },
      {
        value: 1,
        label: "待支付"
      },
      {
        value: 2,
        label: "已支付"
      },
      {
        value: 3,
        label: "已完成"
      },
      {
        value: 4,
        label: "已失效"
      },
      {
        value: 5,
        label: "退款中"
      },
      {
        value: 6,
        label: "已退款"
      }
    ],
    payTypeOrder: [
      {
        value: 1,
        label: "支付宝"
      },
      {
        value: 2,
        label: "微信"
      },
      {
        value: 10,
        label: "上海汇付-支付宝"
      },
      {
        value: 11,
        label: "汇付宝-微信"
      },
      {
        value: 12,
        label: "抖音"
      }
    ],
    payStatus: [
      {
        value: 1000,
        label: "待支付"
      },
      {
        value: 1001,
        label: "已支付"
      },
      {
        value: 1002,
        label: "交易关闭(支付超时)"
      }
    ],
    refundStatus: [
      // {
      //   value: 2000,
      //   label: '未退款'
      // },
      {
        value: 2001,
        label: "退款中"
      },
      {
        value: 2002,
        label: "退款成功"
      },
      {
        value: 2003,
        label: "退款失败"
      }
    ],
    // 律师审核状态[1:审核中,2:同意退款,3:拒绝退款]
    lawyerCheckStatus: [
      {
        value: 1,
        label: "审核中"
      },
      {
        value: 2,
        label: "同意退款"
      },
      {
        value: 3,
        label: "拒绝退款"
      }
    ],
    lawyersStatus: [
      {
        value: 3000,
        label: "待接单"
      },
      {
        value: 3001,
        label: "已接单"
      },
      {
        value: 3002,
        label: "已拒绝"
      },
      {
        value: 3003,
        label: "超时未接单"
      }
    ],
    serviceStatus: [
      {
        value: 4000,
        label: "未完成"
      },
      {
        value: 4001,
        label: "已完成"
      },
      {
        value: 4002,
        label: "已失效"
      }
    ],
    lawyerCnfirmStatus: [
      {
        value: 5000,
        label: "未确认"
      },
      {
        value: 5001,
        label: "已确认"
      }
    ],
    usersCnfirmStatus: [
      {
        value: 6000,
        label: "未确认"
      },
      {
        value: 6001,
        label: "已确认"
      }
    ],
    noticeTypes: [
      {
        value: 2,
        label: "聚梦_法临c端通知短信渠道"
      },
      {
        value: 4,
        label: "push单条消息"
      },
      {
        value: 5,
        label: "站内信消息"
      },
      {
        value: 6,
        label: "聚梦_法临c端验证验证码渠道"
      },
      {
        value: 7,
        label: "聚梦_法临b端长律通知短信渠道"
      },
      {
        value: 8,
        label: "聚梦_法临b端长律验证码渠道"
      },
      {
        value: 9,
        label: "push多条消息"
      },
      {
        value: 10,
        label: "聚合短信平台"
      },
      {
        value: 11,
        label: "聚梦-法临c端落地页通知短信渠道"
      },
      {
        value: 12,
        label: "聚梦-法临c端营销短信实现"
      },
      {
        value: 13,
        label: "大河三通-营销类通道"
      },
      {
        value: 14,
        label: "大河三通-行业类通过（验证码和通知）"
      }
      // {
      //   value: 1,
      //   label: '容联云短信'
      // },
      // {
      //   value: 2,
      //   label: '聚梦短信'
      // },
      // {
      //   value: 3,
      //   label: '环信消息'
      // },
      // {
      //   value: 4,
      //   label: '极光通知'
      // },
      // {
      //   value: 5,
      //   label: '系统消息'
      // },
      // {
      //   value: 6,
      //   label: '法临验证码'
      // },
      // {
      //   value: 7,
      //   label: '长律通知'
      // },
      // {
      //   value: 8,
      //   label: '长律验证码'
      // },
      // {
      //   value: 9,
      //   label: '简单push推送'
      // },
      // {
      //   value: 10,
      //   label: '聚合短信'
      // }
    ],
    clientTypes: [
      {
        value: 1,
        label: "法临"
      },
      {
        value: 2,
        label: "长律"
      },
      {
        value: 3,
        label: "法临落地页"
      },
      {
        value: 4,
        label: "小程序"
      }
    ],
    // targetTypes: [
    //   {
    //     value: 1,
    //     label: '法临留资'
    //   },
    //   {
    //     value: 2,
    //     label: '法临支付'
    //   },
    //   {
    //     value: 3,
    //     label: '法临其他'
    //   },
    //   {
    //     value: 4,
    //     label: '长律认证'
    //   },
    //   {
    //     value: 5,
    //     label: '长律支付'
    //   },
    //   {
    //     value: 6,
    //     label: '长律邀请好友'
    //   },
    //   {
    //     value: 7,
    //     label: '长律接单'
    //   },
    //   {
    //     value: 8,
    //     label: '长律其他'
    //   }
    // ],
    OS: [
      { label: "全平台", value: 0 },
      { label: "PC", value: 1 },
      { label: "APP", value: 2 },
      { label: "WAP", value: 3 }
    ],
    withdrawStatus: [
      {
        value: 1,
        label: "已提交"
      },
      {
        value: 2,
        label: "提现失败"
      },
      {
        value: 3,
        label: "提现成功"
      },
      {
        value: 4,
        label: "系统自动打款中"
      },
      {
        value: 5,
        label: "审核通过,第三方打款失败 "
      }
    ],
    punishStatus: [
      {
        value: 1,
        label: "处罚中"
      },
      {
        value: 0,
        label: "处罚结束"
      }
    ],
    limitType: [
      {
        value: 1,
        label: "案源"
      },
      {
        value: 2,
        label: "付费咨询"
      },
      {
        value: 3,
        label: "即时咨询"
      }
    ],
    vipPayType: [
      {
        value: 1,
        label: "支付宝"
      },
      {
        value: 2,
        label: "微信"
      },
      {
        value: 3,
        label: "余额"
      },
      {
        value: 4,
        label: "微信+余额"
      }
    ],
    vipOrderPayStatus: [
      {
        value: 1000,
        label: "待支付"
      },
      {
        value: 1001,
        label: "已支付"
      },
      {
        value: 1002,
        label: "已关闭"
      },
      {
        value: 2002,
        label: "已退款"
      },
      {
        value: 4002,
        label: "已失效"
      }
    ],
    vipOrderType: [
      {
        value: 1,
        label: "钱包充值"
      },
      {
        value: 2,
        label: "会员充值（正价会员）"
      },
      {
        value: 3,
        label: "会员充值（加购会员）"
      },
      {
        value: 4,
        label: "二月律师充值活动"
      }
    ],
    isBindPone: [
      {
        value: 1,
        label: "绑定"
      },
      {
        value: 0,
        label: "不绑定"
      }
    ],
    caseCheckStatus: [
      {
        value: 1,
        label: "待审核"
      },
      {
        value: 2,
        label: "审核通过"
      },
      {
        value: 3,
        label: "审核未通过"
      },
      {
        value: 4,
        label: "机审通过待人审"
      }
    ],
    iosAB: [
      {
        value: 1,
        label: "开启"
      },
      {
        value: 0,
        label: "关闭"
      }
    ],
    isReplay: [
      {
        value: 0,
        label: "未回复"
      },
      {
        value: 1,
        label: "已回复"
      }
    ],
    lawReplay: [
      // {
      //   value: 0,
      //   label: '待接单'
      // },
      // {
      //   value: 1,
      //   label: '已接单'
      // },
      // {
      //   value: 2,
      //   label: '已忽略'
      // }
      {
        value: 0,
        label: "未回复"
      },
      {
        value: 1,
        label: "已回复"
      },
      {
        value: 2,
        label: "已忽略"
      }
    ],
    customMessageShowType: [
      {
        value: 1,
        label: "通知"
      },
      {
        value: 2,
        label: "站内通知"
      },
      {
        value: 3,
        label: "全部"
      }
    ],
    customMessageSendType: [
      {
        value: 1,
        label: "定时推送"
      },
      {
        value: 2,
        label: "立即推送"
      }
    ],
    customMessageSendStatus: [
      {
        value: 1,
        label: "待推送"
      },
      {
        value: 2,
        label: "已推送"
      }
    ],
    caseLockStatus: [
      {
        value: 0,
        label: "未锁定"
      },
      {
        value: 1,
        label: "已锁定"
      }
    ],
    caseStatus: [
      {
        value: 0,
        label: "正常"
      },
      // {
      //   value: 1,
      //   label: '已解决'
      // },
      // {
      //   value: 2,
      //   label: '未解决'
      // },
      {
        value: 3,
        label: "待审核"
      },
      {
        value: 9,
        label: "已关闭"
      }
    ],
    caseTypes: [
      {
        value: "刑事案件",
        label: "刑事案件"
      },
      {
        value: "民事案件",
        label: "民事案件"
      },
      {
        value: "行政案件",
        label: "行政案件"
      }
    ],
    caseDocumentType: [
      {
        value: "裁定书",
        label: "裁定书"
      },
      {
        value: "判决书",
        label: "判决书"
      }
    ],
    caseTrialProcedure: [
      {
        value: "刑事一审",
        label: "刑事一审"
      },
      {
        value: "刑事二审",
        label: "刑事二审"
      },
      {
        value: "民事一审",
        label: "民事一审"
      },
      {
        value: "民事二审",
        label: "民事二审"
      },
      {
        value: "行政一审",
        label: "行政一审"
      },
      {
        value: "行政二审",
        label: "行政二审"
      }
    ],
    adverRange: [
      {
        value: 0,
        label: "注册状态"
      },
      {
        value: 1,
        label: "认证状态"
      },
      {
        value: 2,
        label: "是否会员"
      },
      {
        value: 3,
        label: "会员等级"
      }
    ],
    followStatus: [
      {
        value: 1,
        label: "待跟进"
      },
      {
        value: 2,
        label: "跟进中"
      },
      {
        value: 3,
        label: "已跟进"
      }
    ],
    caseAddService: [
      {
        value: "CASE_SOURCE_LOCK_TIME",
        label: "案源锁定时长"
      },
      {
        value: "CASE_SOURCE_LOCK_COUNT",
        label: "案源锁定次数"
      },
      {
        value: "CASE_FLOW_PACKET",
        label: "流量包"
      }
    ],
    sysParamsType: [
      {
        value: 1,
        label: "客户端参数"
      },
      {
        value: 2,
        label: "服务端参数"
      }
    ],
    qaStatus: [
      {
        value: 1,
        label: "正常"
      },
      {
        value: 0,
        label: "待审核"
      },
      {
        value: 2,
        label: "已关闭"
      },
      {
        value: 3,
        label: "没有回复自动关闭"
      }
    ],
    consultationStatus: [
      {
        value: 1,
        label: "待接单"
      },
      {
        value: 2,
        label: "已接单"
      },
      {
        value: 3,
        label: "服务中"
      },
      {
        value: 4,
        label: "已完成"
      }
    ],
    forwardStatus: [
      {
        value: 0,
        label: "待转发"
      },
      {
        value: 1,
        label: "已转发"
      },
      {
        value: 2,
        label: "不转发"
      }
    ],
    gradeStatus: [
      {
        value: 2,
        label: "高"
      },
      {
        value: 1,
        label: "中"
      },
      {
        value: 0,
        label: "低"
      }
    ],
    lawyersTypes: [
      // {
      //   value: 1,
      //   label: '自营律师'
      // },
      {
        value: 0,
        label: "律师"
      },
      {
        value: 2,
        label: "实习律师"
      },
      {
        value: -1,
        label: "未知"
      }
    ],
    publishRelationCode: [
      {
        value: 0,
        label: "未关联深度案源"
      },
      {
        value: 1,
        label: "关联1条深度案源"
      },
      {
        value: 2,
        label: "关联2条及以上深度案源"
      }
    ],
    caseStatusFreeCase: [
      {
        value: 0,
        label: "待审核"
      },
      {
        value: 1,
        label: "正常"
      },
      {
        value: 9,
        label: "关闭"
      }
    ],
    extendTimes: [
      {
        value: 0,
        label: "0次"
      },
      {
        value: 1,
        label: "1次"
      },
      {
        value: 2,
        label: "2次及以上的深度案源数据"
      }
    ],
    deepStatus: [
      {
        value: 1,
        label: "待接单"
      },
      {
        value: 2,
        label: "已接单"
      },
      {
        value: 3,
        label: "服务中"
      },
      {
        value: 4,
        label: "已完成"
      },
      {
        value: 9,
        label: "已关闭"
      }
    ],
    typeSource: [
      {
        value: 1,
        label: "用户填写的"
      },
      {
        value: 2,
        label: "语义分析（数据分析）"
      },
      {
        value: 3,
        label: "渠道绑定"
      },
      {
        value: 4,
        label: "默认"
      }
    ],
    typeSourceFree: [
      {
        value: 1,
        label: "用户填写的"
      },
      {
        value: 2,
        label: "语义分析（数据分析）"
      },
      {
        value: 3,
        label: "渠道绑定"
      },
      {
        value: 4,
        label: "默认"
      },
      {
        value: 9,
        label: "后台修改"
      }
    ],
    typeValueIsMatch: [
      {
        value: 1,
        label: "筛选出用户选择来源的问题类型≠语义分析问题类型"
      },
      {
        value: 2,
        label: "用户选择来源的问题类型＝语义分析问题类型的案源数据"
      }
    ],
    paidStatus: [
      {
        value: 0,
        label: "待支付"
      },
      {
        value: 1,
        label: "待接单"
      },
      {
        value: 2,
        label: "已接单"
      },
      {
        value: 3,
        label: "服务中"
      },
      {
        value: 4,
        label: "已完成"
      },
      {
        value: 8,
        label: "待完成"
      },
      {
        value: 9,
        label: "已关闭"
      }
    ],
    thiredfollowStatus: [
      {
        value: 0,
        label: "待跟进"
      },
      {
        value: 1,
        label: "跟进中"
      },
      {
        value: 2,
        label: "已跟进"
      }
    ],
    thiredCaseStatus: [
      {
        value: 0,
        label: "创建"
      },
      {
        value: 1,
        label: "有效"
      },
      {
        value: 2,
        label: "无效"
      },
      {
        value: 10,
        label: "电话未接通"
      }
    ],
    FollwUpWays: [
      {
        value: 0,
        label: "电话"
      },
      {
        value: 1,
        label: "微信"
      },
      {
        value: 2,
        label: "短信"
      },
      {
        value: 3,
        label: "QQ"
      },
      {
        value: 4,
        label: "IM"
      },
      {
        value: 5,
        label: "邮件"
      },
      {
        value: 6,
        label: "其他"
      }
    ],
    userType: [
      {
        value: 1,
        label: "法临用户"
      },
      {
        value: 2,
        label: "长律用户"
      }
    ],
    isSourceTypeValue: [
      {
        value: 1,
        label: "可筛选出自营律师转发问题类型＝原公共免费案源问题类型"
      },
      {
        value: 2,
        label: "可筛选出自营律师转发问题类型≠原公共免费案源问题类型的案源数据"
      }
    ],
    deepStatusType: [
      {
        value: 2,
        label: "高"
      },
      {
        value: 1,
        label: "中"
      },
      {
        value: 0,
        label: "低"
      }
    ],
    scoreTypes: [
      {
        value: "0-1",
        label: "0~1分（含）"
      },
      {
        value: "1-2",
        label: "1~2分（含）"
      },
      {
        value: "2-3",
        label: "2~3分（含）"
      },
      {
        value: "3-4",
        label: "3~4分（含）"
      },
      {
        value: "4-5",
        label: "4~5分（含）"
      }
    ],
    lawyerCaseTypes: [
      {
        value: 2,
        label: "公共深度案源"
      },
      {
        value: 3,
        label: "公共付费案源"
      },
      {
        value: 4,
        label: "一对一付费咨询"
      },
      {
        value: 6,
        label: "电话咨询"
      },
      {
        value: 20,
        label: "v2案源服务订单"
      }
    ],
    buyStatus: [
      {
        value: 1,
        label: "购买成功"
      },
      {
        value: 2,
        label: "购买失败"
      }
    ],
    oneToOneCcaseStatus: [
      {
        value: 0,
        label: "待支付"
      },
      {
        value: 1,
        label: "待接单"
      },
      {
        value: 3,
        label: "服务中"
      },
      {
        value: 4,
        label: "已完成"
      },
      {
        value: 9,
        label: "已关闭"
      }
    ],
    contractStatus: [
      {
        value: 0,
        label: "未生效"
      },
      {
        value: 1,
        label: "生效中"
      },
      {
        value: 2,
        label: "已失效"
      }
    ],
    lkyFollwUpWays: [
      {
        value: 1,
        label: "沟通记录"
      },
      {
        value: 2,
        label: "线下会见记录"
      },
      {
        value: 6,
        label: "线上会见记录"
      },
      {
        value: 3,
        label: "成交记录"
      },
      {
        value: 4,
        label: "重单"
      },
      {
        value: 5,
        label: "无效"
      }
    ],
    lkyFollwUpWaysTextPd: [
      {
        value: 0,
        label: "待跟进"
      },
      {
        value: 1,
        label: "跟进中"
      },
      {
        value: 2,
        label: "已线下会见"
      },
      {
        value: 6,
        label: "已线上会见"
      },
      {
        value: 3,
        label: "已成交"
      },
      {
        value: 4,
        label: "重单"
      },
      {
        value: 5,
        label: "无效"
      }
    ],
    lkyDistribute: [
      {
        value: 0,
        label: "未派单"
      },
      {
        value: 1,
        label: "已派单"
      }
    ],
    lkyWorkScheduleStatus: [
      {
        value: 0,
        label: "启动"
      },
      {
        value: 1,
        label: "禁用"
      }
    ],
    lkyCustormerSource: [
      {
        value: 0,
        label: "手动录入"
      },
      {
        value: 1,
        label: "自动录入"
      },
      {
        value: 3,
        label: "第三方案源导入"
      }
    ],
    lkyPayType: [
      {
        value: "0",
        label: "全额支付"
      }
      // {
      //   value: '1',
      //   label: '分期支付'
      // }
    ],
    lkyFollowTimes: [
      {
        value: 3,
        label: "三天内"
      },
      {
        value: 5,
        label: "五天内"
      },
      {
        value: 7,
        label: "七天内"
      }
    ],
    sourceYesNo: [
      {
        value: 1,
        label: "否"
      },
      {
        value: 2,
        label: "是"
      }
    ],
    lawyerNameStatus: [
      {
        value: 0,
        label: "未实名"
      },
      {
        value: 1,
        label: "已实名"
      }
    ],
    distributionRecordStatus: [
      {
        value: 0,
        label: "未读"
      },
      {
        value: 1,
        label: "已读"
      },
      {
        value: 2,
        label: "有效"
      },
      {
        value: 9,
        label: "无效"
      }
    ],
    caseBagSource: [
      {
        value: 1,
        label: "公共免费案源"
      },
      {
        value: 2,
        label: "公共付费案源"
      },
      {
        value: 9,
        label: "其他"
      }
    ],
    caseBagSourceStatus: [
      {
        value: 0,
        label: "无效"
      },
      {
        value: 1,
        label: "有效"
      }
    ],
    caseBagSourceEffective: [
      {
        value: 1,
        label: "启用"
      },
      {
        value: 9,
        label: "禁用"
      }
    ],
    payTypes: [
      {
        value: 1,
        label: "图文咨询"
      },
      {
        value: 2,
        label: "电话咨询"
      }
    ],
    dayMembers: [
      // {
      //   value: 0,
      //   label: '3天'
      // },
      // {
      //   value: 1,
      //   label: '7天'
      // },
      // {
      //   value: 2,
      //   label: '半月'
      // },
      {
        value: 3,
        label: "月卡"
      },
      {
        value: 4,
        label: "季卡"
      },
      {
        value: 5,
        label: "半年卡"
      },
      {
        value: 6,
        label: "年卡"
      },
      {
        value: 7,
        label: "自定义"
      }
    ],
    openRangeMembers: [
      {
        value: 0,
        label: "新用户"
      },
      {
        value: 1,
        label: "老用户"
      },
      {
        value: 2,
        label: "全部用户"
      }
    ],
    gzhOldUsers: [
      {
        value: 1,
        label: "新用户"
      },
      {
        value: 0,
        label: "老用户"
      }
    ],
    userRange: [
      {
        value: 0,
        label: "全部用户"
      },
      {
        value: 1,
        label: "新用户"
      }
    ],
    membersLevel: [
      {
        value: 0,
        label: "A级"
      },
      {
        value: 1,
        label: "B级"
      },
      {
        value: 2,
        label: "C级"
      },
      {
        value: 3,
        label: "D级"
      }
    ],
    // 业务2.0
    isCaseUpdateT: [
      {
        value: 0,
        label: "未更新"
      },
      {
        value: 1,
        label: "已更新"
      }
    ],
    isCasePayT: [
      {
        value: 0,
        label: "未付费"
      },
      {
        value: 1,
        label: "已付费"
      }
    ],
    isCaseStatusT: [
      {
        value: 0,
        label: "待审核"
      },
      {
        value: 1,
        label: "待消耗（审核通过）"
      },
      {
        value: 2,
        label: "已消耗"
      },
      {
        value: 3,
        label: "完全消耗"
      },
      {
        value: 9,
        label: "已关闭 "
      }
    ],
    // 数据统计，业务2.0
    osTypes: [
      {
        value: "ANDROID",
        label: "安卓 "
      },
      {
        value: "IPHONE",
        label: "苹果"
      },
      {
        value: "PC",
        label: "电脑 "
      }
    ],
    osVersion: [
      {
        value: 1,
        label: "IOS "
      },
      {
        value: 2,
        label: "Android "
      },
      {
        value: 3,
        label: "H5下载页"
      },

      {
        value: 4,
        label: "PCWEB "
      },
      {
        value: 5,
        label: "wap(m站) "
      },
      {
        value: 6,
        label: "H5渠道落地页面"
      },
      {
        value: 7,
        label: "长律IOS"
      },
      {
        value: 8,
        label: "长律Android "
      },
      {
        value: 9,
        label: "长律-Android"
      },
      {
        value: 10,
        label: "cms"
      },
      {
        value: 11,
        label: "抖音小程序 "
      },
      {
        value: 12,
        label: "百度小程序 "
      },
      {
        value: 13,
        label: "微信小程序 "
      }
    ],
    serverWayT: [
      {
        value: 1,
        label: "系统派发 "
      },
      {
        value: 2,
        label: "律师抢单 "
      },
      {
        value: 3,
        label: "用户主动咨询 "
      },
      {
        value: 4,
        label: "咨询师抢单"
      },
      {
        value: 5,
        label: "付费派发"
      },
      {
        value: 6,
        label: "付费一对一咨询"
      },
      {
        value: 7,
        label: "一对一咨询"
      },
      {
        value: 8,
        label: "一口价抢单"
      },
      {
        value: 9,
        label: "案源包"
      }
    ],
    lawyerTypeOrderOto: [
      {
        value: -1,
        label: "全部"
      },
      {
        value: 1,
        label: "1V1服务 "
      },
      {
        value: 3,
        label: "1v1公共咨询服务 "
      }
    ],
    // 服务类型
    lawyerTypeService: [
      {
        value: -1,
        label: "全部"
      },
      {
        value: 3,
        label: "平台服务 "
      },
      {
        value: 1,
        label: "1v1律师服务 "
      }
    ],
    // 申述状态、 0未申诉、1待审核、2审核通过、3审核不通过
    claimStatus: [
      {
        value: 0,
        label: "未申诉 "
      },
      {
        value: 1,
        label: "待审核 "
      },
      {
        value: 2,
        label: "审核通过 "
      },
      {
        value: 3,
        label: "审核不通过 "
      }
    ],
    lawyerLockStatus: [
      {
        value: 0,
        label: "待服务"
      },
      {
        value: 1,
        label: "服务中 "
      },
      {
        value: 2,
        label: "待完成 "
      },
      {
        value: 3,
        label: "已完成 "
      },
      {
        value: 9,
        label: "取消锁定"
      },
      {
        value: 91,
        label: "用户退单"
      },
      {
        value: 99,
        label: "已关闭"
      }
    ],
    lawyerStatusOto: [
      {
        value: 0,
        label: "待服务"
      },
      {
        value: 1,
        label: "服务中 "
      },
      {
        value: 3,
        label: "已完成"
      },
      {
        value: 91,
        label: "用户退款"
      },
      {
        value: 9,
        label: "取消锁定(历史)"
      },
      {
        value: 99,
        label: "已关闭(历史)"
      }
    ],
    lawyerConsumeType: [
      {
        value: 2,
        label: "律师抢单 "
      },
      {
        value: 1,
        label: "系统派单 "
      },
      {
        value: 8,
        label: "一口价抢单 "
      }
    ],
    lawyerRoleT: [
      {
        value: 0,
        label: "律师 "
      },
      {
        value: 2,
        label: "法律咨询师 "
      }
    ],
    feekStatusOrders: [
      {
        value: 1,
        label: "审核中"
      },
      {
        value: 2,
        label: "审核成功"
      },
      {
        value: 3,
        label: "审核失败"
      }
    ],
    withdrawStatusLawyer: [
      {
        value: 1,
        label: "已提交"
      },
      {
        value: 2,
        label: "提现失败"
      },
      {
        value: 3,
        label: "已通过待系统打款"
      },
      {
        value: 4,
        label: "提现成功"
      },
      {
        value: 5,
        label: "审核通过,第三方打款失败"
      }
    ],
    serverOrderStatus: [
      {
        value: 0,
        label: "待锁定 "
      },
      {
        value: 1,
        label: "服务中 "
      },
      {
        value: 2,
        label: "待完成 "
      },
      {
        value: 3,
        label: "已完成 "
      },
      {
        value: 9,
        label: "取消锁定"
      },
      {
        value: 91,
        label: "用户退单"
      },
      {
        value: 99,
        label: "律师反无效"
      },
      {
        value: 2001,
        label: "退款中"
      },
      {
        value: 2002,
        label: "退款成功"
      },
      {
        value: 2003,
        label: "退款失败"
      }
    ],
    leadFollowUpPageStatus: [
      {
        value: 1,
        label: "服务中"
      },
      {
        value: 2,
        label: "跟进结束"
      },
    ],
    clueSortType: [
      {
        value: 2,
        label: "抢单时间由近及远"
      },
      {
        value: 1,
        label: "抢单时间由远及近"
      },
    ],
    shareProfitState: [
      {
        value: 0,
        label: "未分佣"
      },
      {
        value: 1,
        label: "待分佣"
      },
      {
        value: 2,
        label: "已分佣"
      },
      {
        value: 9,
        label: "不分佣"
      }
    ],
    scopeBusiness: [
      {
        value: 1,
        label: "C端法临"
      },
      {
        value: 2,
        label: "B端长律"
      }
    ],
    goodNewsType: [
      {
        value: 1,
        label: "成单喜报"
      }
    ],
    xtmessageType: [
      {
        value: 1,
        label: "版本更新"
      },
      {
        value: 2,
        label: "活动通知"
      },
      {
        value: 3,
        label: "成单喜报"
      },
      {
        value: 4,
        label: "其他"
      }
    ],
    xtterminalType: [
      {
        value: 1,
        label: "app"
      }
    ],
    /** 奖励类型 */
    awardType: [
      {
        value: 0,
        label: "现金"
      },
      {
        value: 1,
        label: "卡券"
      }
    ],
    /** 任务类型 */
    taskType: [
      {
        value: 0,
        label: "每日任务"
      },
      {
        value: 1,
        label: "新手任务"
      },
      {
        value: 2,
        label: "服务任务"
      }
    ],
    /** 任务状态 */
    taskStateType: [
      {
        value: 0,
        label: "启用中"
      },
      {
        value: 1,
        label: "停用"
      }
    ],
    /** 卡券类型 */
    couponType: [
      {
        value: 1,
        label: "现金券"
      },
      {
        value: 2,
        label: "会员体验券"
      },
      {
        label: "法临币现金券",
        value: 3
      }, {
        label: "法临币充值券",
        value: 4
      }
    ],
    serveTypes: [
      {
        value: 1,
        label: "平台派发"
      },
      {
        value: 2,
        label: "律师抢单"
      },
      {
        value: 3,
        label: "用户主动咨询"
      },
      {
        value: 4,
        label: "咨询师抢单"
      },
      {
        value: 5,
        label: "付费派发"
      },
      {
        value: 6,
        label: "用户付费一对一咨询"
      },
      {
        value: 7,
        label: "一对一咨询"
      },
      {
        value: 8,
        label: "律师一口价抢单"
      }
    ],
    activityStatus: [
      {
        value: 1,
        label: "进行中"
      },
      {
        value: 2,
        label: "已结束"
      }
    ],
    memberDiscountType: [
      {
        value: 1,
        label: "立减"
      },
      {
        value: 2,
        label: "折扣"
      }
    ],
    memberConsumerType: [
      {
        value: 0,
        label: "全部用户"
      },
      {
        value: 1,
        label: "已认证用户"
      },
      {
        value: 2,
        label: "已注册新用户"
      }
    ],
    targetTypes: [
      {
        value: 1,
        label: "全体用户"
      },
      {
        value: 2,
        label: "认证用户"
      },
      {
        value: 3,
        label: "会员用户"
      },
      {
        value: 4,
        label: "未认证用户"
      }
    ],
    lawyerStatusCard: [
      {
        value: 0,
        label: "未使用"
      },
      {
        value: 1,
        label: "已使用"
      },
      {
        value: 9,
        label: "已失效"
      }
    ],
    lawyerStatuSourceType: [
      {
        value: 0,
        label: "自然增长"
      },
      {
        value: 1,
        label: "律师拉新"
      },
      {
        value: 2,
        label: "商务拉新"
      }
    ],
    articleSource: [
      { label: "问律师", value: 1 },
      { label: "法律问答", value: 2 }
    ],
    // 2.2.1 新增 来源字段
    resourceType: [
      {
        value: "",
        label: "全部"
      },
      {
        value: 1,
        label: "App-问律师"
      },
      {
        value: 2,
        label: "App-打官司"
      },
      {
        value: 0,
        label: "H5落地页"
      }
    ],
    // 2.2.4新增 -- 问题状态
    NewQaStatus: [
      {
        value: 1,
        label: "正常"
      },
      {
        value: 0,
        label: "待审核"
      },
      {
        value: 2,
        label: "已关闭"
      }
    ],
    // 消耗状态
    consumeStatus: [
      {
        value: 0,
        label: "待消耗"
      },
      {
        value: 1,
        label: "已消耗"
      },
      {
        value: 2,
        label: "完全消耗"
      }
    ],
    caseConsumeType: [
      { label: "抢单", value: "0" },
      { label: "独享价", value: "1" }
    ],
    caseType: [
      { label: "精准案源", value: "1" },
      { label: "优质案源", value: "2" }
    ],
    cityTypes: [
      { label: "头部城市", value: "1" },
      { label: "腰部城市", value: "2" },
      { label: "底部城市", value: "3" },
      { label: "边缘城市", value: "4" },
      { label: "剔除城市", value: "5" }
    ],
    isMemberName: [
      { label: "非会员", value: "0" },
      { label: "会员", value: "1" }
    ],
    orderGrabbingTypePay: [
      { label: "充值抢单", value: 1 },
      { label: "赠送抢单", value: 2 },
      { label: "临时账户", value: 4 },
      { label: "会员免费次数", value: 9 }
    ],
    lawyerTypeAnalysis: [
      { label: "会员", value: 1 },
      { label: "充值法临币律师", value: 0 }
    ],
    // 成案信息渠道
    newsChannelTypeList: [
      { label: "精准案源", value: 1 },
      { label: "优质案源", value: 2 },
      { label: "付费咨询", value: 3 },
      { label: "即时咨询", value: 4 }
    ],
    accountType: [
      { label: "收入", value: 1 },
      { label: "支出", value: 2 }
    ],
    serviceBagDistributeStatus: [
      { label: "待锁定", value: 0 },
      { label: "服务中", value: 1 },
      { label: "待完成", value: 2 },
      { label: "已完成", value: 3 },
      { label: "取消锁定", value: 9 },
      { label: "用户退单", value: 91 },
      { label: "律师退单", value: 99 }
    ], leadRecordsStatus: [
      { label: "即时咨询", value: 1 },
      { label: "案源", value: 2 },
    ], depositType: [
      { label: "线上充值", value: 0 },
      { label: "线下充值", value: 1 },
      { label: '系统赠送或补偿', value: 2 }
    ]
  }

};

export const headerConfig = {
  osVersion: 15
};
export default global;

