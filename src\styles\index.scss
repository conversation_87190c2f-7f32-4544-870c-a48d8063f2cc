.text-center{
  text-align: center;
}

.flex-vertically-centered {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-column-centered {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.flex-dis {
  display: flex;
}
.flex-column {
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-space-between {
  justify-content: space-between;
}
.flex-space-around {
  justify-content: space-around;
}
.flex-space-evenly {
  justify-content: space-evenly;
}

.flex-space-center {
  justify-content: center;
}
.flex-space-end {
  justify-content: flex-end;
}
.flex-align-center{
  align-items: center;
}
.flex-align-end{
  align-items: flex-end;
}
.flex-1 {
  flex: 1;
}
.flex-shrink{
  flex-shrink: 0;
}
.height-max{
  height: 100%;
}

.admin-main-title{
  font-size: 20px !important;
  font-weight: bold;
}

.admin-title{
  font-size: 18px !important;
  font-weight: bold;
}

.admin-subtitle{
  font-size: 16px;
  font-weight: 600;
}

.admin-text{
  font-size: 14px;
  font-weight: normal;
}

.font-16 {
  font-size: 16px;
}

.font-14 {
  font-size: 14px;
}

.font-12 {
  font-size: 12px;
}

.d-shrink-0 {
  flex-shrink: 0;
}
