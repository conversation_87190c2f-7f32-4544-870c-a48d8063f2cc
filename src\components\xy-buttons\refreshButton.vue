
<template>
  <el-button type="primary" size="mini" icon="el-icon-refresh" @click="refresh">刷新</el-button>
</template>

<script>
export default {
  name: "RefreshButton",
  methods: {
    refresh() {
      this.$store.dispatch("setIsRouterRefresh");
      // 关闭详情页
      this.$store.dispatch("setShowDetailPage", false);
      // 修改面包屑
      this.$store.dispatch("setBreadcrumb", [this.$route.meta.title]);
      // this.$store.dispatch('setIsRouterRefresh', false)
      // this.$nextTick(() => {
      //   this.$store.dispatch('setIsRouterRefresh', true)
      //   console.log('reload')
      // })
    }
  }
};
</script>

<style scoped>

</style>
