<template></template>
<script>
import SSNContent from "@/components/serverNotice/s-s-n-content.vue";
import { getLawyerMainWaitServerVos } from "@/api/business";
import { serverStartLock } from "@/api/common";
import { getToken } from "utils/storage";
import { toImPage } from "utils/business-methods";
import Notification from "components/notification/index";
export default {
  name: "Notification",
  data() {
    return {
      notice: null,
      list: []
    };
  },
  mounted() {
    this.getLawyerMainWaitServerVos();
    this.timer = setInterval(() => {
      if (!getToken()) {
        clearInterval(this.timer);
        return;
      }
      this.getLawyerMainWaitServerVos();
    }, 1000 * 60);
    this.$on("hook:beforeDestroy", () => {
      this.notice && this.notice.close();
      clearInterval(this.timer);
    });
  },
  methods: {
    async getLawyerMainWaitServerVos() {
      await getLawyerMainWaitServerVos().then((data = []) => {
        this.list = data;
      });
      this.$nextTick(() => {
        this.showNotification();
      });
    },
    showNotification() {
      const item = this.list.shift();
      if (!item) return;
      const h = this.$createElement;
      this.notice = Notification({
        message: h(SSNContent, {
          props: {
            message: item.info
          },
          on: {
            startService: async() => {
              const res = await serverStartLock(item.caseSourceServerV2Id).finally(() => {
                this.notice.close();
              });
              this.$emit("startService", res);
              this.$nextTick(() => {
                this.showNotification();
              });
              toImPage(res.imSessionId);
            },
            intoIm: async() => {
              const res = await serverStartLock(item.caseSourceServerV2Id).finally(() => {
                this.notice.close();
              });
              toImPage(res.imSessionId);
            }
          }
        })
      });
    }
  }
};
</script>

<style scoped>

</style>
