
<template>
  <div class="addContent inline">
      <app-button   @click="openDialog"   icon="icon-zengjia">
        <slot />
      </app-button>
  </div>
</template>

<script>
import AppButton from "components/appButton/index.vue";
import buttonPermissionsEnum from "@/enum/buttonPermissionsEnum";
export default {
  name: "AddContent",
  components: {
    AppButton
  },
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    typeName: {
      type: String,
      default: "success"
    },
    params: {
      type: Object,
      default: () => {}
    },
    size: {
      type: String,
      default: "mini"
    },
    request: {
      type: Object,
      default: () => {
        return {
          deleteUrl: () => Promise.resolve()
        };
      }
    }
  },
  data() {
    return {
      dialogVisible: false,
      option: { title: "新增" },
      form: { },
      lists: [], // 列表数据
      rules: {}, // 校验规则
      filed: buttonPermissionsEnum.ADD,
      dialogVisibleAdd: false
    };
  },
  mounted() {

  },
  created() {
    // this.getData()
  },
  methods: {
    // 数据的过滤 ,form表单生成
    getData() {
      this.lists = this.tableData.filter(res => !res.formHidden);
      this.lists.map(reg => {
        if (reg.prop) { this.$set(this.form, reg.prop, ""); }
        if (reg.requireRule) {
          const obj = {};
          obj[reg.prop] = reg.requireRule;
          this.rules = Object.assign({}, this.rules, obj);
        }
      });
      // console.log(this.lists, this.rules)
    },
    //   打开弹窗
    openDialog() {
      this.$emit("click");
      // this.dialogVisible = true
    },
    //   取消
    cancel() {
      this.dialogVisible = false;
    },
    //   确定
    onSubmit(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          alert("submit!");
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    }
  }
};
</script>

<style lang="scss" scoped>
  .footer-btn{
      position: absolute;
      bottom: 0;
      right: 15px;
  }
</style>
