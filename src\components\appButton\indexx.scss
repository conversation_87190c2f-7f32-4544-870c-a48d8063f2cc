$Theme: (
        button:(
                primary:(
                  /*主题色*/
                        themeColor: #3887F5,
                  /*文字颜色*/
                        color: white,
                  /*hover颜色*/
                        hover: #5AA1F7,
                  /*激活颜色*/
                        active: #2363CB,
                  /*禁止的主题颜色*/
                        disabled: #A0CFFB,
                  /*禁止的文字颜色*/
                        disabledColor: #FFFFFF,
                ),
                info:(
                  /*主题色*/
                        themeColor: #F5F5F7,
                  /*文字颜色*/
                        color: #333333,
                  /*hover颜色*/
                        hover: #EEEEEE,
                  /*激活颜色*/
                        active: #DDDDDD,
                  /*禁止的主题颜色*/
                        disabled: #F5F5F7,
                  /*禁止的文字颜色*/
                        disabledColor: #CCCCCC,
                ),
                success:(
                  /*主题色*/
                        themeColor: #22BF7E,
                  /*文字颜色*/
                        color: white,
                  /*hover颜色*/
                        hover: #22BF7E,
                  /*激活颜色*/
                        active: #22BF7E,
                  /*禁止的主题颜色*/
                        disabled: #A0CFFB,
                  /*禁止的文字颜色*/
                        disabledColor: #FFFFFF,
                ),
          /*活动按钮 宣传*/
                publicize:(
                  /*主题色*/
                        themeColor: #F78C3E,
                  /*文字颜色*/
                        color: #FFFFFF,
                  /*hover颜色*/
                        hover: #F9A45F,
                  /*激活颜色*/
                        active: #CC6626,
                  /*禁止的主题颜色*/
                        disabled: #FCD1A3,
                  /*禁止的文字颜色*/
                        disabledColor: #FFFFFF,
                )
        ),
        border:(
                primary:(
                  /*主题色*/
                        themeColor: #3887F5,
                  /*文字颜色*/
                        color: #3887F5,
                  /*hover颜色*/
                        hover: #5AA1F7,
                  /*激活颜色*/
                        active: #2363CB,
                  /*禁止的主题颜色*/
                        disabled: #A0CFFB,
                  /*禁止的文字颜色*/
                        disabledColor: #A0CFFB,
                ),
                danger:(
                  /*主题色*/
                        themeColor: #EB4738,
                  /*文字颜色*/
                        color: #EB4738,
                  /*hover颜色变量*/
                        hover: rgba(235, 71, 56, 0.8),
                  /*激活颜色*/
                        active: #EB4738,
                  /*禁止的主题颜色*/
                        disabled: #A0CFFB,
                  /*禁止的文字颜色*/
                        disabledColor: #FFFFFF,
                ),
                waring:(),
        ),
        text:(
                primary:(
                  /*文字颜色*/
                        color: #3887F5,
                  /*hover颜色*/
                        hover: #5AA1F7,
                  /*激活颜色*/
                        active: #2363CB,
                  /*禁止的主题颜色*/
                        disabled: #A0CFFB,
                  /*禁止的文字颜色*/
                        disabledColor: #999999,
                ),
                success:(
                  /*文字颜色*/
                        color: #22BF7E,
                  /*hover颜色*/
                        hover: #22BF7E,
                  /*激活颜色*/
                        active: #22BF7E,
                  /*禁止的主题颜色*/
                        disabled: #A0CFFB,
                  /*禁止的文字颜色*/
                        disabledColor: #999999,
                ),
                danger:(
                  /*文字颜色*/
                        color: #EB4738,
                  /*hover颜色*/
                        hover: #EB4738,
                  /*激活颜色*/
                        active: #EB4738,
                  /*禁止的主题颜色*/
                        disabled: #A0CFFB,
                  /*禁止的文字颜色*/
                        disabledColor: #999999,
                ),
                info:(
                  /*文字颜色*/
                        color: #999999,
                  /*hover颜色*/
                        hover: #999999,
                  /*激活颜色*/
                        active: #999999,
                  /*禁止的主题颜色*/
                        disabled: #999999,
                  /*禁止的文字颜色*/
                        disabledColor: #999999,
                ),
                publicize:(
                  /*文字颜色*/
                        color: #333333,
                  /*hover颜色*/
                        hover: #333333,
                  /*激活颜色*/
                        active: #333333,
                  /*禁止的主题颜色*/
                        disabled: #999999,
                  /*禁止的文字颜色*/
                        disabledColor: #999999,
                ),
                warning:(
                  /*文字颜色*/
                        color: #F5992C,
                  /*hover颜色*/
                        hover: #F5992C,
                  /*激活颜色*/
                        active: #F5992C,
                  /*禁止的主题颜色*/
                  /*禁止的主题颜色*/
                        disabled: #999999,
                  /*禁止的文字颜色*/
                        disabledColor: #999999,
                )
        )
);
@mixin button($value1) {
  background: map-get($value1, themeColor);
  color: map-get($value1, color);
  &:hover {
    background: map-get($value1, hover);
  }
  &:active {
    background: map-get($value1, active);
  }
  &--disabled {
    &,
    &:hover,
    &:active {
      background: map-get($value1, disabled);
      color: map-get($value1, disabledColor);
      @extend .cursor-no-drop;
    }
  }
}

@mixin border($value1) {
  border: 1px solid map-get($value1, themeColor);
  color: map-get($value1, themeColor);
  &:hover {
    border-color: map-get($value1, hover);
    color: map-get($value1, hover);
  }
  &:active {
    border-color: map-get($value1, active);
    color: map-get($value1, active);
  }
  &--disabled {
    &,
    &:hover,
    &:active {
      border-color: map-get($value1, disabled);
      color: map-get($value1, disabledColor);
      @extend .cursor-no-drop;
    }
  }
}

@mixin text($value1) {
  padding: 0 16px 0 0;
  color: map-get($value1, color);
  &:hover {
    color: map-get($value1, hover);
  }
  &:active {
    color: map-get($value1, active);
  }
  &--disabled {
    &,
    &:hover,
    &:active {
      color: map-get($value1, disabledColor);
      @extend .cursor-no-drop;
    }
  }
}


@mixin generateButton() {
  @each $label, $value in $Theme {
    &_#{$label} {
      @each $label1, $value1 in $value {
        &_#{$label1} {
          @if $label == 'button' {
            @include button($value1);
          }
          @if $label == 'border' {
            @include border($value1);
          }
          @if $label == 'text' {
            @include text($value1);
          }
        }
      }
    }
  }
}
