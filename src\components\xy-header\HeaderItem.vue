<template>
	<div class="flex items-center justify-center flex-col cursor-pointer">
    <i class="text-[18px] iconfont" :class="[data.iconClass]" />
<!--    <div class="mt-[2px] text-[12px] text-[#666666]">{{data.title}}</div>-->
  </div>
</template>
<script>
export default {
  name: "HeaderItem",
  props: {
    data: {
      type: Object,
      default: () => ({})
    },
  },
};
</script>
<style scoped lang="scss"></style>
