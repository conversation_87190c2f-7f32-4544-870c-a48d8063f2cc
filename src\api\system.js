
import { request } from "utils/axios";

/* 人员配置管理*/
export const lcInstitutionStaffList = (params) => request.post("/law-cloud/lcInstitutionStaff/list", params);// 列表
export const lcInstitutionStaffInsert = (params) => request.post("/law-cloud/lc/lcInstitutionStaff/insert", params);// 新增人员
export const lcInstitutionStaffUpdate = (params) => request.post("/law-cloud/lc/lcInstitutionStaff/update", params);// 编辑人员
export const lcInstitutionStaffUpdateStatus = (params) => request.post("/law-cloud/lc/lcInstitutionStaff/updateStatus", params);// 禁用/启用人员
export const lcInstitutionStaffPage = (params) => request.post("/law-cloud/lc/lcInstitutionStaff/page", params);// 人员分页查询
export const lcInstitutionStaffGetStaffPhoneById = (params) => request.post("/law-cloud/lc/lcInstitutionStaff/getStaffPhoneById", params);// 根据人员id查询手机号
export const lcInstitutionStaffPermissionConfig = (params) => request.post("/law-cloud/lc/lcInstitutionStaff/permissionConfig", params);// 人员列表-权限配置
export const lcInstitutionStaffDetailByStaffId = (params) => request.post("/law-cloud/lc/lcInstitutionStaff/detailByStaffId", params);// 人员列表-根据人员id查询详情人员列表-权限配置
export const sysModuleSelectByStaffId = (params) => request.post("/law-cloud/lc/sys/module/selectByStaffId", params);// 律客云-根据员工id查询系统菜单信息
export const sysModuleSelectAllList = (params) => request.post("/law-cloud/lc/sys/module/selectAllList", params);// 律客云-查询所有系统菜单信息


/* 机构信息*/
export const lcLawFirmsInstitutionDetail = (params) => request.post("/law-cloud/lcLawFirmsInstitution/detail", params); // 详情
export const lcLawFirmsInstitutionAccountInfo = (params) => request.post("/law-cloud/lcLawFirmsInstitution/accountInfo", params); // 机构详情-账户信息
export const lcLawFirmsInstitutionPageServerInfo = (params) => request.post("/law-cloud/lcLawFirmsInstitution/pageServerInfo", params); // 机构详情-服务消耗明细-分页查询
export const lcLawFirmsInstitutionPageServerInfoExport = (params) => request.post("/law-cloud/lcLawFirmsInstitution/pageServerInfo/export", params); // 机构详情-服务消耗明细-导出
export const lcLawFirmsTransferRecord = (params) => request.post("/law-cloud/lcLawFirmsTransferRecord/page", params); // 机构详情-线索跟进记录-分页查询
export const lcLawFirmsTransferRecordExport = (params) => request.post("/law-cloud/lcLawFirmsTransferRecord/export", params); // 机构详情-线索跟进记录-导出

/** 律师事务所机构订单-分页查询 */
export const lcLawFirmsInstitutionOrderPage = (params) => request.post("/law-cloud/lcLawFirmsInstitutionOrder/page", params);
export const lcLawFirmsInstitutionOrderExport = (params) => request.post("/law-cloud/lcLawFirmsInstitutionOrder/export", params); // 导出


/* 查询系统使用人员数和可添加上限数*/
export const lcInstitutionStaffGetInstitutionStaffInfo = (params) => request.post("/law-cloud/lc/lcInstitutionStaff/getInstitutionStaffInfo", params); // 导出

/* 修改员工按钮权限，根据菜单id*/
export const updateStaffModuleFuncById = (params) => request.post("/law-cloud/lc/sys/module/updateStaffModuleFuncById", params); // 导出

/* 获取菜单页面按钮权限功能*/
export const sysModuleFunc = (params) => request.post("/law-cloud/lc/sys/module/sysModuleFunc", params); // 导出

/* 律师事务所机构信息-机构详情-法临币账户信息*/
export const lcLawFirmsInstitutionFlbAccountInfo = (params) => request.post("/law-cloud/lcLawFirmsInstitution/flbAccountInfo", params); // 导出

/* 律师事务所机构法临币订单明细-分页查询*/
export const lcLawFirmsInstitutionFlbAccountInfoPage = (params) => request.post("/law-cloud/lcLawFirmsInstitution/flbAccountInfo/page", params); // 导出

/* 律师事务所机构法临币订单明细导出*/
export const lcLawFirmsInstitutionFlbAccountInfoPageExport = (params) => request.post("/law-cloud/lcLawFirmsInstitution/flbAccountInfo/export", params); // 导出


