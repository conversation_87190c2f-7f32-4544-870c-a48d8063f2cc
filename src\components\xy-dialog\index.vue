<template>
  <el-dialog
    v-el-drag-dialog="draggable"
    :append-to-body="options.appendToBody|| true"
    :before-close="cancel"
    :center="options.center||false"
    :class="'default-dialog'"
    :close-on-click-modal="options.closeOnClickModal || false"
    :close-on-press-escape="options.closeOnPressEscape || false"
    :destroy-on-close="true"
    :lock-scroll="options.lockScroll||true"
    :show-close="typeof (options.showClose) === 'boolean' ? options.showClose : true"
    :title="options.title||''"
    :top="options.top || '15vh'"
    :visible.sync="dialogVisible"
    :width="options.width||'400px'"
    @close="cancel"
  >
    <slot />
    <template v-if="showFooter" slot="footer">
      <slot name="footer">
        <div slot="footer" class="dialog-footer">
          <el-button v-if="cancelButtonState" class="btn cancel" @click="cancel">{{cancelButtonText}}</el-button>
          <el-button v-if="sureButtonState" v-debounce="sure" class="btn" style="margin-left: 15px" type="primary">{{sureButtonText}}</el-button>
        </div>
      </slot>
    </template>
  </el-dialog>
</template>

<script>
import elDragDialog from "@/directive/el-dragDialog";
import debounce from "@/directive/debounce/debounce";

export default {
  name: "XyDialog",
  directives: { elDragDialog, debounce },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    options: {
      type: Object,
      default: () => {
        return {
          title: "详情",
          width: "30%"
        };
      }
    },
    showFooter: {
      type: Boolean,
      default: true
    },
    cancelButtonState: {
      type: Boolean,
      default: true
    },
    sureButtonState: {
      type: Boolean,
      default: true
    },
    cancelButtonText: {
      type: String,
      default: "取 消"
    },
    sureButtonText: {
      type: String,
      default: "确 定"
    },
    draggable: {
      type: Boolean,
      default: true,
      desc: "是否可以拖拽弹框"
    }
  },
  data() {
    return {};
  },
  destroyed() {
    this.$emit("close");
  },
  mounted() {
    // this.$emit('open')
  },
  methods: {
    cancel(rel) {
      this.$emit("close");
    },
    sure() {
      this.$emit("sure");
    }
  }
};
</script>

<style lang="scss" scoped>
</style>
