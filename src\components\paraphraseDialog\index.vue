
<template>
  <xyDialog v-if="dialogVisible" :dialog-visible="dialogVisible" :options="options" draggable @close="close" :show-footer="false">
    <div class="box">
      <dl v-for="(item,index) in descData" :key="index"  :class="`${activeIdx === index ? 'active':''}`">
        <dt>{{ item.title }}</dt>
        <dd v-for="(inner,i) in item.values" :key="i">{{ `${i+1}、${inner}` }}</dd>
      </dl>
    </div>
  </xyDialog>
</template>

<script>
import xyDialog from "@/components/xy-dialog";
export default {
  name: "ParaphraseDialog",
  components: {
    xyDialog
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    options: {
      type: Object,
      default: () => {
        return {
          title: "字段释义",
          width: "45%"
        };
      }
    },
    activeIdx: {
      type: [Number, String],
      default: 0
    },
    descData: {
      type: Array,
      default: () => []
    }
  },
  methods: {

    close() {
      this.$emit("update:dialogVisible", false);
    }
  }
};
</script>

<style lang="scss" scoped>
.box{
  padding-bottom: 30px;
  max-height: 600px;
  overflow-y: auto;
}
dl{
  margin-bottom: 30px;
  border: 1px solid transparent;
  border-radius: 2px;
  dt{
    margin-bottom: 10px;
    padding-left: $distance-normal;
    line-height: 44px;
    background: #f8f8f8;
    color: $text-color-secondary;
    font-weight: 600;

  }
  dd{
    padding-left: $distance-normal;
    line-height: 30px;
    color: $text-color-desc;
  }
  &.active{
    border: 1px dashed $primary-color;
    dt{
      color: $primary-color;
    }
  }
}
</style>
