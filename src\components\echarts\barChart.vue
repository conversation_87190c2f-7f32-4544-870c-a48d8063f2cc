<template>
  <div :id="id" :class="className" :style="{height:height,width:width}" />

</template>

<script>
import echarts from "echarts";
import resize from "@/minixs/resize";

export default {
  name: "Bar<PERSON>hart",
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart"
    },
    name: [String],
    id: {
      type: String,
      default: "chart"
    },
    width: {
      type: String,
      default: "100%"
    },
    height: {
      type: String,
      default: "500px"
    },
    barData: {
      type: Array,
      default: () => []
    },
    labelTags: {
      type: Object,
      default: () => {}
    },
    xAxisData: {
      type: Object,
      default: () => {
        return {
          type: "value",
          data: []
        };
      }
    },
    yAxisData: {
      type: Object,
      default: () => {
        return {
          type: "category",
          data: []
        };
      }
    },
    legendData: {
      type: Array,
      default: () => [""]
    }
  },
  data() {
    return {
      chart: null,
      seriesData: [],
      color: ["#58A3F7", "#FFC542", "#FF7474", "#50B5FF", "#8167F5", "#6e7074", "#546570", "#c4ccd3"]
    };
  },
  watch: {
    barData: {
      handler(val) {
        this.initChart();
      },
      deep: true
    }
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },

  methods: {
    // 初始化渲染图表
    initChart() {
      this.seriesData = [];
      this.chart = echarts.init(document.getElementById(this.id));

      for (let i = 0; i < this.legendData.length; i++) {
        // console.log(this.barData[i], '值')
        this.seriesData.push({
          name: this.legendData[i],
          type: "bar",
          barMaxWidth: 35,
          lineStyle: {
            normal: {
              width: 2
            }
          },
          label: {
            ...this.labelTags,
            textStyle: {
              color: "#333"
            }
          },
          itemStyle: {
            normal: {
              color: this.color[parseInt(Math.random() * 10, 10)]
            //   label: {
            //     show: true,
            //     textStyle: {
            //       color: '#fff'
            //     },
            //     position: 'insideTop',
            //     formatter(p) {
            //       return p.value > 0 ? p.value : ''
            //     }
            //   }
            }

          },
          data: this.barData[i]
        });
      }

      this.chart.setOption({
        tooltip: {
          trigger: "axis",
          axisPointer: {
            textStyle: {
              color: "#fff"
            }
          }
        },
        legend: {
          x: "5%",
          top: "10%",
          data: this.legendData,
          textStyle: {
            fontSize: 12,
            color: "#999"
          }
        },
        grid: {
          left: "3%",
          right: "4%",
          bottom: "3%",
          containLabel: true
        },
        xAxis: {
          ...this.xAxisData,
          boundaryGap: [0, 0.01]
        },
        yAxis: {
          ...this.yAxisData
        },
        series: this.seriesData

      }, true);
    }
  }
};
</script>
