#引入其他项目预先定义的公共模板
include:
  - project: all/flzx/cicd-devops/common-template
    ref: master
    file:
      - 'pro/imlaw-front-for-k8s.yml'


#定义执行步骤
stages:
  - npmBuild
  - dockerBuild
  - prek8sDeploy
  - k8sDeploy

#第一步 npm打包构建
npmBuild:
  stage: npmBuild
  environment: pro
  rules:
    - if: '$CI_COMMIT_REF_NAME == "master"'
  before_script:
    - |- 
       rm -rf dist   #清除上次构建的缓存,避免出现未知问题
       #判断是否传入变量
       if [ -z "${BuildVersion}" ];then
          echo "未输入版本号,pipline需要传入变量,比如buildversion 版本号: 1.0.9"
          exit 555
        fi

       #判断是否符合版本号定义
       regex="[1-9].[0-9].[0-9]"
       if [[ ${BuildVersion} =~ $regex ]];then
         echo "传入版本号值为:${BuildVersion},符合版本号规则"
       else          
          echo "你传入版本号值为:${BuildVersion},不符合版本定义规则,示例版本号比如: 1.0.9"
          exit 555
       fi  

       jsonTxt=`cat package.json|grep "build:prod"`
       echo "文件匹配到的是： $jsonTxt"
       envTxt=`echo $jsonTxt|sed "s/version=.*/version=${BuildVersion}\"\,/1"`
       echo "envTxt: $envTxt"
       sed -i "s/${jsonTxt}/${envTxt}/g" package.json  
       echo "package.json 最终构建环境和版本号打印如下:"
       cat package.json|grep "build:prod"
       sed -i "s/${ServiceName}:.*/${ServiceName}:${BuildVersion}/1" /home/<USER>/builds/.version/prod-web.env
       
  variables:
    #DEPOLY_ENVIRONMENT: "pro"
    # NPM_INSTALL_CMD: "npm install --unsafe-perm"
    NPM_INSTALL_CMD: "npm install --unsafe-perm --registry https://registry.npmjs.org"
    BUILD_CMD: "npm run build:prod"
  extends: .front-pro-build
  #when: manual

  #缓存构建文件夹              
  cache:
    # untracked: true
    paths:
    - node_modules/
    - dist/ 
  #when: manual

#第二步docker镜像构建
dockerBuild:
  stage: dockerBuild
  needs: [npmBuild]
  environment: pro
  rules:
    - if: '$CI_COMMIT_REF_NAME == "master"'
  extends: .front-pro-dockerBuild
  #when: manual

#获取上一步缓存的构建文件夹
  cache:
    paths:
      - dist/ 
    policy: pull



#第三步 预发布k8s文件部署，增加打包发布的验证，避免出错
prek8sDeploy:
  stage: prek8sDeploy
  needs: [dockerBuild]
  environment: pre
  variables:
    DEPLOY_ENV: "预发布"
  rules:
    - if: '$CI_COMMIT_REF_NAME == "master"'
  extends: .front-pre-k8sDeploy

#第四步 生产k8s文件部署
k8sDeploy:
  stage: k8sDeploy
  needs: [dockerBuild,prek8sDeploy]
  environment: pro
  rules:
    - if: '$CI_COMMIT_REF_NAME == "master"'
  extends: .front-pro-k8sDeploy
  # when: manual
