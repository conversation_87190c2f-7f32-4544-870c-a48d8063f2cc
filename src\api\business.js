import { justRequest, request } from "utils/axios";

// 案源列表  -补充信息
export const caseRecordPagesMore = (params) => request.post("/law-cloud/caseSourceV2/supplement/problems", params);

// 获取待服务弹窗信息
export const getLawyerMainWaitServerVos = (params) => request.post("/law-cloud/caseSourceServerV2/getLawyerMainWaitServerVos", params);

// 转案源
export const toCaseSource = (params) => request.post("/law-cloud/caseSourceServerV2/toCaseSource", params);

// 自营律师服务看板-今日概览 维度：天
export const selfServiceOverview = (params) => request.post("/law-cloud/statistics2bLawyerSelfService/overview", params);

// 明细-自营律师服务看板-分页查询 维度：天
export const selfServicePageByDay = (params) => request.post("/law-cloud/statistics2bLawyerSelfService/pageByDay", params);

// 明细-自营律师服务看板-导出 维度：天
export const selfServiceExportByDay = (params) => request.post("/law-cloud/statistics2bLawyerSelfService/exportByDay", params);

// 汇总-自营律师服务看板-分页查询 维度：审核人
export const selfServicePageByLawyer = (params) => request.post("/law-cloud/statistics2bLawyerSelfService/pageByLawyer", params);
// 汇总-自营律师服务看板-导出 维度：审核人
export const selfServiceExportByLawyer = (params) => request.post("/law-cloud/statistics2bLawyerSelfService/exportByLawyer", params);

// 进行外呼
export const lawyerCallOut = (params) => request.post("/law-cloud/lawyerCall/callOut", params);

// 挂断外呼
export const lawyerCallHandUp = (recordId) => request.post("/law-cloud/lawyerCall/handUp/" + recordId);

// 外呼记录弹窗
export const lawyerDialrecords = (params) => request.post("/law-cloud/lawyerDial/records", params);
// 外呼记录分页查询
export const lawyerCallrecords = (params) => request.post("/law-cloud/lawyerCall/records", params);

// 自营律师服务外呼看板-分页查询 维度：天
export const lawyerCallpageByDay = (params) => request.post("/law-cloud/statistics2bLawyerCall/pageByDay", params);

// 自营律师服务外呼看板-导出 维度：天
export const lawyerCallexportByDay = (params) => request.post("/law-cloud/statistics2bLawyerCall/exportByDay", params);

// 自营律师服务外呼看板-分页查询 维度：审核人
export const lawyerCallpageByLawyer = (params) => request.post("/law-cloud/statistics2bLawyerCall/pageByLawyer", params);

// 自营律师服务外呼看板-导出 维度：天
export const lawyerCallexportByLawyer = (params) => request.post("/law-cloud/statistics2bLawyerCall/exportByLawyer", params);

// 录音下载
export const lawyerCalldownload = (params) => justRequest.get("/law-cloud/lawyerCall/url/download", { params, responseType: "blob" });

// 问答列表
export const qaMessagePageList = (params) => request.post("/info/qaMessage/pageList", params);

// 问答列表-我的违规
export const deregulationPageList = (params) => request.post("/paralegal/caseSourceServerV2/deregulation/pageList", params);

// 问答列表-问答抢答
export const qaMessageRushAnswer = (params) => request.post("/info/qaMessage/rushAnswer", params);

// 问答列表-问答抢答(新)
export const qaMessageRushAnswerV2 = (params) => request.post("/info/qaMessage/rushAnswer/v2", params);
