import WS, { WS_STATE } from "utils/ws.js";
import { getToken } from "utils/storage.js";
import basics from "utils/basicsMethods.js";
import EventBus from "utils/wsEventBus.js";
import { compareVersions, getHttpSystemVersion } from "components/versionPromptPopup";

const systemVersion = getHttpSystemVersion();

let ws  = {
  close(){

  }
};


/**/
export const createWs = () => {
  console.log("createWs");
  const token = getToken();
  if(!token){
    systemVersion.start();
    ws.close();
    return;
  }
  /* 没有打开ws 或者ws状态关闭在链接*/
  /* 防止多次调用*/
  if (basics.isNull(ws.getWsState) || (ws.getWsState() === WS_STATE.CLOSED)) {
    console.log("createWs ing");
    ws = new WS({
      onmessage(data) {
        EventBus.$emit("wsMessage", data);
        console.log(data, "onmessage");
      },
      onopen() {
        console.log("onopen");
        /* im开启的时候关闭http请求的版本请求*/
        systemVersion.stop();
      },
      onclose() {
        /* 当im关闭时 开启http版本请求*/
        // systemVersion.start();
        console.log("close");
      },
      onallmessage(data) {
        if(basics.isObj(data) && !basics.isNull(data.pcVersion)){
          compareVersions(data.pcVersion);
        }
      }
    }).connect(process.env.VUE_APP_BASE_WS_URL + `/message/push/${token}`);
  }
};

export const getWs = () => {
  return ws;
};

export const wsClose = () => {
  try {
    ws.close();
  }catch (e) {
    console.log(e);
  }
};
