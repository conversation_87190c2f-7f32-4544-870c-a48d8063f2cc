<template>
	<div>
    <!-- :active="activeValue[valueKey] === item[valueKey]" -->
		<ul class="flex flex-wrap gap-[8px]">
			<li v-for="item in selectList" :key="item[valueKey]">
				<app-select-item
					:label="labelKey"
					:value="valueKey"
					:data="item"

          :active="checkedList.some(i => i[valueKey] === item[valueKey])"
					@click="handleSelect(item)"
				/>
			</li>
		</ul>
	</div>
</template>

<script>
import AppSelectItem from "components/AppSelect/AppSelectItem.vue";

export default {
  name: "AppSelect",
  components: { AppSelectItem },
  props: {
    selectList: {
      type: Array,
      default: () => [],
      required: true,
    },
    /** 展示的字段名 */
    labelKey: {
      type: String,
      default: "label",
    },
    /** 展示的字段值 */
    valueKey: {
      type: String,
      default: "value",
    },
    /** 默认值 */
    defaultValue: {
      type: [String, Number, Object, Array],
      default: undefined,
    },
    /** 是否单选 */
    typeWay: {
      type: String,
      default: "single",
    },
  },
  data() {
    return {
      /** 当前选择的值 */
      activeValue: {},
      checkedList: [],
    };
  },
  mounted() {
    this.initValue();
  },
  methods: {
    /** 初始值赋值 */
    initValue() {
      const val = this.defaultValue;
      console.log(val, 1);

      // 如果是Object
      if (this.basics.isObj(val)) {
        // this.activeValue = val;
        this.checkedList = [val];
        return;
      }



      // 如果是String或者Number
      // this.activeValue = {
      //   [this.valueKey]: val,
      // };

      // 如果是String或者Number，或者是数组
      const arr = this.basics.isArray(val) ? val : (this.basics.isString(val) ? (val.split(",")) : [val]);
      this.checkedList = arr.map(item => {
        return {
          [this.valueKey]: item,
        };
      });

    },
    handleSelect(item) {
      // 如果是单选
      if (this.typeWay === "single") {
        this.checkedList = [item];
      }else {
        // 如果是多选
        const index = this.checkedList.findIndex(
          (i) => i[this.valueKey] === item[this.valueKey]
        );
        if(index !== -1) {
          // 如果已经选中，则取消选中
          this.checkedList.splice(index, 1);
        }else {
          if(item[this.valueKey] === "") {
            // 如果选中的是全部，则清空其他选项
            this.checkedList = [];
          }
          // 如果选中的是其他选项，然后需要将全部取消选中
          if(this.checkedList.some(i => i[this.valueKey] === "")) {
            this.checkedList.splice(this.checkedList.findIndex(i => i[this.valueKey] === ""), 1);
          }
          this.checkedList.push(item);

        }
      }
      // this.activeValue = item;
      this.$emit("change", this.typeWay === "single" ? item : this.checkedList);





    },
  },
};
</script>

<style scoped lang="scss"></style>
