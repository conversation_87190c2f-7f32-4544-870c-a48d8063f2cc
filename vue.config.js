// vue.config.js
const path = require("path");
// const BundleAnalyzerPlugin = require('webpack-bundle-analyzer').BundleAnalyzerPlugin // 打包分析插件

function resolve(dir) {
  return path.join(__dirname, dir);
}
/* 动态命令打包 注入全局常量 VUE_APP+动态注入的key获取*/
const argv = process.argv;

/* 正则匹配 --$0=$1的格式 并获取$0 $1*/
function getReg(str) {
  return str.match(/--(.*?)=(.*)/);
}
argv.forEach(item => {
  const reg = getReg(item);
  if(reg){
    console.log("匹配的指令======" + JSON.stringify(reg));
    const key = `VUE_APP_${reg[1].toUpperCase()}`;
    /* 将$0驼峰并加上VUE_APP关键字注入*/
    process.env[key] = reg[2];
    console.log("生成的自定义打包命令=====key====" + key + "====value====" + reg[2]);
  }
});
module.exports = {
  // 部署应用包时的基本URL，默认为'/'
  // 假设你的应用将会部署在域名的根部,比如，https://www.vue-cli.com/,则设置为"/"
  // 如果你的应用是部署在一个子路径下，那么你需要在这里指定子路径，比如，如果你部署在 https://www.my-vue.com/my-app/; 那么将这个值改为 “/my-app/”
  publicPath: "/",

  // 将构建好的文件输出到哪里 当运行 vue-cli-service build 时生成的生产环境构建文件的目录。注意目标目录在构建之前会被清除 (构建时传入 --no-clean 可关闭该行为)。
  outputDir: "dist",

  // 放置生成的静态资源(js、css、img、fonts)的目录。
  assetsDir: "",

  // 指定生成的 index.html 的输出路径
  indexPath: "index.html",

  // 默认情况下，生成的静态资源在它们的文件名中包含了 hash 以便更好的控制缓存。然而，这也要求 index 的 HTML 是被 Vue CLI 自动生成的。如果你无法使用 Vue CLI 生成的 index HTML，你可以通过将这个选项设为 false 来关闭文件名哈希
  filenameHashing: true,

  // 是否在开发环境下通过 eslint-loader 在每次保存时 lint 代码。这个值会在 @vue/cli-plugin-eslint 被安装之后生效。
  // 设置为 true 时，eslint-loader 会将 lint 错误输出为编译警告。默认情况下，警告仅仅会被输出到命令行，且不会使得编译失败。
  // 如果你希望让 lint 错误在开发时直接显示在浏览器中，你可以使用 lintOnSave: 'error'。这会强制 eslint-loader 将 lint 错误输出为编译错误，同时也意味着 lint 错误将会导致编译失败。
  lintOnSave: false,

  // 是否使用包含运行时编译器的 Vue 构建版本。设置为 true 后你就可以在 Vue 组件中使用 template 选项了，但是这会让你的应用额外增加 10kb 左右。
  runtimeCompiler: false,

  // 默认情况下 babel-loader 会忽略所有 node_modules 中的文件。如果你想要通过 Babel 显式转译一个依赖，可以在这个选项中列出来。
  transpileDependencies: [],

  // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
  productionSourceMap: false,

  // 是一个函数，会接收一个基于 webpack-chain 的 ChainableConfig 实例。允许对内部的 webpack 配置进行更细粒度的修改。
  chainWebpack: (config) => {
    // 配置别名
    config.resolve.alias
      .set("@", resolve("src"))
      .set("assets", resolve("src/assets"))
      .set("components", resolve("src/components"))
      .set("router", resolve("src/router"))
      .set("utils", resolve("src/utils"))
      .set("store", resolve("src/store"))
      .set("views", resolve("src/views"));

    if (process.env.VUE_APP_EVN_CONFIG === "analyze") {
      config
        .plugin("webpack-bundle-analyzer")
        .use(require("webpack-bundle-analyzer").BundleAnalyzerPlugin);
    }
    // 移除 prefetch 插件
    config.plugins.delete("prefetch");
  },

  // 是否为 Babel 或 TypeScript 使用 thread-loader。该选项在系统的 CPU 有多于一个内核时自动启用，仅作用于生产构建。
  parallel: require("os").cpus().length > 1,

  // 向 PWA 插件传递选项。
  // https://github.com/vuejs/vue-cli/tree/dev/packages/%40vue/cli-plugin-pwa
  pwa: {},

  // 所有 webpack-dev-server 的选项都支持。注意：有些值像 host、port 和 https 可能会被命令行参数覆写。
  // 有些值像 publicPath 和 historyApiFallback 不应该被修改，因为它们需要和开发服务器的 publicPath 同步以保障正常的工作。
  // 代理配置
  devServer: {
    host: "0.0.0.0",
    port: 8080, // 端口号
    https: false, // https:{type:Boolean}
    open: true // 配置自动启动浏览器  open: 'Google Chrome'-默认启动谷歌

    // 配置单个代理
    // proxy: 'http://localhost:9000'

    // 配置多个代理-(使用，后台已做跨域处理)
    // proxy: {
    //   '/api': {
    //     // target: "https://*********:8080", // 目标主机
    //     target: 'http://*************:18081',
    //     ws: true, // 代理的WebSockets
    //     changeOrigin: true, // 允许websockets跨域
    //     pathRewrite: {
    //       '^/api': ''
    //     }
    //   }
    // }
  },

  // 第三方插件选项
  // 这是一个不进行任何 schema 验证的对象，因此它可以用来传递任何第三方插件选项。
  pluginOptions: {
    foo: {
      // 插件可以作为 `options.pluginOptions.foo` 访问这些选项。
    }
  },

  // 有的时候你想要向 webpack 的预处理器 loader 传递选项。你可以使用 vue.config.js 中的 css.loaderOptions 选项。比如你可以这样向所有 Sass/Less 样式传入共享的全局变量
  css: {
    extract: {
      ignoreOrder: true
    },
    loaderOptions: {
      // 给 sass-loader 传递选项
      sass: {
        prependData: `
                @import "@/assets/css/common.scss";
                @import "@/assets/css/mixin.scss";
                @import "@/assets/css/reset.scss";
                @import "@/assets/css/var.scss";
                @import "@/assets/css/element-ui.scss";
                `
      },
    }
  },
  // 配置webpack打包
  configureWebpack: (config) => {
    console.log("===========" + process.env.VUE_APP_EVN_CONFIG, "process.env.VUE_APP_EVN_CONFIG");
    /* 测试环境和开发环境使用*/
    if (process.env.VUE_APP_EVN_CONFIG === "dev" || process.env.VUE_APP_EVN_CONFIG === "test") {
      config.devtool = "eval-source-map";
    }else{
      // 取消console打印
      config.optimization.minimizer[0].options.terserOptions.compress.drop_console = true;
    }

    // 防止将某些 import 的包(package)打包到 bundle 中，而是在运行时(runtime)再去从外部获取这些扩展依赖
    // config.externals = {
    //   vue: 'Vue',
    //   'vue-router': 'VueRouter',
    //   'element-ui': 'ELEMENT',
    //   vuex: 'Vuex',
    //   axios: 'axios'
    // }
  }
};
