<template>
	<div class="w-full h-[48px] bg-[#FFFFFF] flex items-center justify-between pr-[24px] box-border">
		<collapse-button />
		<div class="flex items-center">
			<ul class="flex items-center space-x-[24px] ">
				<li>
					<header-swipe v-if="!getIsSpecialCustomerService" />
				</li>
				<li
					v-for="item in iconList"
					:key="item.id"
					class="w-[20px] h-[20px] flex items-center justify-center"
				>
					<template v-if="item.id === 1">
						<el-tooltip
							popper-class="!bg-[rgba(0,0,0,0.7)] rounded-[8px]"
							manual
							v-model="pushStatus"
							class="item"
							effect="dark"
							placement="top-end"
						>
							<template #content>
								<p class="text-[14px] text-[#FFFFFF]">
									你有<span class="text-[#F2AF30]">1</span>条新的推送案源，点击查看推送记录
								</p>
							</template>
							<app-tooltip :content="item.guideText">
								<el-badge is-dot :hidden="casePushNum < 1" class="item badge">
									<div @click="openCase">
										<header-item :data="item" />
									</div>
								</el-badge>
							</app-tooltip>
						</el-tooltip>
					</template>
					<div class="popover mt-[-2px]" v-if="item.id === 2">
						<el-popover
							placement="bottom-end"
							width="462"
							v-model="notificationsState"
							popper-class="!p-[0px] rounded-[8px] [box-shadow:0px_0px_4px_0px_rgba(0,0,0,0.1)]"
							trigger="click"
						>
							<div>
								<message-notifications :state="notificationsState" />
							</div>
							<template #reference>
								<app-tooltip :content="item.guideText">
									<el-badge is-dot :hidden="!noticeUnread" class="item badge">
										<header-item :data="item" />
									</el-badge>
								</app-tooltip>
							</template>
						</el-popover>
					</div>
					<template v-if="item.id === 4">
						<app-tooltip :content="item.guideText">
							<div @click="logout">
								<header-item :data="item" />
							</div>
						</app-tooltip>
					</template>
					<template v-if="item.id === 3">
						<el-popover
							placement="bottom"
							width="260"
							popper-class="!p-[0px] rounded-[8px] [box-shadow:0px_0px_4px_0px_rgba(0,0,0,0.1)]"
							trigger="click"
						>
							<div class="w-[260px] box-border px-[24px] py-[16px]">
								<p class="font-bold text-[16px] text-[#333333] !pb-[10px]">律所信息</p>
								<div
									class=" space-y-[12px] pb-[16px] border-[0px] border-b-[1px] border-dotted border-[#DDDDDD]"
								>
									<div class="flex items-center">
										<p class="text-[14px] w-[75px] shrink-0 text-[#666666]">律所名称</p>
										<p class="text-[14px] text-[#333333]">{{ userInfo.institutionName }}</p>
									</div>
									<div class="flex items-center">
										<p class="text-[14px] w-[75px] shrink-0 text-[#666666]">管理员</p>
										<p class="text-[14px] text-[#333333]">{{ userInfo.managerName }}</p>
									</div>
									<div class="flex items-center">
										<p class="text-[14px] w-[75px] shrink-0 text-[#666666]">手机号</p>
										<p class="text-[14px] text-[#333333]">{{ userInfo.managerPhone }}</p>
									</div>
								</div>
								<p class="font-bold text-[16px] text-[#333333] !pb-[10px] !pt-[16px]">个人信息</p>
								<div class=" space-y-[12px]">
									<div class="flex items-center">
										<p class="text-[14px] w-[75px] shrink-0 text-[#666666]">员工姓名</p>
										<p class="text-[14px] text-[#333333]">{{ userInfo.userName }}</p>
										<div
											v-if="lawyerCertStatus"
											class="w-[48px] h-[21px] bg-[#22BF7E] rounded-[4px] box-border flex items-center justify-center ml-[8px]"
										>
											<div class="text-[12px] text-[#FFFFFF]">已实名</div>
										</div>
										<div
											v-else
											class="w-[48px] h-[21px] bg-[#EB4738] rounded-[4px] box-border flex items-center justify-center ml-[8px]"
										>
											<div class="text-[12px] text-[#FFFFFF]">未实名</div>
										</div>
									</div>
									<div class="flex items-center">
										<p class="text-[14px] w-[75px] shrink-0 text-[#666666]">岗位</p>
										<p class="text-[14px] text-[#333333]">{{ userInfo.jobTitle }}</p>
									</div>
									<div class="flex items-center">
										<p class="text-[14px] w-[75px] shrink-0 text-[#666666]">手机号</p>
										<p class="text-[14px] text-[#333333]">{{ userInfo.userPhone }}</p>
									</div>
								</div>
							</div>
							<template #reference>
								<app-tooltip :content="item.guideText">
									<header-item :data="item" />
								</app-tooltip>
							</template>
						</el-popover>
					</template>
					<template v-if="item.id === 5">
						<app-tooltip :content="item.guideText">
							<div @click="openGuide">
								<header-item :data="item" />
							</div>
						</app-tooltip>
					</template>
				</li>
			</ul>
		</div>
	</div>
</template>

<script>
import CollapseButton from "components/xy-treeMenu/components/CollapseButton.vue";
import HeaderItem from "components/xy-header/HeaderItem.vue";
import AppTooltip from "components/AppTooltip/index.vue";
import { caseSourceCluesPushNum, lcCluePlanV2PushRedDot } from "@/api/clues";
import MessageNotifications from "components/xy-header/messageNotifications.vue";
import EventBus from "utils/wsEventBus.js";
import { CMD } from "utils/ws.js";
import { lcInstitutionStaffUnread } from "@/api/common.js";
import HeaderSwipe from "components/xy-header/HeaderSwipe.vue";
import { mapGetters } from "vuex";
import { turnToCasePush } from "utils/turnPage";
import { getDictValueByOrgDescKey } from "utils/tools";

export default {
  name: "AppHeader",
  components: { HeaderSwipe, MessageNotifications, AppTooltip, HeaderItem, CollapseButton },
  data() {
    return {
      timer: null,
      /* 推送状态*/
      pushStatus: false,
      notificationsState: false,
      /* 消息通知类型 goodNewsUnreadNum 成单喜报 noticeUnreadNum 更新公告 sysNotificationNum系统消息*/
      noticeTypeList: ["goodNewsUnreadNum", "noticeUnreadNum", "sysNotificationNum"],
      getIsSpecialCustomerService: false
    };
  },
  computed: {
    ...mapGetters({
      lawyerCertStatus: "lawyerCertStatus"
    }),
    /* 通知未读状态*/
    noticeUnread() {
      return (
        this.noticeTypeList.reduce(
          (total, i) => total + this.$store.getters["getNoticeCounts"][i],
          0,
        ) > 0
      );
    },
    /* 案源推送数量*/
    casePushNum() {
      return this.$store.getters.getNoticeCounts.caseSourcePush;
    },
    userInfo() {
      return this.$store.getters.userInfo;
    },
    iconList() {
      return [
        {
          iconClass: "icon-xinshouyindao",
          title: "新手引导",
          guideText: "新手引导",
          id: 5,
        },
        {
          iconClass: "icon-zidongqianganyuan1",
          title: "案源推送",
          guideText: "案源推送",
          id: 1,
        },
        {
          iconClass: "icon-xiaoxi01",
          title: "消息通知",
          guideText: "消息通知",
          id: 2,
        },
        {
          iconClass: "icon-user",
          title: "账户信息",
          guideText: "账户信息",
          id: 3,
        },
        {
          iconClass: "icon-xianxing",
          title: "退出账号",
          guideText: "退出账号",
          id: 4,
        },
      ];
    },
  },
  created() {
    this.getCaseNum();
    this.getNoticeUnread();

    getDictValueByOrgDescKey({ key: "showType" }).then(data => {
      this.getIsSpecialCustomerService = data === 1;
    });
    /* 获取案源推送提示*/
    let timer = null;
    EventBus.$on("wsMessage", ({ cmd }) => {
      if (cmd === CMD.CASE_PUSH) {
        clearTimeout(timer);
        this.pushStatus = true;
        timer = setTimeout(() => {
          this.pushStatus = false;
          this.getLcCluePlanV2PushRedDot();
        }, 3000);
      }
    });
  },
  methods: {
    /* 获取消息未读数*/
    getNoticeUnread() {
      /* 用户未读消息数*/
      lcInstitutionStaffUnread().then(res => {
        /* 更新全局的消息未读数 */
        const keys = this.noticeTypeList;
        const data = {};
        keys.forEach(i => {
          if (i && !this.basics.isNull(res[i])) {
            data[i] = res[i] || 0;
          }
        });
        this.$store.commit("SET_NOTICE_COUNTS", data);
      });
    },
    /** 打开新人引导 */
    openGuide() {
      this.$store.commit("SET_GUIDE", true);
    },
    /** 点击退出登录 */
    logout() {
      this.$store.dispatch("loginOut");
    },
    openCase() {
      turnToCasePush();
    },
    getCaseNum() {
      caseSourceCluesPushNum().then(r => {
        // 弹出已经修改过案源推送计划
        if (Number(r.modifyStatus)) {
          this.$notify({
            dangerouslyUseHTMLString: true,
            showClose: false,
            offset: 45,
            customClass: "notify-box",
            message:
							"<p style=\"font-size: 14px;color: #333333;\"><i style=\"color: #FF851F;\" class=\"iconfont icon-xianxingtubiao-3\"></i> 管理员为您开启了新的案源城市，请重新开启案源自动推荐计划</p>",
          });
        }
        this.$store.commit("SET_CASE_PUSH_NUMBER", r.recommendCount);
      });
      // 推送数量
      this.getLcCluePlanV2PushRedDot();
    },
    getLcCluePlanV2PushRedDot() {
      // 推送数量 header上小红点
      lcCluePlanV2PushRedDot().then(data => {
        this.$store.commit("SET_NOTICE_COUNTS", {
          caseSourcePush: data.caseSourceNum || 0,
        });
      });
    },
  },
};
</script>

<style lang="scss">
.notify-box {
	width: 448px;
	height: 38px;
	background: #faf2e8;
	border-radius: 2px 2px 2px 2px;
	border: 1px solid #fcd1a3;
	line-height: 38px;
	padding: 0;
}
</style>
<style scoped lang="scss">
.case-num {
	width: 21px;
	height: 17px;
	background: #eb4738;
	border-radius: 8px 8px 8px 8px;
	border: 1px solid #ffffff;
	color: #ffffff;
	font-size: 12px;
	display: inline-block;
	position: absolute;
	font-style: normal;
	text-align: center;
	right: -4px;
	top: -5px;
}
.badge {
	::v-deep .el-badge__content {
		top: 4px;
		width: 6px;
		height: 6px;
	}
}
.popover {
	::v-deep {
		> span {
			display: block;
		}
	}
}
</style>
