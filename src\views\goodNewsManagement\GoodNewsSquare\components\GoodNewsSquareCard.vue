<template>
	<div class="flex">
		<img
			class="w-[14px] h-[164px] block mr-[10px] shrink-0"
			src="@/views/goodNewsManagement/GoodNewsSquare/img/Frame938.png"
			alt=""
		/>
		<div class="w-full">
			<div class="text-[14px] text-[#666666]">{{ data.pubTime }}</div>
			<div
				class="bg-[#F5F5F7] rounded-[4px] p-[24px] box-border h-[108px] cursor-pointer mt-[16px] box-border"
				@click="handleClick"
			>
				<div class="flex items-center">
					<img class="w-[60px] h-[60px] rounded-[4px] mr-[12px] shrink-0" alt="" :src="data.logo||require('@/views/goodNewsManagement/GoodNewsSquare/img/defalut_logo.png')" />
					<div class="text-[16px] text-[#333333] w-[408px] mr-[24px] shrink-0 whitespace-pre-wrap">
						{{ content }}
					</div>
					<div class="grid grid-cols-2 w-full">
						<div v-for="item in list" :key="item.field" class="flex items-center">
							<div class="text-[14px] text-[#666666]">{{ item.title }}</div>
							<div class="font-bold text-[16px] text-[#333333] ml-[12px]">
								{{ priceYuan(data[item.field]) }}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import { priceYuan } from "utils/toolMethods";

export default {
  name: "GoodNewsSquareCard",
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      list: [
        {
          title: "成案金额",
          field: "amount",
        },
        {
          title: "委托金额",
          field: "caseEntrustAmount",
        },
      ],
    };
  },
  computed: {
    content() {
      const type = this.data.type.toString();

      switch (type) {
      case "1":
        return `恭喜【${this.data.userName}】 律师在${this.data.regionName}成案！`;
      case "2":
        return `恭喜 ${this.data.institutionName || "****"} 【${this.data.userName}】 律师在${this.data.regionName}成案！`;
      default:
        return "";
      }
    },
  },
  methods: {
    priceYuan,
    handleClick() {
      this.$emit("click", this.data);
    },
  },
};
</script>
