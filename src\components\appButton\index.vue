<template>
  <button
      class="app-button"
      @click.prevent="handleClick"
      @mousedown.prevent="handleMousedown"
      :data-disabled="disabled"
      :disabled="disabled"
      :class="[`app-button_${styleType}_${type}`,`app-button_${size}`,disabled?`app-button_${styleType}_${type}--disabled`:'']"
  >
    <i v-if="icon" class="iconfont" :class="[icon]" /><slot />
  </button>
</template>

<script>

import { delayClickMixins } from "utils/delayClick";

export default {
  name: "AppButton",
  mixins: [delayClickMixins()],
  props: {
    icon: {
      type: String,
      default: ""
    },
    size: {
      type: String,
      default: "large",
      validator: (value) => {
        /* medium中等按钮 small小型按钮  large最大按钮*/
        return ["small", "medium", "large"].includes(value);
      }
    },
    disabled: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: "primary",
      validator: (value) => {
        /* primary主要按钮  info灰色按钮  success成功按钮  warning警告按钮 danger危险按钮 publicize宣传按钮活动使用*/
        return ["primary", "info", "success", "warning", "danger", "publicize"].includes(value);
      }
    },
    styleType: {
      type: String,
      default: "button",
      validator: (value) => {
        /* button按钮样式  border边框样式  text文字样式*/
        return ["button", "border", "text"].includes(value);
      }
    },
    /* 使用Mousedown方法*/
    useMousedown: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    /* 判断能不能点击*/
    canClick(callback) {
      if (this.disabled) return false;
      this.delayClickCallbackWrapper(callback);
    },
    handleClick(e) {
      if(!this.useMousedown){
        this.canClick(() => {
          this.$emit("click");
          this.$emit("preventClick", e);
        });
      }
    },
    handleMousedown(e){
      if(this.useMousedown){
        this.canClick(() => {
          this.$emit("handleMousedown", e);
        });
      }
    }
  }
};
</script>

<style scoped lang="scss">
@import "@/components/appButton/indexx.scss";
.app-button {
  outline: none;
  border: none;
  background: none;
  border-radius: 4px 4px 4px 4px;
  @extend .cursor-pointer;

  &_large {
    font-size: 14px;
    padding: 6px 16px;
  }

  &_medium {
    font-size: 14px;
    padding: 4px 12px;
  }

  &_small {
    font-size: 12px;
    padding: 3.5px 12px;

  }

  .iconfont {
    font-size: inherit;
    margin-right: 8px;
  }

  @include generateButton()
}

</style>
