import { request } from "utils/axios";

/** 上传喜报 */
export const lcGoodNewsV2Upload = (data) =>
  request.post("/law-cloud/lcGoodNewsV2/upload", data);

/** 我的喜报分页 */
export const lcGoodNewsV2MyGoodNews = (data) =>
  request.post("/law-cloud/lcGoodNewsV2/myGoodNews", data);
/** 我的喜报-喜报详情 */
export const lcGoodNewsV2DetailById = (data) =>
  request.post("/law-cloud/lcGoodNewsV2/detailById", data);

/** 重新上传（编辑喜报） */
export const lcGoodNewsV2ReUpload = (data) =>
  request.post("/law-cloud/lcGoodNewsV2/reUpload", data);
/** 撤销审核 */
export const lcGoodNewsV2Cancel = (data) =>
  request.post("/law-cloud/lcGoodNewsV2/cancel", data);
/** 删除喜报 */
export const lcGoodNewsV2Delete = (data) =>
  request.post("/law-cloud/lcGoodNewsV2/delete", data);
/** 喜报广场列表 */
export const lcGoodNewsV2SquareList = (data) =>
  request.post("/law-cloud/lcGoodNewsV2/squareList", data);
/** 喜报广场-喜报详情 */
export const lcGoodNewsV2SquareDetailById = (data) =>
  request.post("/law-cloud/lcGoodNewsV2/squareDetailById", data);
