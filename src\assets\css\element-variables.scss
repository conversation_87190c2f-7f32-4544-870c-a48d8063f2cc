/* 改变主题色变量 */
$--color-primary: $primary-color;
$--disabled-fill-base: #EEEEEE;
/* 改变 icon 字体路径变量，必需 */
$--font-path: '~element-ui/lib/theme-chalk/fonts';

/* 表格 */
$--table-header-background-color: #F5F5F7;
$--table-border-color: #EEEEEE;
$--table-row-hover-background-color: #EDF3F7;
$--table-font-color: #666666;
$--table-fixed-box-shadow: 0 0 14px rgba(0, 0, 0, .15);

/* Button */
$--button-info-font-color: #333333;
$--button-info-background-color: #F5F5F7;
$--button-info-border-color: #F5F5F7;

/* Input */
$--input-font-color: #333333;
$--input-border: 1px solid #EEEEEE;
$--input-placeholder-color: #CCCCCC;
$--input-hover-border: $--color-primary;
$--select-border-color-hover: $--color-primary;
@import "~element-ui/packages/theme-chalk/src/index";
