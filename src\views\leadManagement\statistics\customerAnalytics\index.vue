<template>
  <div class="wrap">
    <!-- 筛选   -->
    <div class="search-box">
      <el-form :inline="true" ref="form" :model="form" label-width="80px">
        <el-form-item label="员工">
          <el-select v-model="form.staffId" filterable  placeholder="请选择人员">
            <el-option v-for="(item,i) in personnelList" :key="i" :label="item.userName" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="日期">
          <el-date-picker
            v-model="form.dates"
            :picker-options="datePickerOptions(180)"
            type="daterange"
            value-format="yyyyMMdd"
            align="center"
            clearable
            prefix-icon="el-icon-date"
            end-placeholder="结束日期"
            start-placeholder="开始日期"
            @change="changeDateRange"
          />
        </el-form-item>
        <el-form-item style="margin-left: 40px">
          <el-button type="primary" @click="onSearch">查询</el-button>
          <el-button @click="handleRest">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!-- 统计图   -->
    <div class="charts-pie">
      <div class="fig-wrap fig-border">
        <div class="fig-box fig-box-left">
            <p class="head-title">跟进客户省份覆盖</p>
          <div>
            <china-map :area-data="areaData" />
          </div>
        </div>
        <div class="fig-box fig-box-right">
          <p class="head-title">跟进状态分布</p>
<!--          <follow-up-status-distribution-echarts class="pie-box" ref="echarts2" />-->
          <labels-echarts :info-data="infoStatus" class="pie-box" />
        </div>
      </div>
      <div class="fig-box fig-margin">
        <p class="head-title">跟进标签分布</p>
        <div class="flex flex-space-between labels-box">
          <div v-for="(item,i) in labelsData" :key="i">
            <p class="tc labels-title">{{ item.labelTypeName }}</p>
            <labels-echarts :radius="['40%', '30%']" :info-data="item.labelDistribution" class="labels" ref="echarts2" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { trimesterTimeFormat } from "utils/toolMethods";
import ChinaMap from "views/leadManagement/statistics/customerAnalytics/component/chinaMap.vue";
import LabelsEcharts from "./component/labelsEcharts.vue";
import { followUpLabelDistribution, followUpStatusDistribution, lawFirmLocation } from "@/api/clues";
import { lcInstitutionStaffList } from "@/api/system";
import { locationAll } from "views/leadManagement/statistics/customerAnalytics/component/location";
import datePickerOptions from "utils/datePickerOptions";


export default {
  name: "CustomerAnalytics",
  components: {  ChinaMap, LabelsEcharts },
  data(){
    return{
      form: {
        staffId: "",
        dates: trimesterTimeFormat("YYYYMMDD")
      },
      areaData: [],
      personnelList: [],
      labelsData: [],
      infoStatus: []
    };
  },
  created() {
    lcInstitutionStaffList().then(res => {
      /* 追加全部选项*/
      (res || []).unshift({ id: "0", userName: "全部" });
      this.personnelList = res;
    });


    this.getData();
  },
  methods: {
    datePickerOptions,
    changeDateRange(){

    },
    // 查询
    onSearch(){
      this.getData();
    },
    // 重置
    handleRest(){
      this.form = {
        staffId: "",
        dates: trimesterTimeFormat("YYYYMMDD")
      };
      this.getData();
    },
    // 对比省的code
    handleData(name) {
      let new_name = null;
      new_name = locationAll.filter(res => name.indexOf(res.name) !== -1);
      return new_name[0].name || "";
    },
    getData(){
      const data = {
        staffId: this.form.staffId,
      };
      if(this.form.dates ){
        data.beginDate = this.form.dates[0];
        data.endDate = this.form.dates[1];
      }
      // 城市
      lawFirmLocation(data).then(r => {
        this.areaData = r.map(s => {
          return {
            ...s,
            name: this.handleData(s.name),
          };
        });
      });
      // 跟进状态
      followUpStatusDistribution(data).then(data => {
        this.infoStatus = data
      });

      followUpLabelDistribution(data).then(r => {
        this.labelsData = r;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.wrap{
  //padding: 24px;
  background-color: #FFFFFF;
  .search-box{
    padding: 24px 0 0;
  }

  .fig-box{
    border: 1px solid #EEEEEE;
  }

  .head-title{
    height: 54px;
    background: #FFFFFF;
    box-shadow: inset 0px -1px 0px 0px #EEEEEE;
    font-weight: bold;
    font-size: 16px;
    color: #333333;
    line-height: 54px;
    padding: 0 24px;
  }
}
  .fig-wrap{
    display: grid;
    grid-template-columns:2fr 1fr;
    //display: flex;
    //justify-content: space-between;
    grid-gap: 24px;
    margin-bottom: 24px;
    //.fig-box-left{
    //    width: pxToPer(734);
    //}
    //
    //.fig-box-right{
    //  width: pxToPer(354);
    //}

  .pie-box{
    margin-top: 30%;
  }
}
   .charts-pie{
     padding: 0px 24px 24px;
   }
   .labels-box{
     padding-top: 24px;

     > div{
       width: 25%;

       //padding: 0 20px;
     }
     .labels-title{
       font-weight: 600;
       font-size: 14px;
       color: #333333;
     }
     .labels{
       width: 100%;
       box-sizing: border-box;
     }
   }
</style>
