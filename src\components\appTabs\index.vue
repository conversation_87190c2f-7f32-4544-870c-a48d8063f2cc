<template>
  <div class="tabs d-flex flex-align-center">
    <div v-for="i in list" @click="handleSwitch(i)" :key="i.value" :class="{active:activeValue === i.value}" class=" tab-item flex-1 cursor-pointer">{{i.label}}</div>
  </div>
</template>

<script>
export default {
  name: "AppTabs",
  props: {
    list: {
      type: Array,
      default: () => ([
        { label: "全部", value: "all" },
        { label: "待回复", value: "wait" },
        { label: "已回复", value: "reply" }
      ])
    },
    activeValue: {
      type: [String, Boolean, Number],
      default: ""
    }
  },
  methods: {
    handleSwitch({ label, value }) {
      this.$emit("switch", { label, value });
      this.$emit("update:activeValue", value);
    }
  }
};
</script>

<style scoped lang="scss">
.tabs{
  padding: 2px;
  background: #F5F5F7;
  border-radius: 8px 8px 8px 8px;
  overflow: hidden;
  .tab-item{
    line-height: pxToPer(32);
    text-align: center;
    font-size: 12px;
    font-weight: 400;
    color: #999999;
    &.active{
      background: #FFFFFF;
      border-radius: 6px 6px 6px 6px;
      color: #3887F5;
    }
  }
}
</style>
