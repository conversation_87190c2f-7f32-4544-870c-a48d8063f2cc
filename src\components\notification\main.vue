<template>
  <transition name="notification-fade">
    <div
      class="notification"
      v-show="visible"
      :class="[customClass]"
      :style="positionStyle"
    >
      <slot>
        {{message}}
      </slot>
    </div>
  </transition>
</template>

<script>
import { debounce } from "utils/business-methods";
export default {
  data() {
    return {
      visible: false,
      message: "",
      showClose: true,
      customClass: "",
      onClose: null,
      onClick: null,
      closed: false,
      positionStyle: null,
      draggable: true
    };
  },
  watch: {
    closed(newVal) {
      if (newVal) {
        this.visible = false;
        this.$el.addEventListener("transitionend", this.destroyElement);
      }
    }
  },
  mounted() {
    if (this.draggable) this.$el.addEventListener("mousedown", this.elementDrag);
  },
  methods: {
    destroyElement() {
      this.$el.removeEventListener("transitionend", this.destroyElement);
      if (this.draggable) this.$el.removeEventListener("mousedown", this.elementDrag);
      this.$destroy();
      this.$el.parentNode.removeChild(this.$el);
    },

    click() {
      if (typeof this.onClick === "function") {
        this.onClick();
      }
    },

    close() {
      this.closed = true;
      if (typeof this.onClose === "function") {
        this.onClose();
      }
    },
    elementDrag(e) {
      const disX = e.clientX - this.$el.offsetLeft;
      const disY = e.clientY - this.$el.offsetTop;
      // 改变鼠标样式
      this.$el.style.cursor = "grabbing";
      document.onmousemove = (e) => {
        // 清除默认的 right 和 bottom
        this.$el.style.right = "auto";
        this.$el.style.bottom = "auto";

        this.$el.style.left = e.clientX - disX + "px";
        this.$el.style.top = e.clientY - disY + "px";

        // 控制不超出边界
        if (this.$el.offsetLeft + this.$el.offsetWidth > document.body.offsetWidth) {
          this.$el.style.left = document.body.offsetWidth - this.$el.offsetWidth + "px";
        }
        if (this.$el.offsetTop + this.$el.offsetHeight > document.body.offsetHeight) {
          this.$el.style.top = document.body.offsetHeight - this.$el.offsetHeight + "px";
        }
        if (this.$el.offsetLeft < 0) {
          this.$el.style.left = 0;
        }
        if (this.$el.offsetTop < 0) {
          this.$el.style.top = 0;
        }
        // 本地存储
        this.setStoragePosition();
      };
      document.onmouseup = () => {
        this.$el.style.cursor = "grab";
        document.onmousemove = null;
        document.onmouseup = null;
      };
    },
    setStoragePosition: debounce(function() {
      localStorage.setItem("notificationPosition", JSON.stringify({
        left: this.$el.style.left,
        top: this.$el.style.top
      }));
    }, 500)
  }
};
</script>

<style scoped lang="scss">
.notification{
  display: flex;
  position: fixed;
  bottom: 28px;
  right: 0;
  background: #fff;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  cursor: grab;
}

</style>
