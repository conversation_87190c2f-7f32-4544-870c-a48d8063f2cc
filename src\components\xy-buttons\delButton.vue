
<template>
 <div class="inline">
  <el-popconfirm
    v-if="isTableBtn"
    :title="title"
    cancel-button-text='不用了'
    class="del-wrap"
    confirm-button-text='确定'
    icon="el-icon-info"
    icon-color="red"
    @confirm="handleDel">
    <el-button slot="reference" :type="typeName" class="del-button"><slot>删除</slot></el-button>
  </el-popconfirm>

    <el-button v-else  type="danger" @click="handleDelData">
      <slot>删除</slot>
    </el-button>
  </div>

</template>

<script>
import buttonPermissionsEnum from "@/enum/buttonPermissionsEnum";

export default {
  name: "DelButton",
  props: {
    title: {
      type: String,
      default: "确定要删除该条数据吗?"
    },
    params: [Object, String, Number],
    request: {
      type: Object,
      default: () => {
        return {
          deleteUrl: () => Promise.resolve()
        };
      }
    },
    isRefresh: {
      type: Boolean,
      default: true
    },
    isTableRefresh: {
      type: Boolean,
      default: true,
      desc: "操作后刷新页面还是刷新表格"
    },
    isTableBtn: {
      type: Boolean,
      default: true,
      desc: "是否是表格里面的按钮"
    },
    typeName: {
      type: String,
      default: "text"
    }
  },
  data() {
    return {
      filed: buttonPermissionsEnum.DELETE
    };
  },
  methods: {
    handleDel() {
      this.request.deleteUrl(this.params).then(res => {
        this.$message.success("删除成功!");
        this.$emit("click");
        if (this.isRefresh) {
          if (this.isTableRefresh) {
            // 刷新表格
            this.$store.dispatch("setIsTableRefresh");
          } else {
            // 刷新页面
            this.$store.dispatch("setIsRouterRefresh");
          }
        }
      }).catch(() => {});
    },
    handleDelData() {
      this.$emit("click");
    }
  }
};
</script>

<style scoped>
.del-wrap{
  margin-left: 0 !important;
}
.del-button{
  margin-left: 10px;
  color: #F56C6C;

}
</style>
