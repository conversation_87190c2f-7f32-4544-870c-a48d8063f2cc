import { request } from "utils/axios";

/** 律客云-发送登录短信 */
export const getSmsCode = (params) => request.post("/law-cloud/lc/login/sendSms", params);

/** 律客云-短信登录 */
export const smsLogin = (params) => request.post("/law-cloud/lc/login/smsLogin", params);

/** 律客云-用户退出登录接口 */
export const loginOut = (params) => request.post("/law-cloud/lc/login/logOut", params);

/** 律客云-获取当前系统登录人信息(菜单相关信息) */
export const getLoginUserInfo = (params) => request.post("/law-cloud/lc/login/getStaffLoginInfo", params);

// 律客云PC扫码二维码生成
export const scanQRCodeCreateQRCode = (params) => request.post("/law-cloud/lc/scanQRCode/createQRCode", params);

// 检查二维码状态
export const scanQRCodeCheckStatus = (params) => request.post("/law-cloud/lc/scanQRCode/checkStatus", params);
