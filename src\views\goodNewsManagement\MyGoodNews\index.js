import global from "utils/constant";
import { datePickerOptionsExpand } from "utils/datePickerOptions";
import { dataDetailList } from "@/api/common";
import { goodsAmountSort, goodsCheckStatus } from "@/enum/select";


export const tableColumn = [
  {
    label: "律所/机构名称",
    prop: "institutionName",
    width: "202px",
  },
  {
    label: "员工姓名",
    prop: "staffName",
    width: "118px",
  },
  {
    label: "案件类型",
    prop: "typeValue",
    width: "104px",
    search: true,
    type: global.formItemType.select,
    filterUrl: true,
    multiple: true,
    syncData: {
      url: dataDetailList,
      value: "value",
      label: "label",
      params: { groupCode: "LAWYER_SPECIALITY" },
    },
  },
  {
    label: "线索类型",
    prop: "clueType",
    width: "104px",
    search: true,
    type: global.formItemType.select,
    filterUrl: true,
    multiple: true,
    syncData: {
      url: dataDetailList,
      value: "value",
      label: "label",
      params: { groupCode: "LC_GOOD_NEWS_INFO_CHANNEL" },
    },
  },
  {
    label: "成案地点",
    prop: "regionName",
    width: "186px",
    search: true,
    type: global.formItemType.regioncascader,
    multiple: true,
  },
  {
    label: "成案金额",
    prop: "amountSort",
    search: true,
    tableHidden: true,
    type: global.formItemType.select,
    syncData: {
      data: goodsAmountSort,
    },
  },
  {
    label: "成案金额",
    prop: "amount",
    width: "137px",
    type: "money"
  },
  {
    label: "委托金额",
    prop: "caseEntrustAmountSort",
    width: "108px",
    search: true,
    tableHidden: true,
    type: global.formItemType.select,
    syncData: {
      data: goodsAmountSort,
    },
  },
  {
    label: "委托金额",
    prop: "caseEntrustAmount",
    width: "108px",
    type: "money"
  },
  {
    label: "上传时间",
    prop: "createTime",
    width: "192px",
  },
  {
    prop: "a1",
    propKey: ["createTimeStart", "createTimeEnd"],
    label: "上传时间",
    search: true,
    tableHidden: true,
    type: global.formItemType.datetimerange,
    valueFormat: "yyyy/MM/dd HH:mm:ss",
    pickerDate: datePickerOptionsExpand(90),
  },
  {
    label: "审核状态",
    prop: "checkStatus",
    width: "120px",
    search: true,
    type: global.formItemType.select,
    filterUrl: true,
    syncData: {
      data: goodsCheckStatus,
    },
    multiple: true,
  },
  {
    label: "奖励金额",
    prop: "giftAmount",
    width: "108px",
    type: "money"
  },
  {
    label: "备注",
    prop: "checkReason",
  },
];
