<template>
  <div class="min-h-[142px] bg-[#FFFFFF] rounded-[2px]">
    <workbench-card-title>
      账户信息
      <template #append>
        <deposit-entrance>
          <app-button  class="!p-[0]" style-type="text" tail-icon="icon-xianxingtubiao-9"  @click="handlePayShow">
            去充值 <i class="iconfont icon-xianxingtubiao-9" />
          </app-button>
        </deposit-entrance>
      </template>
    </workbench-card-title>
    <div class="pb-[12px]">
        <div class="account-box flex  justify-between">
          <div v-for="item in list" :key="item.prop" class="items-center">
            <p class="text-[24px]  text-[#333333]">
              {{ data[item.prop]?(item.type==='money'?priceNumber(data[item.prop]):data[item.prop]):'0' }}
            </p>
            <p class="text-[12px] mt-[8px] text-[#666666]">{{ item.label }}</p>
          </div>
        </div>
    </div>
  </div>
</template>

<script>
import WorkbenchCardTitle from "views/workbenchManagement/workbench/components/CardTitle.vue";
import AppButton from "components/appButton/index.vue";
import DepositEntrance from "components/DepositEntrance/index.vue";
import { lcLawFirmsInstitutionAllAccountInfo } from "@/api/workbenchManagement";
import { priceNumber } from "utils/toolMethods";
import { hasMenuPermissionsExecute } from "router/menuPermissions";
import menuPermissionIDEnum from "@/enum/menuPermissionIDEnum";

export default {
  name: "AccountBalance",
  components: { DepositEntrance, AppButton, WorkbenchCardTitle },
  data() {
    return {
      data: {},
      list: [
        {
          label: "剩余可抢案源数",
          prop: "remainCaseSourceNum"
        },
        {
          label: "剩余可抢咨询数",
          prop: "remainQaMessageNum"
        },
        {
          label: "剩余可用法临币",
          prop: "remainAmount",
          type: "money"
        },
        {
          label: "赠送可用法临币",
          prop: "giftRemainAmount",
          type: "money"
        },
      ]
    };
  },
  mounted() {
    /* 有这个菜单权限才请求*/
    hasMenuPermissionsExecute(menuPermissionIDEnum.LAW_FIRM_MANAGEMENT, () => {
      lcLawFirmsInstitutionAllAccountInfo().then((data = {}) => {
        this.data = data;
      });
    });
  },
  methods: {
    priceNumber,
    handlePayShow() {
      this.$store.commit("SET_IS_PAY_PC", true);
    },
  }
};
</script>

<style scoped lang="scss">
.account-box{
  text-align: center;
  padding: 16px 0;

  > div {
    flex: 1;
  }
}
</style>
