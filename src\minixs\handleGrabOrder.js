import { judgeBalance } from "views/leadManagement/clueSquare/cluesToTheSourceOfTheCase";
import { isLeadPackageDisplay } from "components/leadPackageDisplay";

export default {

  methods: {
    handleGrabOrder(params) {

      this.currentParams =  Object.assign({}, params);
      const _num = this.currentParams.way === 1 ?  this.deductNum : 1;

      // accountType 抢单账户类型（0无 1、线索包账户，2、法临币充值账户，3、法临币赠送账户）
      if(this.remainingGrabTimes.accountType) {
        const status = judgeBalance({ ...this.currentParams, accountType: this.remainingGrabTimes.accountType, deductNum: _num }, this.remainingGrabTimes, this);
        if(status) return  this.submitPop(this.remainingGrabTimes.accountType);
        return;
      }

      const status =  isLeadPackageDisplay();
      const isNoMoney = (~~this.remainingGrabTimes.remainAmount < ~~this.currentParams.amount && ~~this.remainingGrabTimes.giftRemainAmount < ~~this.currentParams.amount);


      if(!status && isNoMoney){
        return this.$message.warning("余额不足，请联系管理员充值吧~");
      }else if(status && ~~this.remainingGrabTimes.remainCaseCount < ~~_num && isNoMoney){
        return this.$message.warning("余额不足，请联系管理员充值吧~");
      }
      this.dialogVisible = true;

    },
  }

};
