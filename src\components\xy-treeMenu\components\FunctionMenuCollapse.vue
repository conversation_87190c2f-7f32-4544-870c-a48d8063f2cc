<template>
	<div class="menu-collapse">
		<el-menu
			class="xy-menu"
			:default-active="$route.path"
			:show-timeout="200"
			mode="vertical"
			:collapse="true"
			:unique-opened="true"
			background-color="#091627"
			text-color="#ffffff"
			active-text-color="#ffffff"
		>
			<function-menu-item-collapse
				v-for="(route, index) in data"
				:key="index"
				:show-icon="true"
				:item="route"
				:base-path="route.path"
			/>
		</el-menu>
	</div>
</template>

<script>
import FunctionMenuItemCollapse from "components/xy-treeMenu/components/FunctionMenuItemCollapse.vue";

export default {
  name: "FunctionMenuCollapse",
  components: { FunctionMenuItemCollapse },
  props: {
    data: {
      type: [Object, Array],
      default: () => [],
    },
  },
};
</script>

<style scoped lang="scss"></style>
