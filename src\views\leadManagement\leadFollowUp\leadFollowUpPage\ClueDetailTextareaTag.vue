<template>
  <el-tooltip placement="top" popper-class="w-[284px] bg-[rgba(0,0,0,0.7)] rounded-[8px]" :disabled="!isMore">
    <div slot="content" class="flex flex-wrap">
      <div v-for="(item, index) in tags" :key="index" class="mr-[4px] mt-[4px]">
        <clue-detail-textarea-tag-item :close-icon="false" @select="handleSelect" :style-order="index" :data="item" />
      </div>
    </div>
    <div class="flex items-center clue-detail-textarea-tag-item-c">
      <clue-detail-textarea-tag-item
        v-if="!disabled"
        :tags-list="tagsList"
        @select="handleSelect"
        type="plus"
        :select-tag="tags"
      />
      <div v-for="(item, index) in showTags" :key="index" :class="[disabled?'mr-[8px]':'ml-[8px]']">
        <clue-detail-textarea-tag-item class="clue-detail-textarea-tag-item" :close-icon="!disabled" @select="handleSelect" :style-order="index" :data="item" />
      </div>
<!--      <div v-if="isMore" class="w-[24px] ml-[8px] h-[24px] bg-[#F5F5F7] rounded-[4px] flex items-center justify-center">-->
<!--        <i class="iconfont icon-gengduo" />-->
<!--      </div>-->
    </div>
  </el-tooltip>
</template>

<script>
import ClueDetailTextareaTagItem
  from "views/leadManagement/leadFollowUp/leadFollowUpPage/ClueDetailTextareaTagItem.vue";

export default {
  name: "ClueDetailTextareaTag",
  components: { ClueDetailTextareaTagItem },
  props: {
    /** 标签的值 */
    labelValues: {
      type: String,
      default: "",
    },
    /** 可以添加的标签 */
    tagsList: {
      type: Array,
      default: () => [],
    },
    /* 平坦化数组*/
    flatList: {
      type: Array,
      default: () => []
    },
    /* 不可编辑*/
    disabled: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    tags() {
      if (this.labelValues.length === 0) return [];

      const labelValues = this.labelValues.split(",");

      return labelValues.map(item => {
        const data = (this.basics.isArrNull(this.flatList) ? this.tagsList : this.flatList).find(tag => {
          return tag.label === item;
        });
        if (data) {
          let supplementDesc = {};
          try {
            supplementDesc = JSON.parse(data.supplementDesc);
          } catch (e) {
            console.error(e);
          }
          return {
            ...data,
            supplementDesc
          };
        }
        return data;
      }).filter(item => item);
    },
    /* 是否更多*/
    isMore() {
      return this.tags.length > 3 && !this.disabled;
    },
    showTags(){
      return !this.disabled ? this.tags.slice(0, 4) : this.tags;
    }
  },
  methods: {
    /** 选择了值后 */
    handleSelect(value) {
      let tags = [];

      // 如果已经存在，则删除，如果不存在，则添加
      if (this.tags.some(item => item.value === value.value)) {
        tags = this.tags.filter(item => item.value !== value.value);
      } else {
        tags = [...this.tags, value];
      }

      console.log("选择的内容", tags);

      // 判断 tags 和 this.tags 是否相等，如果不相等，则说明有变化
      if (tags.length !== this.tags.length) {
        const diff = [];

        // 判断是 删除 还是 添加
        if (tags.length > this.tags.length) {
          // 添加

          tags.forEach(item => {
            if (!this.tags.some(tag => tag.value === item.value)) {
              diff.push(item);
            }
          });

          this.$emit("add", diff);
        } else {
          // 删除

          this.tags.forEach(item => {
            if (!tags.some(tag => tag.value === item.value)) {
              diff.push(item);
            }
          });

          this.$emit("delete", diff);
        }
      }

      this.$emit("select", tags);
    },
  },
};
</script>

<style scoped lang="scss"></style>
