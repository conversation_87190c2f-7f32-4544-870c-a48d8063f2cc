<template>
	<div class="bg-white">
		<workbench-card-title class="pb-[24px]">
			<span class="flex items-center">
				自动抢案源计划<i
					@click="openAgreementPopup"
					class="iconfont icon-shuoming pl-[4px] !text-[16px] font-normal cursor-pointer !text-[#999999]"
				/>
			</span>
			<template #append>
				<div>
					<app-button icon="icon-Plan" @click="addPlan">新增计划</app-button>
				</div>
			</template>
		</workbench-card-title>
		<div class="px-[24px] pt-[16px] flex items-center">
			<div
				class="rounded-[4px] w-[25%] box-border ml-[16px] first-of-type:ml-[0px] border-[1px] border-solid border-[#EEEEEE] p-[16px]"
			>
				<p class="text-[14px] text-[#666666]">今日平台抢案源上限</p>
				<p class="font-[500] pt-[4px] text-[24px] text-[#333333]">
					{{ platformData.caseCount || 0 }}/{{ systemParams.grabCaseUpperLimit }}
				</p>
			</div>
			<div
				class="rounded-[4px] w-[25%] box-border ml-[16px] first-of-type:ml-[0px] border-[1px] border-solid border-[#EEEEEE] p-[16px]"
			>
				<p class="text-[14px] text-[#666666]">今日抢案源累计消耗线索包次数</p>
				<p class="font-[500] pt-[4px] text-[24px] text-[#333333]">
					{{ platformData.clueKitCount || 0 }}
				</p>
			</div>
			<div
				class="rounded-[4px] w-[25%] box-border ml-[16px] first-of-type:ml-[0px] border-[1px] border-solid border-[#EEEEEE] p-[16px]"
			>
				<p class="text-[14px] text-[#666666]">今日抢案源累计消耗法临币</p>
				<p class="font-[500] pt-[4px] text-[24px] text-[#333333]">
					{{ priceNumber(platformData.falinCurrencyCount) }}
				</p>
			</div>
		</div>
		<div class="flex items-center flex-wrap pr-[24px] pb-[24px]">
			<div
				class="w-[50%] pl-[24px] box-border pt-[24px]"
				v-for="item in planList"
				:key="item.planId"
			>
				<plan-cards
         :get-is-set-default-payment="getIsSetDefaultPayment"
					:plan-data="item"
					class="bg-[#FFFFFF] [box-shadow:0px_2px_10px_0px_rgba(0,0,0,0.1)] rounded-[4px] overflow-hidden"
					@refresh-list="getPlanList"
					@edit-plan="editPlan"
				/>
			</div>
		</div>
		<workbench-card-title class="!pt-[18px]">自动抢案源记录</workbench-card-title>
		<automatic-robbery-source-record />
		<action-plan
			:visible.sync="addPlanPopupVisible"
			@refresh-list="getPlanList"
			:edit-data="selectedPlanData"
			:system-params="systemParams"
		/>
		<order-grabbing-agreement :show.sync="agreementPopupState" />
		<warning-popup
			title="未设置默认支付方式"
			confirm-text="前往开启"
			cancel-text="关闭"
			:visible.sync="warningPopupVisible"
			@confirm="goToSetDefaultPayment"
			content="您未设置【默认支付方式】，无法制定自动抢单计划，请先开启默认支付方式"
		/>
		<warning-popup
			:show-cancel="false"
			:visible.sync="warningPopupVisible2"
			title="温馨提示"
			confirm-text="知道了"
			@confirm="warningPopupVisible2 = false"
			content="当前账号无默认支付方式设置权限，请联系机构管理员开通，开通后可制定自动抢单计划"
		/>
	</div>
</template>

<script>
import WorkbenchCardTitle from "@/views/workbenchManagement/workbench/components/CardTitle.vue";
import AppButton from "components/appButton/index.vue";
import PlanCards from "@/views/marketingManagement/automaticallyRobTheSourceOfTheCase/components/planCards/index.vue";
import ActionPlan from "@/views/marketingManagement/automaticallyRobTheSourceOfTheCase/components/actionPlan/index.vue";
import AutomaticRobberySourceRecord from "views/marketingManagement/automaticallyRobTheSourceOfTheCase/components/automaticRobberySourceRecord/index.vue";
import OrderGrabbingAgreement from "views/marketingManagement/automaticallyRobTheSourceOfTheCase/components/orderGrabbingAgreement/index.vue";
import { hasMenuPermissions } from "router/menuPermissions";
import menuPermissionIDEnum from "@/enum/menuPermissionIDEnum";
import WarningPopup from "components/appPopup/warningPopup.vue";
import { turnToDefaultPaymentSetting } from "@/utils/turnPage";
import { autoGrabPlanList, autoGrabPlanSummary, lcInstitutionStaffGetPayWay } from "@/api/clues";
import { commonConfigKey } from "@/api/common";
import { priceNumber } from "@/utils/toolMethods";

export default {
  name: "AutomaticallyRobTheSourceOfTheCase",
  components: {
    WarningPopup,
    OrderGrabbingAgreement,
    AutomaticRobberySourceRecord,
    AppButton,
    WorkbenchCardTitle,
    PlanCards,
    ActionPlan,
  },
  data() {
    return {
      agreementPopupState: false,
      /* 未设置默认支付方式弹窗 */
      warningPopupVisible: false,
      /* 无权限弹窗 */
      warningPopupVisible2: false,
      /* 新增、修改计划弹窗 */
      addPlanPopupVisible: false,
      /* 选择的计划数据 */
      selectedPlanData: {},
      /** 抢单计划列表 */
      planList: [],
      /** 从系统参数获取到的各种上限 */
      systemParams: {},
      /** 今日平台数据*/
      platformData: {},
      /** 是否到达上限 */
      isReachUpperLimit: false,
    };
  },
  mounted() {
    this.getPlanList();
    this.getCommonConfigKey();
    this.getPlatformData();
  },
  methods: {
    /** 获取是否设置了默认支付 */
    getIsSetDefaultPayment() {
      return lcInstitutionStaffGetPayWay().then(data => {
        if (data.accountType === 0) {
          this.warningPopupVisible = true;
          return Promise.reject("未设置默认支付方式");
        }

        return Promise.resolve();
      });
    },
    priceNumber,
    /** 获取今日平台数据*/
    getPlatformData() {
      autoGrabPlanSummary().then(data => {
        this.platformData = data;
      });
    },
    /** 获取新增限制 */
    getCommonConfigKey() {
      commonConfigKey({
        paramName: "auto_grab_plan_param",
      }).then(data => {
        try {
          this.systemParams = JSON.parse(data);
        } catch (e) {
          console.error(e);
        }
      });
    },
    /** 获取计划列表 */
    getPlanList() {
      autoGrabPlanList().then(data => {
        this.planList = data;

        if(data.length >= this.systemParams.planUpperLimit) {
          this.isReachUpperLimit = true;
        } else {
          this.isReachUpperLimit = false;
        }
      });
    },
    /* 开启协议弹窗 */
    openAgreementPopup() {
      this.agreementPopupState = true;
    },
    /* 判断有无设置默认支付 */
    async checkDefaultPayment() {
      return Promise.resolve(true);
    },
    /* 判断有没有支付权限 */
    checkPaymentPermission() {
      return hasMenuPermissions(menuPermissionIDEnum.DEFAULT_PAYMENT_SETTING);
    },
    /* 判断是否需要弹出支付引导 */
    async checkNeedPopUpPaymentGuide() {
      // 没有权限弹出 开启权限引导
      if (!this.checkPaymentPermission()) {
        this.warningPopupVisible2 = true;
        return Promise.reject();
      }
      const hasDefaultPayment = await this.checkDefaultPayment();
      /* 没有配置默认支付 弹出设置引导 */
      if (!hasDefaultPayment) {
        this.warningPopupVisible = true;
        return Promise.reject();
      }
      return Promise.resolve();
    },
    /* 没有权限 前往开启点击 */
    goToSetDefaultPayment() {
      this.warningPopupVisible = false;
      turnToDefaultPaymentSetting();
    },
    /* 新增计划 */
    async addPlan() {
      try {
        if(this.isReachUpperLimit) {
          this.$message.error("计划已达上限，无法新增");
          return;
        }
        await this.getIsSetDefaultPayment();
        this.selectedPlanData = {};
        this.addPlanPopupVisible = true;
      } catch (e) {
        console.log(e);
      }
    },
    /* 修改计划 */
    async editPlan(data) {
      try {
        await this.getIsSetDefaultPayment();
        this.selectedPlanData = data;
        this.addPlanPopupVisible = true;
      } catch (e) {
        console.log(e);
      }
    },
  },
};
</script>

<style scoped lang="scss"></style>
