<template>
  <app-popup
    width="400px"
    top="40vh"
    v-bind="$attrs"
    :show-title="false"
    :visible.sync="getShow"
    v-on="$listeners"
  >
    <div>
      <div class="px-[24px] h-[54px] flex items-center">
        <i
          class="!text-[20px]  mr-[8px] iconfont text-[#ff851f]"
          :class="icon"
        />
        <div class="font-[500] text-[16px] text-[#333333]">{{title}}~</div>
      </div>
      <div class="px-[24px] text-[14px] text-[#666666]">
        {{ content }}
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "components/appPopup/index.vue";

export default {
  name: "WarningPopup",
  components: { AppPopup },
  inheritAttrs: false,
  props: {
    title: {
      type: String,
      default: ""
    },
    content: {
      type: String,
      default: ""
    },
    visible: {
      type: Boolean,
      default: false
    },
    icon: {
      type: String,
      default: "icon-xianxingtubiao-3"
    }
  },
  computed: {
    getShow: {
      get(){
        return this.visible;
      },
      set(val){
        this.$emit("update:visible", val);
      }
    }
  }
};
</script>

<style scoped lang="scss">

</style>
