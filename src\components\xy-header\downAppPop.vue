<template>
  <app-popup
        :show-title="false"
        width="560px"
        class="pop-wrap"
        :hide-footer="true"
        :visible.sync="visible">
    <div class="close-box">
      <img class="close" @click="handleClose" src="@/assets/images/close.png" />
    </div>
    <img class="img" v-for="(i,index) in data.previewImg" :key="index" :src="i" />
  </app-popup>
</template>

<script>
import AppPopup from "components/appPopup/index.vue";

export default {
  name: "DownAppPop",
  components: { AppPopup },
  props: {
    data: {
      type: Object,
      default: () => ({
        /* 预览的图片*/
        previewImg: [],
        /* 缓存的key*/
        key: ""
      })
    },
  },
  data(){
    return{
      visible: false
    };
  },
  computed: {
    localStorageKey() {
      return "SHOWDOWNAPPFILED" + (this.data.key || "");
    }
  },
  mounted() {
    this.visible = this.basics.isNull(localStorage.getItem(this.localStorageKey));
  },
  methods: {
    handleClose(){
      this.visible = false;
      localStorage.setItem(this.localStorageKey, false);
    }
  }
};
</script>

<style lang="scss" scoped>
.img{
  width: 100%;
}
.close-box{
  text-align: right;
  margin-top: 40%;
}
.close{
  width: 40px;
  height: 40px;
  cursor: pointer;
}
/deep/ .app-popup_class{
  background-color: transparent !important;
  box-shadow: none;
}
</style>
