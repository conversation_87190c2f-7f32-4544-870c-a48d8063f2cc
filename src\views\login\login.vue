<template>
	<div class="login">
		<div class="login-form">
			<div class="py-[64px] relative login-right pr flex-dis flex-align-center flex-space-center">
        <img @click="changeComponentName('ScanTheCodeLogin')" v-if="isFormLogin" class="cursor-pointer w-[80px] h-[80px] absolute right-[4px] top-[4px]" src="@/views/login/imgs/<EMAIL>" alt="" />
        <img @click="changeComponentName('FormLogin')" v-else class="cursor-pointer w-[80px] h-[80px] absolute right-[4px] top-[4px]" src="@/views/login/imgs/<EMAIL>" alt="" />
        <div class="top">
					<div class="flex-dis flex-align-center">
						<img class="logo" src="@/assets/images/logo.png" alt="" />
						<div>
							<h1>欢迎登录</h1>
							<h1>律客云管理平台</h1>
						</div>
					</div>
          <component @loginSuccess="loginSuccess" :is="componentName" />
				</div>
			</div>
		</div>
	</div>
</template>

<script>


import { removeTriggerTime } from "utils/storage";

export default {
  name: "Login",
  components: {
    FormLogin: () => import("views/login/formLogin.vue"),
    ScanTheCodeLogin: () => import("views/login/scanTheCodeLogin.vue")
  },
  data() {
    return {
      defaultPage: "/",
      componentName: "FormLogin"
    };
  },
  computed: {
    /* 判断是不是表单登录*/
    isFormLogin() {
      return this.componentName === "FormLogin";
    }
  },
  methods: {
    changeComponentName(name){
      this.componentName = name;
    },
    loginSuccess(){
      this.$store.commit("SET_USER_INFO", {});
      this.$router.push({ path: this.defaultPage });
      removeTriggerTime();
    }
  }
};
</script>

<style lang="scss" scoped>
.login {
	width: 100%;
	height: 100%;
	background: url('../../assets/images/<EMAIL>') no-repeat center;
	background-size: cover;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	overflow: auto;

	.login-right {
		width: 448px;

		.login-img {
			top: 44px;
			right: 40px;
			width: 198px;
		}

		.top {
			width: 320px;

			.logo {
				width: 64px;
				margin-right: 16px;
			}

			h1 {
				color: #262626;
				font-size: 24px;
				font-weight: 600;
				margin-bottom: 4px;
			}
		}
	}

	.login-form {
		transform: translateX(80%);
		background: #fff;
		box-sizing: border-box;
		box-shadow: 0 12px 24px 0 rgba(89, 120, 255, 0.16);
		border-radius: 8px 8px 8px 8px;
	}
}
</style>
