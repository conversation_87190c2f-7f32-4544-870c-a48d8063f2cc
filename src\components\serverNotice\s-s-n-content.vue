<template>
  <div class="s-s-n">
    <div class="s-s-n-title d-flex flex-align-center">
      <img src="@/assets/images/s-s-n.png" alt="" />
      待服务通知
    </div>
    <div class="s-s-n-content">
      {{message}}
    </div>
    <div class="s-s-n-btn-wrapper d-flex flex-space-between" @mousedown.stop>
      <app-button type="info" @click="startService">
        开始服务
      </app-button>
      <app-button  type="primary" @click="intoIm">
        进入会话
      </app-button>
    </div>
  </div>
</template>

<script>
import AppButton from "components/appButton/index.vue";
import { debounce } from "utils/business-methods";
export default {
  name: "SSNContent",
  components: { AppButton },
  props: {
    message: {
      type: String,
      default: ""
    }
  },
  mounted() {
  },
  methods: {
    startService: debounce(function() {
      this.$emit("startService");
    }, 500),
    intoIm: debounce(function() {
      this.$emit("intoIm");
    }, 500)
  }

};
</script>

<style scoped lang="scss">
.s-s-n{
  box-sizing: border-box;
  width: 236px;
  padding: 16px;
  &-title{
    font-size: 16px;
    font-weight: 500;
    color: #333333;
    img	{
      width: 24px;
      height: 24px;
      margin-right: 4px;
    }
  }
  &-content{
    max-height: 96px;
    font-size: 14px;
    font-weight: 400;
    color: #666666;
    line-height: 18px;
    overflow-y: auto;
    margin-top: 15px;
  }
  &-btn-wrapper{
    padding: 16px 0 0;
    ::v-deep button{
      padding: 6px 19px;
    }
  }
}
</style>
