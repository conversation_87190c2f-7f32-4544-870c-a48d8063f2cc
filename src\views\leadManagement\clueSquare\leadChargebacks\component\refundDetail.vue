<template>
  <div :class="['font-14',className]">
    <div v-if="detail.hintStr" class="text-[#EB4738] mt-[16px] "><i class="el-icon-error mr-[8px]" />{{detail.hintStr }}</div>
    <ul class="detail-box flex">
      <li v-for="(item,i) in detailColumn" :key="i" :class="item.className">
        <label>{{item.label}}：</label>
        <span v-if="item.filter">{{detail[item.prop] | wayQd}}</span>
        <span v-else>{{detail[item.prop]}}</span>
      </li>
    </ul>
  </div>
</template>

<script>
import { grabType } from "@/enum/staffing";

export default {
  name: "RefundDetail",
  filters: {
    wayQd(val){
      if(!val) return;
      const obj = grabType.find(r => r.value === val);
      return obj.label;
    }
  },
  props: {
    detail: {
      type: Object,
      default: () => {
        return {};
      }
    },
    className:{
      type:String,
      default:''
    }
  },
  data(){
    return{
      detailColumn: [
        {
          label: "线索ID",
          prop: "businessId"
        }, {
          label: "发布时间",
          prop: "publishTime"
        }, {
          label: "抢单类型",
          prop: "consumeType"
        }, {
          label: "发布人手机号",
          prop: "userPhone"
        }, {
          label: "发布地区",
          prop: "regionName"
        }, {
          label: "线索类型",
          prop: "typeLabel"
        }, {
          label: "涉案金额",
          prop: "amountGradeStr",
          className: "text-orange"
        }, {
          label: "抢单方式",
          filter: true,
          prop: "serverWay"
        }, {
          label: "案件描述",
          prop: "info",
          className: "row"
        },
      ]
    };
  },
};
</script>

<style lang="scss" scoped>
.detail-box{
  background: #FFFFFF;
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #EEEEEE;
  padding:6px 16px;
  flex-wrap: wrap;
  margin-top: 16px;
  li{
    width: 50%;
    line-height: normal;
    padding: 10px 0;

    &.row{
      width: 100%;
      span{
        display: block;
        background: #F5F5F7;
        border-radius: 4px 4px 4px 4px;
        padding: 12px;
        color: #666666;
        margin-top: 8px;
      }
    }

    &.text-orange{
      span{
        color: #F78C3E;
      }
    }

    label{
      display: inline-block;
      width: 98px;
      font-weight: 400;
      font-size: 14px;
      color: #666666;
      margin-right: 8px;
    }
    span{
      font-weight: 400;
      font-size: 14px;
      color: #333333;
    }
  }
}
//竖着排列
.erected{
  ul{
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #EEEEEE;
    padding-top: 6px;
    padding-bottom: 6px;
  }
  li{
    width: 100% !important;
  }
}
</style>
