import Vue from "vue";
import Main from "./main.vue";
import { isVNode } from "element-ui/src/utils/vdom";
const NotificationConstructor = Vue.extend(Main);

let instance = null;
let seed = 1;

const Notification = function(options) {
  if (instance) {
    instance.close();
  }
  options = options || {};
  const id = "notification_" + seed++;
  instance = new NotificationConstructor({
    data: options
  });
  if (isVNode(options.message)) {
    instance.$slots.default = [options.message];
    options.message = "REPLACED_BY_VNODE";
  }
  instance.id = id;
  instance.$mount();
  document.body.appendChild(instance.$el);
  instance.visible = true;
  instance.dom = instance.$el;
  instance.dom.style.zIndex = 1000;
  try {
    const notificationPosition = localStorage.getItem("notificationPosition");
    if (notificationPosition) {
      const position = JSON.parse(notificationPosition);
      instance.dom.style.right = "auto";
      instance.dom.style.bottom = "auto";
      instance.dom.style.top = position.top;
      instance.dom.style.left = position.left;
    }
  } catch (error) {}

  return instance;
};

export default Notification;
