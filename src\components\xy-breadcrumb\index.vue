
<template>
  <div>
      <!-- 页面的表头 -->
      <div class="xy-breadcrumb">
          <el-breadcrumb separator="/">
            <template v-for="(item, index) in breadcrumb">
              <el-breadcrumb-item :key="index">{{ item }}</el-breadcrumb-item>
            </template>
          </el-breadcrumb>
          <div class="btn-box">
            <xyBackButton v-if="showBackButton" />
            <xyRefreshButton />
          </div>
        </div>

  </div>

</template>

<script>
import xyRefreshButton from "@/components/xy-buttons/refreshButton.vue";
import xyBackButton from "@/components/xy-buttons/backButton.vue";
export default {
  name: "Index",
  components: {
    xyRefreshButton,
    xyBackButton
  },
  props: {
    detailBreadCrumb: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    breadcrumb() {
      return this.$store.getters.breadcrumb;
    },
    showBackButton() {
      return this.$store.getters.showBackButton;
    }
  }
};
</script>

<style lang="scss" scoped>
.xy-breadcrumb{
  width: 100%;
  height: 44px;
  padding: 0 $distance-normal;
  background: $menu-group-bg-color;
  box-sizing: border-box;
  position: relative;
  @extend .flex;
  border-bottom: 1px solid $weak-color;
  box-shadow: 0 2px 3px 0 rgba(0, 0, 0, 0.12), 0 0 3px 0 rgba(0, 0, 0, 0.04);
  &:before{
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    margin-top: -8px;
    width: 4px;
    height: 16px;
    background: $primary-color;
  }
}
.detail-bread{
  margin-bottom: 20px;
  button{
    margin-top: 15px;
  }
}
.title-first{
  font-weight: bold;
  cursor: pointer;
  color: #333;
}
</style>
