<template>
  <app-popup :visible="visible" @close="close" title="详情" :hide-footer="true" width="800px">
    <div class="p-[24px]">
      <div class="mb-[24px]">
        <clue-detail-title title="开票信息" />
        <div class="mt-[16px]">
          <app-descriptions :info-props="invoiceInfoProps" :data="detail" :column="2" />
        </div>
      </div>

      <div class="mb-[24px]">
        <clue-detail-title title="开票明细" />
        <div class="mt-[16px]">
          <xy-page index :request="invoiceItemsRequest" :column="invoiceItemsColumn" :show-search="false"
            :is-margin="false" :border="true" />
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import { institutionInvoiceDetailByInvoiceId, institutionInvoiceDetailByInvoiceIdPage } from "@/api/invoice.js";
import AppDescriptions from "@/components/AppDescriptions/index.vue";
import AppPopup from "@/components/appPopup/index.vue";
import XyPage from "@/components/xy-page/index.vue";
import { invoiceInfoProps, invoiceItemsColumn1, invoiceItemsColumn2 } from "@/views/invoiceManagement/invoiceRecord/index.js";
import ClueDetailTitle from "@/views/leadManagement/leadFollowUp/leadFollowUpPage/ClueDetailTitle.vue";

export default {
  name: "InvoiceDetail",
  components: {
    AppPopup,
    AppDescriptions,
    XyPage,
    ClueDetailTitle
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    invoiceId: {
      type: [String, Number],
      default: ""
    }
  },
  data() {
    return {
      detail: {},
      // 开票信息属性
      invoiceInfoProps,
      // 开票明细表格配置
      invoiceItemsColumn: [],
      // 开票明细请求配置
      invoiceItemsRequest: {
        getListUrl: (params) => institutionInvoiceDetailByInvoiceIdPage({ invoiceId: this.invoiceId, ...params })
      }
    };
  },
  watch: {
    invoiceId: {
      handler(val) {
        if (this.visible && val) {
          this.fetchInvoiceDetail();
        }
      },
      immediate: true
    }
  },
  methods: {
    fetchInvoiceDetail() {
      institutionInvoiceDetailByInvoiceId({ id: this.invoiceId }).then(data => {
        this.detail = data || {};
        if (this.detail.invoiceType === 1) {
          this.invoiceItemsColumn = invoiceItemsColumn1;
        } else {
          this.invoiceItemsColumn = invoiceItemsColumn2;
        }
      });
    },
    close() {
      this.$emit("update:visible", false);
    }
  }
};
</script>
