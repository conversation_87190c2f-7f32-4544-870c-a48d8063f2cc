<template>
	<el-container>
		<!-- 页头 -->

		<section class="app-content">
			<!-- 左侧菜单 -->
			<div class="aside [box-shadow:2px_0px_8px_0px_rgba(0,0,0,0.08)]">
				<!-- <xySideMenu></xySideMenu> -->
				<xyTreeMenu />
			</div>

			<!-- 右侧内容 -->
			<el-main class="app-main">
				<div ref="xyHeader">
					<xy-header />
					<history-view />
				</div>
				<!--        <xyBreadcrumb></xyBreadcrumb>-->
				<app-main :style="getMainStyle" />
			</el-main>
		</section>
		<certification-popup />
    <activity-pop-ups />
	</el-container>
</template>

<script>
import xyHeader from "components/xy-header/index";
// import xySideMenu from 'components/xy-sideMenu/index.vue'
import xyTreeMenu from "@/components/xy-treeMenu/index.vue";
import appMain from "views/layout/appMain";
import historyView from "@/components/xy-historyView/index.vue";
import CertificationPopup from "components/CertificationPopup/index.vue";
import ActivityPopUps from "components/ActivityPopUps/index.vue";

export default {
  name: "Layout",
  components: {
    ActivityPopUps,
    CertificationPopup,
    xyHeader,
    // xySideMenu,
    // xyBreadcrumb,
    appMain,
    xyTreeMenu,
    historyView,
  },
  provide() {
    return {
      // 用于面包屑组件隐藏
      toggleHistoryView: this.toggleHistoryView,
    };
  },

  data() {
    return {
      getMainStyle: {},
      isHideBreadcrumb: false,
    };
  },
  computed: {
    isCollapse() {
      return this.$store.getters.getIsCollapsed;
    },
  },
  watch: {
    $route() {
      this.getMainHeight();
    },
  },
  mounted() {
    this.getMainHeight();
  },
  methods: {
    toggleHistoryView(bool) {
      this.isHideBreadcrumb = bool === undefined ? !this.isHideBreadcrumb : !!bool;
      this.getMainHeight();
    },
    // 获取主体内容的高度
    getMainHeight() {
      this.$nextTick(() => {
        // 获取 xyHeader 组件的高度
        const xyHeaderHeight = this.$refs.xyHeader?.offsetHeight;
        this.getMainStyle = {
          height: `calc(100% - ${xyHeaderHeight + "px"})`,
        };
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.el-container {
	height: 100%;
	background: #f3f4f8;
}

.app-content {
	width: 100%;
	height: calc(100vh - 0px);
	display: flex;

	.aside {
		height: 100%;
		box-sizing: border-box;

		overflow-y: auto;
		overflow-x: hidden;
	}

	.app-main {
		flex: 1;
		height: 100%;
		padding: 0;

		.main-page {
			width: 100%;
			height: calc(100% - 90px);
			padding: 16px 24px;
			box-sizing: border-box;
			overflow-y: auto;
			overflow-x: hidden;
			//background: #fff;
		}
	}
}
</style>
