<template>
  <!--  <app-drawer-->
  <!--    :visible.sync="visible"-->
  <!--    title="新手引导"-->
  <!--    size="68%"-->
  <!--  >-->
  <!--    <img class="block img" v-for="(i,index) in imgsList" :src="i" :key="index" />-->
  <!--  </app-drawer>-->

  <el-dialog
    :visible.sync="visible"
    :show-close="false"
    width="1082px"
    class="box-wrap">
    <div class="wrap-box flex">
      <!--  left      -->
      <div class="box-left">
        <h4>欢迎了解律客云管理平台</h4>
        <p>平台具备案源、咨询、工具营销、数据监控、律所管理等多个模块，助力律所全面数字化发展。</p>
        <!--  列表        -->
        <ul class="box-left-title h-[374px] overflow-y-auto" ref="scroll">
          <li v-for="(item,i) in list" :key="i" :class="[(activeName === i) && 'checked']" @click="handleClick(i)">
            <label><img :src="item.remark.icon" />{{ item.label }}</label></li>
        </ul>
      </div>
      <!--  right    -->
      <div class="box-right pr">
        <img v-for="(i,index) in list" :key="index" v-show="index===activeName" :src="i.remark.img" />
        <!--  按钮      -->
        <div class="btns pa">
          <el-button type="info" @click="visible = false">我知道了</el-button>
          <el-button v-if="activeName < (list.length - 1)" type="primary" @click="handleClick(activeName+1)">下一功能</el-button>
        </div>
      </div>
    </div>

  </el-dialog>
</template>

<script>

import { dataDetailList } from "@/api/common.js";

export default {
  name: "BeginnerGuide",
  props: {
    value: {
      type: Boolean,
      default: false,
    },

  },
  data() {
    return {
      activeName: 0,
      list: []
    };
  },
  computed: {
    visible: {
      get() {
        return this.$store.getters["getGuide"];
      },
      set(val) {
        this.$store.commit("SET_GUIDE", val);
      },
    },
  },
  watch: {
    visible: {
      immediate: true,
      handler(){
        if(this.visible){
          this.getList();
        }
      }
    }
  },
  methods: {
    handleClick(i) {
      if (i >= this.list.length) return this.activeName = 0;
      this.activeName = i;
      this.$refs.scroll.scrollTo({
        top: 54 * i,
        behavior: "smooth",
      });
    },
    getList (){
      if(!this.basics.isArrNull(this.list)) return false;
      dataDetailList({
        groupCode: "LAW_CLOUD_ONBOARDING",
      }).then(data => {
        try {
          this.list = (data || []).map(item => ({
            ...item,
            remark: JSON.parse(item.remark)
          }));
          console.log(this.list);
        }catch (e) {
          console.log(e);
        }
      });
    }
  }
};
</script>


<style scoped lang="scss">
.img {
  width: 100%;
}

.box-wrap {
  /deep/ .el-dialog__header {
    display: none;
  }

  /deep/ .el-dialog__body {
    padding: 0;
  }

  /deep/ .el-dialog {
    border-radius: 16px;
  }
}

.wrap-box {
  width: 1082px;
  height: 668px;
  background: url("~@/assets/images/useGuide/bg.png") no-repeat;
  background-size: 100% 100%;

  .box-left {
    padding: 63px 40px;
    width: 330px;
    box-sizing: border-box;

    h4 {
      color: #222222;
      font-size: 20px;
      font-weight: 500;
      margin-bottom: 14px;
    }

    p {
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      margin-bottom: 40px;
    }

    &-title {
      border-left: 1px solid rgba(0, 0, 0, 0.10);

      li {
        margin-bottom: 26px;
        padding-left: 16px;
        position: relative;
        &:last-of-type{
          margin-bottom: 0;
        }

        &.checked {
          &:before {
            content: ' ';
            display: inline-block;
            width: 3px;
            height: 54px;
            background: linear-gradient(180deg, #71B5FF 0%, #2676E4 100%);
            position: absolute;
            left: 0;
          }
        }

        label {
          display: inline-block;
          cursor: pointer;
          width: 223px;
          height: 54px;
          box-shadow: 0px 6px 16px 0px rgba(0, 0, 0, 0.10);
          font-size: 16px;
          font-weight: 400;
          color: #333333;
          line-height: 54px;
          background: #FFFFFF;
          box-sizing: border-box;
          padding-left: 17px;

          img {
            width: 20px;
            height: 20px;
            vertical-align: middle;
            margin-right: 8px;
            position: relative;
            top: -1px;
          }
        }
      }
    }
  }

  .box-right {
    width: calc(100% - 320px);
    box-sizing: border-box;
    padding-left: 65px;
    padding-top: 122px;

    img {
      height: 406.25px;
      vertical-align: middle;

    }

    .btns {
      bottom: 40px;
      right: 40px;
    }
  }
}
</style>
