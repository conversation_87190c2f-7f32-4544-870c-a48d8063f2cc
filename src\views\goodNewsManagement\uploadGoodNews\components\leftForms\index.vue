<template>
	<div>
		<el-form
			:disabled="disabled"
			class="page-form"
			label-position="right"
			ref="form"
			:model="form"
			:rules="ruleConfig"
			label-width="100px"
		>
			<p class="flex font-bold text-[16px] text-[#333333] items-center !pb-[16px] !pt-[24px]">
				<i class="w-[4px] h-[16px] bg-[#3887F5] mr-[8px]" />基础信息
			</p>
			<div class=" border-solid border-[1px] border-[#EEEEEE]">
				<div class="flex items-center">
					<p
						class="font-bold flex items-center justify-center text-[14px] text-[#333333] w-[123px] h-[96px] bg-[#F5F5F7]"
					>
						律所logo
					</p>
					<img class="!pl-[24px] w-[72px] h-[72px]" alt="" :src="form.institutionLogo" />
				</div>
			</div>
			<div class="flex border-solid border-[1px]  border-t-[0] border-[#EEEEEE] mb-[20px]">
				<div class="flex-1 flex items-center">
					<p
						class="font-bold flex items-center justify-center text-[14px] text-[#333333] w-[123px] h-[48px] bg-[#F5F5F7]"
					>
						律所名称
					</p>
					<p class="text-[14px] flex-1 text-[#666666] !pl-[24px]">{{ form.institutionName }}</p>
				</div>
				<div class="flex-1 flex items-center">
					<p
						class="font-bold flex items-center justify-center text-[14px] text-[#333333] w-[123px] h-[48px] bg-[#F5F5F7]"
					>
						所在地城市
					</p>
					<p class="text-[14px] flex-1 text-[#666666] !pl-[24px]">
						{{ form.institutionRegionName }}
					</p>
				</div>
			</div>
			<el-form-item label-width="100px" label="员工姓名：" prop="staffId">
				<el-select
					filterable
					@change="staffChange"
					:disabled="!isAdministrator"
					class="w-full"
					v-model="form.staffId"
					placeholder="请选择"
				>
					<el-option
						v-for="item in staffList"
						:key="item.id"
						:label="item.userName"
						:value="item.id"
					/>
				</el-select>
			</el-form-item>

			<p class="flex font-bold text-[16px] text-[#333333] items-center !pb-[16px] !pt-[24px]">
				<i class="w-[4px] h-[16px] bg-[#3887F5] mr-[8px]" />成案合同
			</p>
			<el-form-item label-width="100px" label="上传合同：" prop="contractImgUrl">
				<upload-image class="!mb-0" v-model="form.contractImgUrl" :limit="3" />
				<!--        <p class="text-[12px] text-[#999999]">请上传图片，最多3张</p>-->
				<p class="text-[12px] text-[#EB4738]">
					温馨提示：请上传合同首页、带有成案金额、按手印或盖章的图片哦，最多3张，图片大小不超过1M
				</p>
			</el-form-item>

			<p class="flex font-bold text-[16px] text-[#333333] items-center !pb-[16px] !pt-[24px]">
				<i class="w-[4px] h-[16px] bg-[#3887F5] mr-[8px]" />案件信息
			</p>
			<el-form-item class="w-full" label-width="126px" label="当事人手机号：" prop="userPhone">
				<el-input
					v-model="form.userPhone"
					placeholder="请输入当事人手机号"
					maxlength="11"
					show-word-limit
				/>
			</el-form-item>
			<div class="flex flex-wrap justify-between caseInformation pl-[26px]">
				<el-form-item class="w-[49%]" label="案件类型：" prop="typeValue">
					<el-select
						@change="typeChange"
						class="w-full"
						v-model="form.typeValue"
						placeholder="请选择"
					>
						<el-option
							v-for="item in caseTypeList"
							:key="item.value"
							:label="item.label"
							:value="Number(item.value)"
						/>
					</el-select>
				</el-form-item>
				<el-form-item class="w-[49%]" label="线索类型：" prop="clueType">
					<el-select
						@change="clueChange"
						class="w-full"
						v-model="form.clueType"
						placeholder="请选择"
					>
						<el-option
							v-for="item in clueTypeList"
							:key="item.value"
							:label="item.label"
							:value="Number(item.value)"
						/>
					</el-select>
				</el-form-item>
				<el-form-item class="w-[49%]" label="成案地点：" prop="cityCodes">
					<region
						v-if="showCity"
						class="w-full city"
						v-model="form.cityCodes"
						:clearable="true"
						:api="areaGetPlatformArea"
						:get-names="true"
						@getregionsname="getRegionsName"
					/>
				</el-form-item>
				<el-form-item class="w-[49%]" label="成案周期：" prop="caseSuccessDay">
					<el-input
						v-model="form.caseSuccessDay"
						maxlength="3"
						show-word-limit
						placeholder="请输入成案周期"
					/>
				</el-form-item>
				<el-form-item class="w-[49%]" label="成案金额：" prop="amount">
					<el-input
						v-model="form.amount"
						:placeholder="minMoneyPlaceholder"
						maxlength="8"
						show-word-limit
					/>
				</el-form-item>
				<el-form-item class="w-[49%]" label="委托金额：" prop="caseEntrustAmount">
					<el-input
						v-model="form.caseEntrustAmount"
						maxlength="6"
						show-word-limit
						:placeholder="minMoneyPlaceholder"
					/>
				</el-form-item>
				<el-form-item class="w-full" label="案件回顾：" prop="caseReview">
					<el-input
						type="textarea"
						maxlength="100"
						show-word-limit
						:rows="6"
						v-model="form.caseReview"
						:placeholder="caseReviewPlaceholder"
					/>
				</el-form-item>
				<el-form-item class="w-full" label="案件详情：" prop="caseDetail">
					<el-input
						type="textarea"
						maxlength="100"
						show-word-limit
						:rows="6"
						v-model="form.caseDetail"
						:placeholder="caseDetailPlaceholder"
					/>
				</el-form-item>
			</div>
		</el-form>
		<div class="flex justify-end pb-[24px]">
			<app-button @click="turnToMyNews" type="info" class="mr-[16px]">取消并返回</app-button>
			<app-button :disabled="disabled" type="primary" @click="handleSubmit">立即上传</app-button>
		</div>
	</div>
</template>

<script>
import UploadImage from "components/uploadImage/index.vue";
import AppButton from "components/appButton/index.vue";
import { lcInstitutionStaffList } from "@/api/system";
import Region from "components/regionCascader/index.vue";
import { areaGetPlatformArea, dataDetailList, commonConfigKey } from "@/api/common";
import { ruleConfig } from "views/goodNewsManagement/uploadGoodNews/components/leftForms/index";
import { turnToMyNews } from "utils/turnPage";
import { priceNumber } from "@/utils/toolMethods";
import { minNumber } from "utils/validate";

export default {
  name: "LeftForms",
  components: { Region, AppButton, UploadImage },
  props: {
    form: {
      type: Object,
      default: () => {
        return {
          institutionLogo: "",
          institutionName: "",
          institutionRegionName: "",
          staffId: "",
          userPhone: "",
          typeValue: "",
          clueType: "",
          cityCodes: [],
          caseSuccessDay: "",
          amount: "",
          caseEntrustAmount: "",
          caseReview: "",
          caseDetail: "",
        };
      },
    },
    showCity: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      /* 案件回顾 提示*/
      caseReviewPlaceholder:
				"请输入案件回顾 100字以内\n" +
				"\n" +
				"例：妻方因丈夫隐匿夫妻共同财产提起诉讼，我方通过调取银行流水、不动产登记信息等证据，成功举证对方转移资金2100万元。经过3次开庭，法院最终支持我方诉求，判决离婚并对隐匿财产进行了公平分割。",
      /* 案件详情 提示*/
      caseDetailPlaceholder:
				"请输入案件详情 100字以内\n" +
				"\n" +
				"例：当事人持疑似伪造的遗嘱主张继承权，我方通过调取笔迹鉴定和走访知情人，成功证实遗嘱系伪造。最终法院支持了法定继承人的诉求，维护了当事人的合法权益",
      staffList: [],
      ruleConfig,
      caseTypeList: [],
      clueTypeList: [],
      minMoney: 5000,
    };
  },
  computed: {
    /* 判断是不是超级管理员*/
    isAdministrator() {
      return this.$store.getters.isAdministrator;
    },
    minMoneyPlaceholder() {
      return `请输入成案金额（${this.minMoney}~99999999.00元）`;
    },
  },
  mounted() {
    this.getMinAmount();
    this.getStaffList();
    this.getCaseType();
    this.getClueType();
  },
  methods: {
    areaGetPlatformArea,
    turnToMyNews,
    /* 获取最低金额*/
    getMinAmount() {
      commonConfigKey({
        paramName: "lc_good_news_v2_min_case_success_amount",
      }).then(res => {
        this.minMoney = priceNumber(res) || 2000;
        this.ruleConfig.amount[2] = { validator: minNumber(this.minMoney, "最小输入" + this.minMoney + "元"), trigger: "blur" };
        this.ruleConfig.caseEntrustAmount[2] = { validator: minNumber(this.minMoney, "最小输入" + this.minMoney + "元"), trigger: "blur" };
      });
    },
    /* 获取员工信息*/
    getStaffList() {
      lcInstitutionStaffList().then(res => {
        this.staffList = Object.freeze(res);
      });
    },
    /* 城市选择*/
    getRegionsName(data) {
      const { value, name } = JSON.parse(data);
      /* provinceCode	是	Integer	省编码
regionCode	是	Integer	地区编码*/
      const [provinceCode, regionCode] = value;
      const [provinceName, regionName] = name;
      this.$emit("update:form", {
        ...this.form,
        provinceCode,
        regionCode,
        provinceName,
        regionName,
      });
    },
    /* 案件类型*/
    getCaseType() {
      dataDetailList({
        groupCode: "LAWYER_SPECIALITY",
      }).then(res => {
        this.caseTypeList = Object.freeze(res);
      });
    },
    /* 获取线索类型*/
    getClueType() {
      dataDetailList({
        groupCode: "LC_GOOD_NEWS_INFO_CHANNEL",
      }).then(res => {
        this.clueTypeList = Object.freeze(res);
      });
    },
    staffChange(data) {
      const staff = this.staffList.find(item => item.id === data);
      if (staff) {
        this.$emit("update:form", {
          ...this.form,
          staffName: staff.userName,
        });
      }
    },
    typeChange(data) {
      const type = this.caseTypeList.find(item => Number(item.value) === data);
      if (type) {
        this.$emit("update:form", {
          ...this.form,
          typeName: type.label,
        });
      }
    },
    clueChange(data) {
      const type = this.clueTypeList.find(item => Number(item.value) === data);
      if (type) {
        this.$emit("update:form", {
          ...this.form,
          clueTypeName: type.label,
        });
      }
    },
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.$emit("handleSubmit", this.form);
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.city {
	::v-deep {
		.el-cascader {
			width: 100%;
		}
	}
}
.page-form {
	::v-deep {
		.el-form-item__label {
			padding-right: 16px !important;
		}
	}
}
</style>
