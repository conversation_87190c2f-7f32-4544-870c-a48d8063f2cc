import basics from "utils/basicsMethods";
import store from "@/store";




/**
 * @description 判断是否展示线索包相关数据 传入的data只有线索包才会执行 defaultData只有不是线索包才会执行 如果两个都没有传就直接返回当前线索包状态
 * <AUTHOR> @date 2024/6/5
 * @param {any} [data] 如果传的是函数以外的数据类型,是线索包就会直接返回当前传入的数据 如果传入是函数只有线索包才会执行这个函数
 * @param {any} [defaultData] 不是线索包时默认返回的数据或者执行函数
 * @return any 返回当前线索包状态
*/
export const isLeadPackageDisplay = (data, defaultData) => {
  /* 线索包状态*/
  const cluePackageState = store.getters["isItALeadPackageUser"];
  if(basics.isNull(data) && basics.isNull(defaultData)) return cluePackageState;
  /* 将所有类型转化成函数*/
  const dataFn = basics.isFunction(data) ? data : () => data;
  const defaultDataFn = basics.isFunction(defaultData) ? defaultData : () => defaultData;
  /* 判断是否是线索包*/
  if (cluePackageState) {
    return dataFn(cluePackageState);
  } else {
    return defaultDataFn(cluePackageState);
  }
};
