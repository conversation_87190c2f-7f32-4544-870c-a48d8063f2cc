import global from "@/utils/constant";
import { consumeStatus } from "@/enum/leadManagement.js";
import { datePickerOptionsExpand } from "utils/datePickerOptions.js";
import CaseSourceTag from "components/CaseSourceTag/index.vue";
import { priceYuan } from "utils/toolMethods";
import basics from "utils/basicsMethods";

const searchForm = [
  /* {
    prop: "amountGrade",
    label: "涉案金额",
    tableHidden: true,
    search: true,
    type: global.formItemType.select,
    syncData: {
      url: dataDetailList,
      value: "value",
      label: "label",
      params: { groupCode: "AMOUNT_GRADE" },
    },
  },*/
  {
    prop: "smsTemplateTitle",
    label: "发布地区",
    tableHidden: true,
    search: true,
    multiple: true,
    // checkStrictly: true,
    type: global.formItemType.regioncascader,
  },
  {
    prop: "a1",
    propKey: ["startTime", "endTime"],
    label: "发布时间",
    search: true,
    tableHidden: true,
    type: global.formItemType.datetimerange,
    valueFormat: "yyyy/MM/dd HH:mm:ss",
    pickerDate: datePickerOptionsExpand(90),
  },
  {
    prop: "info",
    label: "搜索",
    tableHidden: true,
    search: true,
    type: global.formItemType.input,
    placeholder: "关键字模糊匹配",
    maxlength: 5,
  },
  {
    prop: "status",
    label: "消耗情况",
    tableHidden: true,
    search: true,
    syncData: {
      data: consumeStatus,
    },
    type: global.formItemType.select,
  },
];

export const tableColumn = [
  ...searchForm,
  {
    prop: "caseSourceV2Id",
    label: "线索ID",
    width: "120px",
  },
  /* {
    prop: "amountGrade",
    label: "涉案金额",
    filterUrl: true,
    width: "110px",
    syncData: {
      url: dataDetailList,
      value: "value",
      label: "label",
      params: { groupCode: "AMOUNT_GRADE" },
    },
  },*/
  {
    prop: "createTime",
    label: "发布时间",
    width: "192px",
  },
  {
    prop: "provinceName/regionName",
    label: "发布地区",
    width: "174px",
    render: (h, { row }) => {
      return <span>{row.provinceName}{row.regionName}</span>;
    },
  },
  {
    prop: "consumptionCount",
    label: "已跟进律师",
    width: "110px",
    render: (h, { row }) => {
      const isShow = Number(row.sameLawFirmGrabStatus);
      return (
        <div>
          <span>{row.consumptionCount}/{row.consumptionCountMax}</span>
          {!!isShow && (
            <el-tag style="padding: 0px 4px;height:20px;line-height: 16px;margin-left: 8px;border: 1px solid #4F99FF;">
          同机构
            </el-tag>
          )}
        </div>
      );
    },
  },
  {
    prop: "info",
    label: "线索描述",
    minWidth: "334px",
    /* 是否可以选择隐藏 false 不可以*/
    isSelectCheck: false,
    hideToolTip: true,
    render: (h, { row }) => {
      let list = [];
      if(row.kjly) list = [row.kjly];
      return (
        <el-tooltip content={row.info} placement="top" popper-class="pre-line-tooltip">
          <CaseSourceTag list={list || []}>
            <p class={`truncate ${row.kjly ? "ml-[8px]" : ""}`}>{row.info}</p>
          </CaseSourceTag>
        </el-tooltip>);
    }
  },
  {
    prop: "userPhone",
    label: "发布人手机号",
    width: "133px",
  },

  {
    prop: "typeLabel",
    label: "线索类型",
    width: "90px",
  },

  {
    prop: "typeLabel1",
    label: "线索标签",
    width: "195px",
    render: (h, { row }) => {
      return (<CaseSourceTag filterCode={["QUICK_MESSAGE"]} list={row.caseLabelVos || []}></CaseSourceTag>);
    }
  },

  {
    prop: "serverAmount",
    label: "线索价格",
    width: "180px",
    render: (h, { row }) => {
      // if(!basics.isNull(row.rechargeGiftZero) && row.rechargeGiftZero) return (<div>-</div>);
      return (<div>
        <span>{priceYuan(row.serverAmount)}</span>
        {!basics.isNull(row.originalServerAmount) ?
          <span style={{
            color: "#999999",
            paddingLeft: "8px",
            textDecoration: "line-through"
          }}>{priceYuan(row.originalServerAmount)}</span> : null
        }
      </div>);
    }
  },
  {
    prop: "ykjAmount",
    label: "独享线索价格",
    width: "180px",
    render: (h, { row }) => {
      // if(!basics.isNull(row.rechargeGiftZero) && row.rechargeGiftZero) return (<div>-</div>);
      return (<div>
        <span>{priceYuan(row.ykjAmount)}</span>
        {!basics.isNull(row.originalYkjAmount)  ?
          <span style={{
            color: "#999999",
            paddingLeft: "8px",
            textDecoration: "line-through"
          }}>{priceYuan(row.originalYkjAmount)}</span> : null
        }
      </div>);
    }
  },
];

/** 前端提示余额不足  accountType 1 线索包 3 赠送 2 充值 **/
export const judgeBalance = (val, data, _this) => {
  let isPass = true;
  let isPassName = "线索包";
  switch (val.accountType) {
  case 1:
    const _num = val.isCaseSource ? data.remainCaseCount : data.remainQaMessageCount;
    isPass =  Number(_num)  >= Number(val.deductNum);
    isPassName = "线索包";
    break;
  case 2:
    isPass = Number(data.remainAmount) >= Number(val.amount);
    isPassName = "充值";
    break;
  case 3:
    isPass = Number(data.giftRemainAmount) >= Number(val.amount);
    isPassName = "赠送";
    break;
  }
  if(!isPass){
    _this.$confirm("当前设置的" + isPassName + "支付方式余额不足，请充值或前往系统设置更换其他支付方式", "余额不足提醒", {
      confirmButtonText: "确定",
      cancelButtonText: "前往更换设置",
      showClose: false,
      type: "warning"
    }).then(() => {

    }).catch(() => {
      _this.$router.push("defaultCaseGrabOrder");
    });
  }
  return isPass;

};
