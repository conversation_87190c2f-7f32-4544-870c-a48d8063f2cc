<template>
  <div ref="vertical-container"  class="vertical-container_id">
    <div :style="verticalCStyle">
      <div ref="vertical-swipe" class="vertical-swipe" :style="swipeStyle"  :class="{flex:!vertical}">
        <div v-for="i in showList" ref="wraps" :key="i.id" class="van-overflow-hidden wraps" @click="handleClick">
          <slot :data="i.data" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>

export default {
  name: "VerticalSwipe",
  props: {
    list: {
      type: Array,
      default: () => []
    },
    autoplay: {
      type: Number,
      default: 2000
    },
    // 是否为纵向滚动
    vertical: {
      type: Boolean,
      default: true
    },
    // 是否滚动
    istrans: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      showList: [],
      timer: "",
      swipeStyle: {
        transform: "translateY(0px)",
        transition: "no"
      },
      verticalCStyle: {
        width: "auto"
      }
    };
  },
  computed: {
    getCoordinateStr(){
      return this.vertical ? "Y" : "X";
    }
  },
  watch: {
    list: {
      deep: true,
      handler() {
        this.startInt();
      }
    }
  },
  mounted() {
    this.startInt();
  },
  methods: {
    getTransform(value){
      return `translate${this.getCoordinateStr}(${value}px)`;
    },
    removeEl() {
      this.showList.push(this.showList.shift());

      this.swipeStyle.transition = "no";
      this.swipeStyle.transform =  this.getTransform(0);
    },
    serializationList(){
      this.showList = this.list.map((item, index) => {
        return {
          data: item,
          id: this.basics.isNull(item.id) ? index : item.id
        };
      });
    },
    startInt(){
      this.serializationList();
      this.$nextTick(() => {
        this.removeEventListener();
        if(this.istrans){
          this.timer =  setInterval(() => {
            /* 获取第一个元素的高度 */
            const endEl = this.$refs["vertical-container"].getElementsByClassName("wraps")[0];
            const computedStyle = endEl.getBoundingClientRect();
            this.swipeStyle = {
              transform: this.getTransform("-" + (this.vertical ? computedStyle.height : computedStyle.width)),
              transition: "transform 0.3s ease-in-out"
            };
            if(!this.vertical) this.verticalCStyle.width = (computedStyle.width * this.showList.length) + "px";
          }, this.autoplay);
        }
        /* 删除已经移动上去的元素*/
        this.$refs["vertical-swipe"].addEventListener("transitionend", this.removeEl);
        /* 移除监听*/
        this.$on("hook:beforeDestroy", () => {
          this.removeEventListener();
        });
      });
    },
    removeEventListener(){
      this.$refs["vertical-swipe"].removeEventListener("transitionend", this.removeEl);
      clearInterval(this.timer);
    },
    // 单个item的点击
    handleClick(){
      this.$emit("click");
    }
  }
};
</script>

<style scoped lang="scss">
.vertical-container_id {
  height: 100%;
  overflow: hidden;
}

.vertical-swipe {
  transform: translateY(1px);
  overflow: hidden;
}
.flex>.wraps{
  flex-shrink: 0;
}
</style>
