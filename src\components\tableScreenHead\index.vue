<!--
 * @Author: your name
 * @Date: 2021-09-02 09:47:52
 * @LastEditTime: 2021-09-02 11:36:55
 * @LastEditors: Please set LastEditors
 * @Description: 表格头部，字段的选择
 * @FilePath: \law-front\src\components\tableHead\index.vue
-->
<template>
  <div class="tableHead">
    <div class="filter-container">
      <el-checkbox  :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange">全选</el-checkbox>

      <el-checkbox-group  class="checked-wrap inline"  v-model="checkboxVal" @change="handleItem">
        <el-checkbox
          class="box"
          v-for="(item, i) in allBoxVal"
          :disabled="item.disabled"
          :label="item"
          :key="i"
        >{{item.label}}</el-checkbox>
      </el-checkbox-group>
    </div>
  </div>
</template>

<script>
export default {
  name: "TableHead",


  props: {
    column: {
      type: Array,
      default: () => [],
      desc: "列表"
    },
    defaultShowNum: {
      type: Number,
      default: 10,
      desc: "默认展示前几个"
    },
    isRest: {
      type: Boolean,
      default: false,
      desc: "是否在进入新的表格"
    }
  },
  data() {
    return {
      checkboxVal: [],
      /**
       * 当 indeterminate true ，checkAll true ,ui样式为'-'
       * 当 indeterminate true ，checkAll false ,ui样式为'-'
       * 当 indeterminate false ，checkAll true ,ui样式为'√'
       * 当 indeterminate false ，checkAll false ,ui样式无
       * **/
      isIndeterminate: false,
      checkAll: true
    };
  },
  computed: {
    allBoxVal() {
      let arr = [];

      /* isSelectCheck是否可以选择隐藏 false 不可以*/
      arr = this.column
        .filter(res => !res.tableHidden && (this.basics.isNull(res.isSelectCheck) || res.isSelectCheck))
        .map((res, i) => ({
          label: res.label,
          disabled: res.fixed // 固定哪几个个
        }));
      // 默认取前10个，已经被选择
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.checkboxVal = arr;
      // this.checkboxVal = arr.slice(0, this.defaultShowNum)
      return arr;
    }
  },
  watch: {
    isRest(val) {
      // 重新进入新的表格的时候
      if (!val) Object.assign(this.$data, this.$options.data());
    }
  },
  methods: {
    handleCheckAllChange(val) {
      this.checkboxVal = val ? this.allBoxVal : [];
      this.isIndeterminate = false;
      this.$emit("handleChangeBox", this.checkboxVal);
    },
    handleItem(val) {
      const checkedCount = val.length;
      this.checkAll = checkedCount === this.allBoxVal.length;
      this.isIndeterminate = checkedCount > 0 && checkedCount < this.allBoxVal.length;
      this.$emit("handleChangeBox", val);
    }
  }
};
</script>

<style lang="scss" scoped>
.tableHead {
  margin-bottom: 18px;

  .filter-container {
    //border: 1px solid #ebeef5;
    .checked-wrap{
      margin-left: 20px;
    }
  }
  .box {
    margin-bottom: 0;
  }

  /deep/ .el-checkbox__input.is-checked + .el-checkbox__label{
    color: #606266;
  }
}
</style>
