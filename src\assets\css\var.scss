// 配色
$primary-color: #3887F5;  // 网站主色调
$bg-color: #F3F4F8 ; // 背景色
$text-color-main: #333;  // 文字主色
$text-color-secondary: #666666; // 二级文字信息、列表页面文字
$text-color-desc: #999999; // 描述性文字
$weak-color: #DDDDDD; // 用于侧栏、内容栏、描边一级分割线
$menu-main-bg-color: #091627;  // 一级菜单背景色
$menu-secondary-bg-color: #EAEDF1;  // 一级菜单背景色
$menu-group-bg-color: #F2F2F2; // 菜单分组背景色
$error-color:#F8565D;  // 错误提示
$warning-color: #F5992C; // 警告色
$blue-color: #50B5FF; // 辅助色
$success-color: #e2f6eb; // 辅助色
$infos-color: #909399; // 辅助色

// 字体大小设置
$font-size-mini: 12px;
$font-size-small: 14px;
$font-size-normal: 16px;
$font-size-large: 18px;

/*间距*/
$distance-mini: 5px; // mini
$distance-small: 10px; // small
$distance-normal: 15px; // normal
$distance-large: 20px; // large

:export {
  menuBgColor: #ffffff;
  menuFontColor: #333333;
  menuActiveFontColor: #3887F5;
  // menuActiveFontColor: $primary-color
}
