
<template>
	<div>
		<el-upload
			:accept="accept"
			:multiple="multiple"
			:show-file-list="false"
			:on-success="handleSuccess"
			:on-error="handleError"
			:before-upload="beforeUpload"
			:action="uploadUrl"
			:headers="headers"
			:data="params"
		>
			<el-button v-root-btn="buttonPermissionsEnum.IMPORT" size="small" icon="el-icon-upload2" type="warning">
				导入数据
			</el-button>
		</el-upload>
		<!-- <label for="file" class="upload">
			<i class="el-icon-upload2"></i>
			<input type="file"   @change="changeFile" id="file" name="file" >

		</label> -->

		<!-- 展示返回的弹框 -->
		<el-dialog
			title="导入数据"
			:visible.sync="dialogTableVisible"
			:close-on-click-modal="false"
			:append-to-body='true'
		>
			<ul>
				<li v-if="backInfo.errorMsg">
					<label> 导入提示： </label>
					<i class="warning">{{ backInfo.errorMsg }}</i>
				</li>
				<li>
					<label>数据解析行数： </label>
					<i>{{ backInfo.count }}</i>
				</li>
				<li>
					<label> 导入成功行数： </label>
					<i class="success">{{ backInfo.successCount }}</i>
				</li>
				<li>
					<label> 导入失败行数： </label>
					<i class="error">{{ backInfo.failCount }}</i>
				</li>
				<li v-if="backInfo.failFilePath">
					<label>失败数据，请点击下载： </label>
					<a :href="backInfo.failFilePath">{{ backInfo.failFilePath }}</a>
				</li>
				<li v-if="backInfo.failList && backInfo.failList.length">
					<el-collapse accordion>
						<el-collapse-item name="1">
							<template slot="title">
								具体失败行数描述 <i class="el-icon-warning-outline error" />
							</template>
							<p v-for="(item, index) in backInfo.failList" :key="index">
								<label>第{{ index + 1 }}行： </label>
								<i class="error">{{ item.failMsg }}</i>
							</p>
						</el-collapse-item>
					</el-collapse>
				</li>
			</ul>
		</el-dialog>
	</div>
</template>

<script>
import { uploadCase } from "@/api/common"; // 上传文件的地址
import { getToken } from "utils/storage";
import buttonPermissionsEnum from "@/enum/buttonPermissionsEnum";
// import { createRequestSign } from 'utils/toolMethods'
export default {
  name: "ImportButton",
  computed: {
    buttonPermissionsEnum() {
      return buttonPermissionsEnum
    }
  },
  props: {
    isRefresh: {
      type: Boolean,
      default: true,
      desc: "是否请求后立即刷新表格"
    },
    uploadUrl: {
      type: String,
      default: uploadCase,
      desc: "请求地址"
    },
    params: {
      type: Object,
      default: () => ({}),
      desc: "额外参数"
    }
  },
  data() {
    return {
      accept: ".xls,.xlt,.xlsx,.xlsm",
      multiple: false,
      loading: "",
      headers: {
        token: getToken()
      },
      dialogTableVisible: false,
      backInfo: {}
    };
  },
  methods: {
    // 文件上传成功
    handleSuccess(response, file) {
      console.log(response, 999);
      if (!response.code && response.data?.count) {
        this.backInfo = response.data;
        if (this.isRefresh) { this.$store.dispatch("setIsTableRest"); }
        this.$emit("sure", true);
        this.dialogTableVisible = true;
      } else if (!response.code && response.message === "成功") {
        this.$message.success("操作成功");
        if (this.isRefresh) { this.$store.dispatch("setIsTableRest"); }
      } else if (!response.code && !response.data?.count) {
        this.$message.error(response.data?.errorMsg);
      } else {
        this.$message.error(response.message);
      }
      this.loading.close();
    },
    // 文件上传失败
    handleError(err, file, fileList) {
      // console.log(err)
      // console.log(err)
      this.loading.close();
      this.$message.error({
        type: "error",
        message: err.message || "上传文件失败"
      });
    },
    // 上传前的钩子
    beforeUpload(file) {
      this.loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });

      // return this.verification(file)
    }
  }
};
</script>

<style lang='scss' scoped>
ul {
	i {
		font-style: normal;
		&.success {
			color: $primary-color;
		}
		&.error {
			color: $error-color;
		}
		&.warning {
			color: $warning-color;
		}
	}

	a {
		color: #606266;
	}
}
</style>

