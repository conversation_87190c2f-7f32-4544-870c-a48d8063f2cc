<template>
	<div class="inputText">
		<div v-if="!isEdit" class="flex items-center">
      <el-tooltip effect="dark" :content="rowList[this.itemList.prop]" placement="top">
			<p class="truncate">{{ rowList[this.itemList.prop] }}</p>
      </el-tooltip>
			<i
				v-if="show"
				@click="editInput"
				:class="[{ 'ml-[4px]': rowList[this.itemList.prop] }]"
				class="iconfont icon-xianxingtubiao-16 cursor-pointer"
			/>
		</div>

		<div v-else>
			<el-input v-bind="itemList.renderComponentObj.props" ref="ipt" v-model.trim="text" @blur="cancel" />
			<div class="m-t-5">
				<app-button delay-click size="small" style-type="text" use-mousedown @handleMousedown="submit">保存</app-button>
				<app-button delay-click class="!p-0" size="small" type="warning" style-type="text" @click="cancel">取消</app-button>
			</div>
		</div>
	</div>
</template>

<script>
import AppButton from "components/appButton/index.vue";

export default {
  name: "InputText",
  components: { AppButton },
  props: {
    itemList: {
      type: Object,
      default: () => {},
      desc: "表格定义的字段",
    },
    rowList: {
      type: Object,
      default: () => {},
      desc: "表格返回的字段,展示的内容",
    },
  },
  data() {
    return {
      text: "",
      isEdit: false,
    };
  },
  computed: {
    show() {
      const condition = this.itemList.renderComponentObj?.showCondition;
      /**
			 * 1、生成多个条件,得到是每个条件是否能满足
			 * 2、过滤多个条件，同为true ，展示 ，有false 不展示
			 */
      if (condition) {
        condition.forEach(r => {
          r.condition = r.value.includes(this.rowList[r.filed]);
        });
        return condition.filter(r => !r.condition).length < 1;
      }
      return true;
    },
  },
  created() {
    this.text = this.rowList[this.itemList.prop];
  },
  methods: {
    // 编辑
    editInput() {
      this.isEdit = !this.isEdit;
      this.$nextTick(() => {
        this.text = this.rowList[this.itemList.prop];
        this.$refs.ipt.focus();
      });
    },
    // 保存
    submit(e) {
      console.log(e)
      event.preventDefault()
      const Api = this.itemList.renderComponentObj.request;
      // 取不一样的参数 ，作为传参
      const _data = this.itemList.renderComponentObj.params;
      const params = {};
      if (_data) {
        Object.entries(_data).map(res => {
          console.log(res, "renderComponentObj.params");
          if (this.itemList.prop === res[1]) {
            params[this.itemList.prop] = this.text;
          }
          // 如果prop被占用 ，则使用renderComponentProps
          else if (this.itemList.renderComponentProps === res[1]) {
            params[this.itemList.renderComponentProps] = this.text;
          } else {
            params[res[0]] = this.rowList[res[1]] || null;
          }
        });
      }

      console.log(params);

      if (!this.text) return this.$message.warning("请填写内容！");
      if (!Api) return;
      Api(params).then(res => {
        this.$store.dispatch("setIsTableRefresh", this.itemList.renderComponentObj?.tableName);
        this.$message.success("操作成功");
        setTimeout(() => {
          this.cancel();
        }, 1000);
      });
    },
    // 取消
    cancel() {
      // 恢复默认初始数据
      this.text = this.rowList[this.itemList.prop];

      this.isEdit = false;
    },
  },
};
</script>

<style lang="scss" scoped>
.m-l-15 {
	margin-left: 15px !important;
}

.m-t-5 {
	margin-top: 5px;
}
</style>
