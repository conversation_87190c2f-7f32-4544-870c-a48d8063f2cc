<template>
	<div class="bg-[#FFFFFF] select-wrap">
		<xy-page
			:export-option="exportOption"
			:show-export-btn="true"
			ref="page"
			:column="tableColumn"
			:request="request"
			:query="query"
		>
			<template #pageMiddleContainer>
				<div class="text-[14px] text-[#333333] mb-[16px]">
					当前跟进中：<span class="text-[#3887F5]">{{ followUpNum }}</span
					>条
				</div>
			</template>
			<template #handleButton>
				<el-table-column width="302px" fixed="right" label="操作">
					<template #default="{ row }">
						<div class="w-full flex items-center ">
							<app-button @click="handleDetail(row)" style-type="text" type="primary" size="medium"
								>详情
							</app-button>
              <app-button delay-click v-root-btn="buttonPermissionsEnum.CUSTOMER_TRANSFER" @click="handleCustomerTransfers(row)" style-type="text" type="primary" size="medium">客户转移
              </app-button>
							<app-button
                delay-click
								v-if="row.followStatus === 1"
								@click="handleEndFollowUp(row)"
								style-type="text"
								type="danger"
								size="medium"
								>结束跟进
							</app-button>
              <app-button delay-click v-if="Number(row.postExclusiveDeductNum) > 0" @click="handlePostposition(row)" style-type="text" type="primary" size="medium"
              >后置独享
              </app-button>
						</div>
					</template>
				</el-table-column>
			</template>
		</xy-page>
		<clue-detail
			@handleEndFollowUp="handleEndFollowUp"
			:detail-data="detailData"
			v-model="drawer"
		/>
    <customer-transfers
      @customerTransfers="customerTransfers"
       :dialog-visible.sync="customerTransfersPopup" />
    <!--  后置独享  -->
    <exclusive-pay :detail="detailData" :show.sync="exclusiveDialog" />
	</div>
</template>

<script>
import XyPage from "components/xy-page/index.vue";
import { tableColumn } from "./index.js";
import ClueDetail from "views/leadManagement/leadFollowUp/leadFollowUpPage/ClueDetail.vue";
import AppButton from "components/appButton/index.vue";
import { closeClueFollow, clueFollowList, exportClueFollowList, getFollowCount, lcTransferClue } from "@/api/clues";
import CustomerTransfers from "views/leadManagement/leadFollowUp/leadFollowUpPage/CustomerTransfers.vue";
import buttonPermissionsEnum from "@/enum/buttonPermissionsEnum";
import { commonConfigKey } from "@/api/common";
import ExclusivePay from "views/leadManagement/leadFollowUp/leadFollowUpPage/exclusivePay.vue";
import global from "utils/constant";

export default {
  name: "LeadFollowUpPage",
  components: { ExclusivePay, CustomerTransfers, AppButton, ClueDetail, XyPage },
  data() {
    return {
      request: {
        getListUrl: clueFollowList,
      },
      exportOption: {
        url: exportClueFollowList,
      },
      query: {
        sortType: 2,
      },
      drawer: false,
      /** 跟进中数量 */
      followUpNum: 0,
      /** 详情数据 */
      detailData: {},
      /* 客户转移弹窗*/
      customerTransfersPopup: false,
      /* 客户转移最大次数*/
      customerTransferMaxNum: 1,
      exclusiveDialog: false
    };
  },
  computed: {
    buttonPermissionsEnum() {
      return buttonPermissionsEnum;
    },
    tableColumn(){
      return [
        {
          prop: "businessId",
          label: "线索ID",
          tableHidden: true,
          search: true,
          type: global.formItemType.input,
          defaultColumnValue: this.$route.query.id
        }, ...tableColumn];
    }
  },
  created() {
    this.getCustomerTransferMaxNum();
    this.getFollowUpNum();
  },
  methods: {
    /* 获取客户转移的最大次数*/
    getCustomerTransferMaxNum(){
      commonConfigKey({
        paramName: "lc_law_firms_clue_transfer_num",
      }).then(data => {
        if(!this.basics.isNull(data)) this.customerTransferMaxNum = data;
      });
    },

    /** 查询跟进条数 */
    getFollowUpNum() {
      getFollowCount().then(res => {
        this.followUpNum = res.followCount || 0;
      });
    },
    /** 点击结束跟进 */
    handleEndFollowUp(row) {
      closeClueFollow(row.serverId).then(() => {
        this.$message.success("您已结束跟进");
        this.drawer = false;
        this.getFollowUpNum();
        this.$store.dispatch("setIsTableRefresh");
      });
    },
    /** 点击详情 */
    handleDetail(item) {
      this.detailData = item;

      this.drawer = true;
    },
    /* 点击客服转移*/
    handleCustomerTransfers(item) {
      /* 结束跟进无法转移*/
      /*     if(item.followStatus === 2){
        this.$message.error("该线索已结束跟进，无法转移");
        return;
      }*/
      /* 转移过一次就不能在转移*/
      if(item.transferNum >= this.customerTransferMaxNum){
        this.$message.error("该线索转移已达上限，无法再次转移");
        return;
      }
      this.detailData = item;
      this.customerTransfersPopup = true;
    },
    /* 客户转移*/
    customerTransfers(data){
      if(data){
        const { id } = data;
        /*
        * businessId  业务ID type=2为案源ID，type=1及时咨询问答id
        * serverId 服务Id
        * type 业务类型 业务类型（1：即时咨询，2：案源）
        * */
        lcTransferClue({
          transferStaffId: id,
          businessId: this.detailData.businessId,
          serverId: this.detailData.serverId,
          type: this.detailData.type
        }).then(() => {
          this.$message.success("转移成功");
          this.customerTransfersPopup = false;
          this.getFollowUpNum();
          this.$store.dispatch("setIsTableRefresh");
        });
      }
    },
    /* 后置独享 */
    handlePostposition(item){
      this.detailData = item;
      this.exclusiveDialog = true;
    }
  },
};
</script>

<style scoped lang="scss">

</style>
