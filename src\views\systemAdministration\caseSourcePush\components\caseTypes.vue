

<template>
<!--  <app-popup :title="'选择'+popTitle"  width="650px" :show-cancel="true" cancel-text="取消" @cancel="close" @close="close" @confirm="caseSure" :visible="dialogVisible">-->
    <div>
      <div v-if="isShowTitle" class="flex px-[24px] pb-[12px] items-center text-[14px] text-[#333333]">
        选择{{title}}
        <span class="text-[12px] text-[#999999] pl-[8px]">最多可选{{maxCheckedNum}}项</span>
      </div>
      <div class="case-lists-box">
        <el-checkbox-group class="case-item-box" v-model="checkList" @change="handleCheckBox">
          <el-checkbox v-for="(item,i) in list" :key="i" :label="item.value">{{item.label}}</el-checkbox>
        </el-checkbox-group>
      </div>
    </div>
<!--  </app-popup>-->
</template>

<script>

export default {
  name: "CaseTypes",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: () => {
        return {};
      }
    },
    request: {
      type: Function,
      default: () => Promise.resolve()
    },
    list: {
      type: Array,
      default: () => {
        return [];
      },
      desc: "案件类型和涉案金额的数组展示"
    },
    /* 弹窗类型 涉案金额  案件类型*/
    popupType: {
      type: String,
      default: "caseType"
    },
    title: {
      type: String,
      default: ""
    },
    /* 是否显示标题 */
    isShowTitle: {
      type: Boolean,
      default: true
    },
    /* 最大可选数量 */
    maxSelection: {
      type: Number,
      default: 0
    },
    /* 需不需内部验证 */
    isNeedInternalValidation: {
      type: Boolean,
      default: true
    },
  },
  data(){
    return{
      /** 选中公共值 */
      checkList: [],
      maxCheckedNum: 10,
      /** 显示案件类型的弹窗 */
      isShowCaseType: false,
      /** 案件类型 */
      goodAtType: [],
      /** 选中案件类型的值 **/
      goodAtTypeCheckedVal: [],
      /** 选中案件类型的文字 **/
      checkListText: [],
      /** 涉案金额 **/
      amountList: [],
      /** 选中涉案金额 **/
      amountListVal: [],
      /** 涉案金额 **/
      amountTextList: [],
      /** 涉案金额 **/
    };
  },
  computed: {
    /* 是不是涉案金额弹窗类型*/
    isAmountType(){
      return this.popupType === "amount";
    },
    /* 是不是案件类型弹窗类型*/
    isCaseType(){
      return this.popupType === "caseType";
    }
  },
  created() {
    // this.getGoodAtType();
    this.handleDetails();
    if(this.isCaseType){
      this.handleCase();
    }else {
      this.handleAmount();
    }
  },
  methods: {
    /** 获取详情**/
    handleDetails(){
      // 涉案金额
      if(Number(this.detail.amountGradeLimit) === 1){
        this.amountListVal = ["-1"];
      }else {
        this.amountListVal = this.detail?.amountGrades ? this.detail.amountGrades.map(r => String(r)) : [];
      }

      // 案件类型
      if(Number(this.detail.caseTypeLimit) === 1){
        this.goodAtTypeCheckedVal = ["-1"];
      }else {
        this.goodAtTypeCheckedVal = this.detail.caseTypes ? this.detail.caseTypes.map(c => String(c.typeValue)) : [];
      }
    },
    handleCase(){
      this.changeMaxSelection(10);
      this.checkList = this.goodAtTypeCheckedVal;
    },
    handleAmount(){
      this.changeMaxSelection(3);
      this.checkList = this.amountListVal;
      this.isShowCaseType = true;
    },
    // 修改最大可选数量
    changeMaxSelection(maxCheckedNum){
      this.maxCheckedNum = this.maxSelection > 0 ? this.maxSelection : maxCheckedNum;
    },
    /** 选择案件类型 **/
    handleCheckBox(val){
      // 最后一个
      if(val.indexOf("-1") === (val.length - 1) ) this.checkList = ["-1"];

      if(this.checkList.length > this.maxCheckedNum) {
        this.checkList.pop();
        return this.$message.warning("最多可选" + this.maxCheckedNum + "项");
      }
      // 第一个
      if(val.indexOf("-1") === 0 &&  val.length > 1) this.checkList.shift();
    },
    close(){
      this.$emit("update:dialogVisible", false);
    },
    /* 验证 true验证通过*/
    getVerify(){
      if(!this.isNeedInternalValidation) return true;
      if(this.basics.isArrNull(this.checkList)){
        this.$message.warning("请选择" + this.title);
        return false;
      }
      return true;
    },
    /** 确定选中案件类型 **/
    caseSure(){
      if(!this.getVerify()) return;
      const text = [];
      this.list.forEach(r => {
        if(this.checkList.includes(r.value)) {
          text.push({
            typeValue: r.value,
            typeLabel: r.label,
          });
        }
      });
      // 参数
      const data = {
        // ...this.detail,
      };
      if(this.detailId) data.id = this.detailId;

      console.log(this.checkList, this.checkList.includes("-1"));
      if(this.isCaseType){
        data.caseTypeLimit = this.checkList.includes("-1") ? 1 : 0;
        data.caseTypes = text;
        // data.type = 3;
      }else {
        // data.type = 2;
        data.amountGradeLimit = this.checkList.includes("-1") ? 1 : 0;
        data.amountGrades = this.checkList;
      }
      return data;
      // this.$emit("handleTypeText", data);
      /*    caseSourceCluesPushDetailUpdate(data).then(r => {
        console.log(r);
        this.$message.success("操作成功");
        this.close();
      });*/
    },
  }
};
</script>

<style scoped lang="scss">
.case-lists-box{
  border-radius: 8px 8px 8px 8px;
  padding: 16px 16px 0;
  margin:0 24px 16px;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #EEEEEE;
  .case-item-box{
    display: grid;
    grid-template-columns: repeat(4,1fr) ;
    justify-content: space-between;
  }
  label{
    //width: 80px;
    margin-bottom: 16px;
  }
}


</style>
