<template>
  <div>
    <app-popup
      @confirm="handleRootPopupClick"
      @close="handleRootPopupClose"
      @cancel="handleRootPopupClose"
      title="扫码支付"
      :show-cancel="false"
      width="800px"
      :visible.sync="showVisible"
      confirm-text="我已支付"
      class="tc"
    >
      <div class="code-box">
        <div class="cut-down-box" v-if="second">还剩 <i>{{second}}</i>秒支付时间</div>
        <!--  二维码    -->
        <div class="code-bg pr">
          <div class="code" id="qrcode" />
          <!--    过期      -->
          <img v-if="!second" @click="handleOverdue()" class="pa overdue cursor" src="@/assets/images/pay/<EMAIL>" />
        </div>
        <!--  价格    -->
        <div class="code-price">支付￥<span>{{priceNumber(info.rechargeAmount)}}</span>元</div>
      </div>
      <div class="line" />

      <!--   支付结果页面   -->
      <div v-if="showResult" class="pay-result">
        <div class="pay-result-con">
          <img src="@/assets/images/pay/<EMAIL>" />
          <p>支付成功</p>
          <div class="pay-result-btn">
            <el-button @click="handleLs" type="primary">前往律所管理查看充值记录</el-button>
          </div>
        </div>
      </div>
    </app-popup>
  </div>
</template>

<script>
import AppPopup from "components/appPopup/index.vue";
import QRcode from "qrcodejs2";
import { priceNumber } from "utils/toolMethods";
import { goodsListQueryOrderStatus } from "@/api/pay";
import { createOrderFun } from "components/payPop/components/createOrder";

export default {
  name: "PayResult",
  components: { AppPopup },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => {
        return {};
      }
    },
    orderInfo: {
      type: Object,
      default: () => {
        return { };
      },
      desc: "商品页面带过来得订单信息"
    },
  },
  data(){
    return{
      isShowDeductNum: false,
      second: 180,
      timer: null,

      showResult: false,
      queryTimer: null,

      codeFun: null,

      orderInfoNew: {}
    };
  },
  computed: {
    showVisible: {
      get(){
        return this.dialogVisible;
      },
      set(val){
        this.$emit("update:dialogVisible", val);
      }
    }
  },
  mounted() {
    this.cutDownFun();
    setTimeout(() => {
      this.orderInfoNew = this.orderInfo;
      this.generateCode();
    }, 300);
  },
  destroyed() {
    this.clearOrderLoop();
    this.clear();
  },
  methods: {
    priceNumber,
    handleRootPopupClick(){
      this.checkOrderFun();
    },
    handleRootPopupClose(){
      this.clearOrderLoop();
      this.clear();
      this.$emit("update:dialogVisible", false);
    },
    // 倒计时
    cutDownFun(){
      this.timer = setInterval(() => {
        if (this.second > 0) {
          this.second--;
        } else {
          this.clear();
        }
      }, 1000);
    },
    clear() {
      window.clearInterval(this.timer);
      this.timer = null;
      this.second = 0;
      this.clearOrderLoop();
    },
    // 律所管理
    handleLs(){
      this.$emit("update:dialogVisible", false);
      this.$store.commit("SET_IS_PAY_PC", false);
      this.$router.push('/lawFirmInformation');
    },
    /**
     * 查询订单
     * 支付状态[1000:待支付,1001.已支付,1002.交易关闭,2002:退款成功，4002.已失效]
     * **/
    checkOrder(){
      if(!this.orderInfoNew.orderId) return;
      this.queryTimer = setInterval(() => {
        console.log("我在查询");
        this.checkOrderFun();
      }, 4000);
    },
    checkOrderFun(){
      goodsListQueryOrderStatus({ orderId: this.orderInfoNew.orderId }).then(s => {
        console.log("查询订单状态", s);
        if(s.status === 1001) {
          this.showResult = true;
          this.clearOrderLoop();
        }else if(s.status === 4002){
          this.clear();
        }
      }).catch(r => {
        console.log("异常");
        this.clearOrderLoop();
      });
    },
    clearOrderLoop(){
      window.clearInterval(this.queryTimer);
      this.queryTimer = null;
    },
    /** 二维码生成 **/
    generateCode(info = this.orderInfo){
      const url = JSON.parse(info.payResultMsg);
      console.log(info, 999);
      if(info.payResultMsg){
        this.codeFun = new QRcode("qrcode", {
          width: "200", // 生成二维码宽度
          height: "200", // 生成二维码高度
          text: url?.qrcode_url, // 二维码地址或文本，如果是网址扫码后会跳转网址
          colorDark: "#000",
          colorLight: "#fff"
        });
      }
      //  轮询订单状态
      this.checkOrder();
    },
    /** 重新生成订单**/
    handleOverdue(){
      const data = {
        goodsId: this.orderInfoNew.goodsId,
        type: 1,
        payType: this.orderInfoNew.payType
      };
      createOrderFun(data).then(r => {
        this.orderInfoNew = r;
        this.cutDownFun();
        this.second = 180;
        // 二维码重新生成
        const url = JSON.parse(r.payResultMsg);
        this.codeFun.makeCode(url.qrcode_url);

        //  轮询订单状态
        this.checkOrder();
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.code-box{
  background: #F5F5F7;
  border-radius: 8px 8px 8px 8px;
  margin: 24px;
  padding: 24px;
  .code-price{
    font-weight: normal;
    font-size: 24px;
    color: #CE6269;
    margin-top: 24px;
    span{
      font-size: 34px;
    }
  }
}
.cut-down-box{
  font-weight: 400;
  font-size: 16px;
  color: #333333;

  i{
    width: 34px;
    height: 34px;
    background: #FFFCFA;
    border-radius: 4px 4px 4px 4px;
    display:inline-block;
    text-align: center;
    line-height: 34px;
    font-weight: 600;
    font-size: 16px;
    color: #CE6269;
    margin: 0 10px;
    font-style: normal;
  }
}
.code-bg{
  width: 224px;
  height: 224px;
  background:url("~@/assets/images/pay/code-bg.png") no-repeat ;
  background-size: 100%;
  margin:16px auto;
  img{
    width: 200px;
    height: 200px;
    padding: 11px;
  }
}
.line{
  height: 1px;
  background: #EEEEEE;
}
.overdue{
  left: 0;
  right: 0;
  top: 0px;
}
.code{
  ::v-deep img{
    padding: 11px !important
  }
}
.pay-result{
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  background: #FFFFFF;
  bottom: 0;
  border-radius: 8px 8px 8px 8px;
  .pay-result-con{
    background: url("~@/assets/images/pay/<EMAIL>") no-repeat ;
    background-size: contain;
    width: 100%;

    p{
      font-weight: 500;
      font-size: 34px;
      color: #333333;
    }

    img{
      width: 210px;
      margin-top: 15%;
    }
    .pay-result-btn{
      border-top: 1px solid #EEEEEE;
      padding-top: 8px;
      position: absolute;
      left: 0;
      right: 0;
      bottom: 24px;
    }
  }
}
</style>
