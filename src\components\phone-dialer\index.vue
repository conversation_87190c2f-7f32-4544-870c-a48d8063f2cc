<template>
  <app-button type="border"  v-bind="$attrs" :disabled="disabled" @click="callOut">
    <slot>拨打</slot>
  </app-button>
</template>

<script>
import AppButton from "components/appButton/index.vue";
import { lawyerCallOut } from "@/api/business.js";

export default {
  name: "PhoneDialer",
  components: { AppButton },
  inheritAttrs: false,
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      disabled: false
    };
  },
  watch: {
    "data.caseSourceServerV2Id": {
      handler() {
        this.disabled = false;
      }
    }
  },
  methods: {
    /* 拨打电话 外呼*/
    callOut() {
      if (!this.data.caseSourceServerV2Id) return false;
      lawyerCallOut({
        caseSourceServerV2Id: this.data.caseSourceServerV2Id
      }).then(() => {
        this.disabled = true;
        this.$emit("callOut");
      });
    }
  }
};
</script>

<style scoped lang="scss">

</style>
