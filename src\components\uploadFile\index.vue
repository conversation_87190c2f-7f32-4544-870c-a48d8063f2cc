<template>
  <div class="uloadFile">

    <!-- 文件列表展示 -->
    <div class="file-list" v-if="fileList.length > 0">
        <ul class="lists">
            <li class="flex"  v-for="(item,index) in fileList" :key="index">
                <label class="line1"> <i class="el-icon-document" />{{getName(item)}} </label>
                <div class="tools">
                    <i class="el-icon-circle-check success-file" />
                    <i class="el-icon-close delete-file" @click="handleRemove(item,index)" />
                </div>
            </li>
        </ul>
    </div>
    <el-upload
        v-if="limit > fileList.length"
        :accept='accept'
        :multiple="multiple"
        :limit="limit"
        :show-file-list="false"
        :on-success="handleSuccess"
        :on-error="handleError"
        :before-upload="beforeUpload"
        class="editor-slide-upload"
        :action="actionUrl"
      >
        <el-button size="small" type="primary">
          点击上传
        </el-button>
      </el-upload>

      <!-- 用法： <UploadTest v-model="postForm.image_uri"   :value.sync='postForm.image_uri' /> -->
  </div>
</template>
<script>
import { uplodFile } from "@/api/common"; // 上传文件的地址
export default {
  name: "UloadImage",
  props: {
    value: {
      type: String,
      default: "",
      desc: "获取值"
    },
    width: {
      type: String,
      default: "100px",
      desc: "宽度"
    },
    height: {
      type: String,
      default: "100px",
      desc: "高度"
    },
    multiple: {
      type: Boolean,
      default: false,
      desc: "是否多选"
    },
    accept: {
      type: String,
      default: ".doc,.docx,.jpg,.png,.gif,.jpeg,.webp,.ico,.APK,.txt,.xlsx,.mp4,.pdf",
      desc: "文件类型"
    },
    limit: {
      type: Number,
      default: 1,
      desc: "限制传输数量"
    },
    formMaxSizeM: {
      default: 10,
      type: Number,
      desc: "限制图片大小(单位M)"
    },
    formMaxSizekb: {
      default: 100,
      type: Number,
      desc: "限制图片大小(单位kb)"
    },
    actionUrl: {
      type: String,
      default: uplodFile,
      desc: "文件上传地址"
    }
  },
  data() {
    return {
      uplodFile,
      previewList: [],
      showUpload: true,
      loading: "",
      txtResult: ""
    };
  },
  computed: {
    //  当传来的值变化时，跟着变化
    fileList() {
      let _data = [];
      if (this.value) _data = this.value.split(",");
      return _data;
    }
  },
  methods: {
    // 取文件的名字
    getName(val) {
      const str = val.split("/");
      return str[str.length - 1];
    },
    //   更改值
    emitInput(val) {
      this.$emit("input", val);
    },
    // 文件上传成功
    handleSuccess(response, file) {
      let _dataStr = "";
      if (!response.code) {
        if (this.fileList.length > 0) {
          this.fileList.push(response.data);
          _dataStr = this.fileList.toString();
        } else {
          _dataStr = response.data;
        }
        this.emitInput(_dataStr);

        setTimeout(rel => {
          this.loading.close();
          this.$message.success("上传文件成功！");
        }, 2000);
      } else {
        this.loading.close();
        this.$message.error(response.message);
      }
    },
    // 文件上传失败
    handleError(err, file, fileList) {
      this.loading.close();
      this.$message.error({
        type: "info",
        message: err.message || "上传文件失败"
      });
    },
    // 文件移除
    handleRemove(file, index) {
      this.fileList.splice(index, 1);
      const _dataStr = this.fileList.toString();
      this.emitInput(_dataStr);
    },
    // 验证文件的大小
    verification(file) {
      if (file.size / 1024 / 1024 > this.formMaxSizeM) {
        this.$message({
          message: `上传文件大小不能超过${this.formMaxSizeM}M!`,
          type: "warning"
        });
        this.loading.close();
        return false;
      }
      return true;
    },

    // 上传前的钩子
    beforeUpload(file) {
      this.loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });

      // console.log(file)
      return this.verification(file);
      // return new Promise((resolve, reject) => {
      //      resolve(true)
      //      reject(false)
      // })
    },

    // 预览图片
    handlePreview(file, index) {
      this.$refs.previewImage[index].showViewer = true;
    }
  }

};
</script>

<style lang="scss" scoped>

  .lists{
      font-size: 14px;

      li{
          cursor: pointer;

          label{
              display: inline-block;
              width: 85%;
          }
      }

      li:hover{
          .success-file{
              display: none;
          }
          .delete-file{
                display: inline-block !important;
          }
      }

      i{
          margin-right: 5px;
      }
      .delete-file{
          display: none;
      }
  }
  .uploadBtn{
      background: #f5f5f5;
      line-height: 100px;
      font-size: 20px;
      border: 1px dotted #ddd;
      color: #333333;
  }
</style>
