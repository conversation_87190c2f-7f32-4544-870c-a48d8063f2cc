<template>
  <app-drawer size="720px" :visible.sync="visible" title="办案工具">
    <div class="p-[24px] pb-0">
      <app-select v-if="visible" :default-value="selectDefaultValue" @change="selectChange" :select-list="tabBarList" />
    </div>
    <component ref="componentRef" :is="componentName" />
    <template #footer>
      <div class="border-0 pt-[8px] pb-[24px] pr-[24px] flex items-center justify-end border-t-[1px] border-solid border-[#EEEEEE]">
        <app-button @click="reset" type="info">重置</app-button>
        <app-button delay-click  :delay="2000" @click="calculation" class="ml-[16px]">计算</app-button>
      </div>
    </template>
  </app-drawer>
</template>
<script>
import AppSelect from "components/AppSelect/index.vue";
import AppDrawer from "components/AppDrawer/index.vue";
import AppButton from "components/appButton/index.vue";

export default {
  name: "CalculatorDrawer",
  components: {
    AppButton,
    LaborArbitration: () => import("components/CalculatorDrawer/components/labor-arbitration/index.vue"),
    Civil: () => import("components/CalculatorDrawer/components/civil/index.vue"),
    AppDrawer,
    AppSelect
  },
  data() {
    return {
      componentName: "",
      tabBarList: [
        {
          label: "法临律师费计算器",
          value: "1",
          componentName: "Civil",
        },
        {
          label: "劳动仲裁计算器",
          value: "2",
          componentName: "LaborArbitration",
        },
      ],
      selectDefaultValue: "1"
    };
  },
  computed: {
    calculatorConfig(){
      return this.$store.getters.calculator;
    },
    visible: {
      get(){
        return this.calculatorConfig.isShow;
      },
      set(val){
        this.$store.commit("SET_CALCULATOR", {
          isShow: val
        });
      }
    }
  },
  watch: {
    visible(){
      if(this.visible){
        const index = this.calculatorConfig.showIndex;
        this.selectDefaultValue = this.tabBarList[index].value;
        this.selectChange(this.tabBarList[index]);
      }else{
        this.componentName = "";
      }
    }
  },
  methods: {
    selectChange({ componentName }){
      this.componentName = componentName;
    },
    reset(){
      if(this.$refs.componentRef){
        this.$refs.componentRef.resetForm && this.$refs.componentRef.resetForm();
      }
    },
    calculation(){

      if(this.$refs.componentRef){
        this.$refs.componentRef.handleCalculate && this.$refs.componentRef.handleCalculate();
      }
    }
  }
};
</script>

<style scoped lang="scss">

</style>
