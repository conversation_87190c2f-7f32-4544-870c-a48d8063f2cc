<template>
  <ul :class="`xy-list-box ${direction}`">
    <template v-for="(list,i) in listData">
      <li v-if="!list.hide" :key="i" :style="`width: calc(100% / ${rowNum})`">
        <div class="label">{{ list.label }}</div>
        <div class="content">
          <span :class="list.className">{{ list.value || '-' }}</span>
          <span class="handle" v-if="list.buttonText && list.value" @click="handleClick(list)">{{ list.buttonText }}</span>
        </div>
      </li>
    </template>

    <!-- 占位   -->
    <template v-if="emptyNum">
      <li v-for="(item, index) in emptyNum" :key="`${index}-only`" :style="`width: calc(100% / ${rowNum})`">
        <div class="label" />
        <div class="content" />
      </li>
    </template>
  </ul>
</template>

<script>
export default {
  name: "Index",
  props: {
    direction: {
      type: String,
      default: "horizontal",
      desc: "排列方式：horizontal, vertical"
    },
    listData: {
      type: Array,
      default: () => {
        return [];
      }
    },
    rowNum: {
      type: [Number, String],
      default: 4,
      desc: "每行显示几个数据"
    }
  },
  data() {
    return {
      emptyNum: 0 // 空行占位数
    };
  },
  watch: {
    listData: {
      handler(val) {
        let hideNum = 0;
        if (val.length) {
          val.forEach(item => {
            if (item.hide) {
              hideNum++;
            }
          });
        }
        const num = (this.listData.length - hideNum) % this.rowNum;
        if (num) {
          this.emptyNum = this.rowNum - num;
        } else {
          this.emptyNum = 0;
        }
      },
      deep: true
    }
  },
  methods: {
    handleClick(list) {
      this.$emit("buttonClick", list);
    }
  }
};
</script>

<style lang="scss" scoped>
.xy-list-box.horizontal{
  border-bottom: 1px solid $weak-color;
  border-left: 1px solid $weak-color;
  display: flex;
  flex-wrap: wrap;
  li{
    width: 50%;
    .label{
      padding: 0 $distance-normal;
      height: 44px;
      line-height: 44px;
      text-align: center;
      box-sizing: border-box;
      background: $menu-group-bg-color;
      border-top: 1px solid $weak-color;
      border-right: 1px solid $weak-color;
      border-bottom: 1px solid $weak-color;
    }
    .content{
      padding: 0 $distance-normal;
      flex: 1;
      min-height: 44px;
      display: flex;
      justify-content: center;
      align-items: center;
      box-sizing: border-box;
      border-right: 1px solid $weak-color;
    }
  }
}
.xy-list-box.vertical{
  border-left: 1px solid $weak-color;
  border-top: 1px solid $weak-color;
  border-right: 1px solid $weak-color;
  display: flex;
  flex-wrap: wrap;
  li{
    width: calc(100% / 3);
    display: flex;
    .label{
      padding: 0 $distance-normal;
      width: 150px;
      height: 44px;
      line-height: 44px;
      text-align: center;
      box-sizing: border-box;
      background: $menu-group-bg-color;
      border-right: 1px solid $weak-color;
      border-bottom: 1px solid $weak-color;
    }
    .content{
      margin-left: -1px;
      padding: 0 $distance-normal;
      flex: 1;
      height: 44px;
      line-height: 44px;
      text-align: left;
      box-sizing: border-box;
      border-right: 1px solid $weak-color;
      border-bottom: 1px solid $weak-color;
    }
    &:nth-child(3n + 1) {
      .label{
        border-right: 1px solid $weak-color;
      }
    }
  }
}

.xy-list-box{
  li{
    .content{
      .handle{
        margin-left: $distance-small;
        color: $blue-color;
        cursor: pointer;
      }
      .dot{
        position: relative;
        &:before{
          content: '';
          position: absolute;
          left: -10px;
          top: 7px;
          width: 6px;
          height: 6px;
          border-radius: 100%;
          background: $error-color;
        }
      }
    }
  }
}
</style>
