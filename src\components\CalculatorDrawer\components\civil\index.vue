<template>
  <div>
    <workbench-card-title>计算类型</workbench-card-title>
    <el-form
        class="page-form"
        label-position="left"
        ref="caculatorItemForm"
        :model="formData"
        :rules="ruleConfig"
        label-width="120px"
    >
      <div class="flex pt-[16px] px-[24px] flex-wrap justify-between">
        <el-form-item
            v-if="!criminalFormShow"
            label="收费方式"
            class="w-full"
            prop="chargeType">
          <el-select
              @change="handleChange($event, 'chargeType')"
              class="w-full"
              v-model="formData.chargeType"
              placeholder="请选择"
          >
            <el-option
                v-for="item in chargeTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
            v-if="!riskFormShow"
            label="案件类型"
            class="w-full"
            prop="caseType">
          <el-select
              @change="handleChange($event, 'caseType')"
              class="w-full"
              v-model="formData.caseType"
              placeholder="请选择"
          >
            <el-option
                v-for="item in caseTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
            v-if="!riskFormShow"
            class="w-full"
            label="成案地点"
            prop="regionCodes">
          <region
              class="w-full city"
              v-model="formData.regionCodes"
              :clearable="true"
              :api="areaGetPlatformArea"
              :get-names="true"
              @getregionsname="getRegionsName"
          />
        </el-form-item>
        <el-form-item
            class="w-full"
            v-if="criminalFormShow&&formData.regionCode"
            label="案件阶段"
            prop="caseStage">
          <el-select
              @change="handleChange($event, 'caseStage')"
              class="w-full"
              v-model="formData.caseStage"
              placeholder="请选择"
          >
            <el-option
                v-for="item in caseStageList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item
            class="w-full"
            v-if="!criminalFormShow"
            label="标的金额"
            prop="amount">
          <el-input
              class="w-full"
              v-model="formData.amount"
              placeholder="请输入标的金额"
              maxlength="9"
          ><template #append>元</template></el-input>
        </el-form-item>
      </div>
    </el-form>
    <workbench-card-title class="pt-[24px]">计算结果</workbench-card-title>
    <div class="px-[24px] pt-[16px]">
      <div class="relative bg-[linear-gradient(_94deg,_#F2F9FE_0%,_#BDE0FC_100%)]">
        <div class="p-[16px]">
          <p class="text-[16px] text-[#333333]">
            预估律师费用
          </p>
          <div class="flex items-center pt-[8px]">
            <p class="font-[600] text-[14px] text-[#3887F5] !pr-[8px]"  v-if="showCalculateResult&&riskFormShow">不超过</p>
            <div class="font-[600] text-[18px] text-[#3887F5] flex items-center">
              ¥
              <p class="text-[24px]">{{ showCalculateResult? calculateResult : 0 }}</p>
            </div>
          </div>
        </div>
        <img class="w-[220px] absolute top-0 right-0  h-full" src="@/components/CalculatorDrawer/components/civil/imgs/<EMAIL>" alt="" />
      </div>
    </div>
    <div v-if="showCalculateResult">
      <workbench-card-title class="pt-[48px]">{{chargeTypeLabel}}收费标准</workbench-card-title>
      <div class="px-[24px] py-[16px]">
        <p class="text-[14px] !leading-[24px] text-[#666666]"
           v-for="(i,index) in rulesText"
           :key="index">
          {{ i }}
        </p>
        <p class="text-[12px] text-[#999999] !pt-[16px]">注：此计算结果仅供参考，实际收费标准可能因地区、案件复杂程度等因素而异。</p>
      </div>
    </div>
  </div>
</template>

<script>
import WorkbenchCardTitle from "views/workbenchManagement/workbench/components/CardTitle.vue";
import Region from "components/regionCascader/index.vue";
import { areaGetPlatformArea } from "@/api/common";
import { lawyerFeeCalculatorListData } from "@/api/caculator";
import { calculateLawyerFee } from "utils/calculation";
import { formatAmount } from "utils/toolMethods";
import { buriedPoint } from "@/api/buriedPoint";

export default {
  name: "Civil",
  components: { Region, WorkbenchCardTitle },
  data() {
    return {
      chargeTypeList: [
        {
          label: "按标的额比例收费",
          value: "1"
        },
        {
          label: "风险代理收费",
          value: "2"
        }
      ],
      caseTypeList: [
        {
          label: "民事案件",
          value: "1"
        },
        {
          label: "刑事案件",
          value: "2"
        }
      ],
      caseStageList: [],
      formData: {
        // 收费方式
        chargeType: "",
        // 案件类型
        caseType: "",
        // 案件阶段
        caseStage: "",
        // 标的金额
        amount: "",
        // 所在地区
        regionCode: "",
        // 省份
        provinceCode: "",
        regionCodes: []
      },
      ruleConfig: {
        chargeType: [{
          required: true,
          message: "请选择收费方式",
          trigger: "blur"
        }],
        caseType: [{
          required: true,
          message: "请选择案件类型",
          trigger: "blur"
        }],
        regionCodes: [{
          required: true,
          message: "请选择所在地区",
          trigger: "blur"
        }],
        caseStage: [{
          required: true,
          message: "请选择案件阶段",
          trigger: "blur"
        }],
        amount: [{
          required: true,
          message: "请输入标的金额",
          trigger: "blur"
        },
        {
          // 整数
          pattern: /^\d+$/,
          message: "标的金额请输入整数",
          trigger: "change"
        }]
      },
      //   规制文案
      rulesText: [],
      //   计算结果
      calculateResult: null
    };
  },
  computed: {
    // 民事选择from的显示
    civilFormShow() {
      return this.formData.chargeType === "1" && this.formData.caseType === "1";
    },
    // 风险收费from的显示
    riskFormShow() {
      return this.formData.chargeType === "2";
    },
    // 刑事案件from的显示
    criminalFormShow() {
      return this.formData.chargeType === "1" && this.formData.caseType === "2";
    },
    // 是否展示计算结果
    showCalculateResult() {
      return !this.basics.isNull(this.calculateResult);
    },
    chargeTypeLabel() {
      return this.chargeTypeList.find(
        (item) => item.value === this.formData.chargeType
      )?.label;
    },
  },
  mounted() {
    buriedPoint({
      code: "LAW_CLOUD_ATTORNEYS_FEES_CALCULATOR_PAGE"
    });
  },
  methods: {
    areaGetPlatformArea,
    handleChange(data, key) {
      console.log(key);
      if (key === "caseType") {
        // 如果是刑事案件 并且选择了城市  就调用配置 实时获取案件阶段
        this.getCriminalLawyerFeeCalculatorListData();
      }
      //   如果是请选择收费方式 就清空一次城市
      if (key === "chargeType") {
        this.formData.regionCode = "";
        this.formData.regionCodes = [];
        this.formData.provinceCode = "";
        this.formData.caseType = "";
      }
    },
    // 获取律师费计算器列表数据
    getLawyerFeeCalculatorListData() {
      return lawyerFeeCalculatorListData(this.formData).then(res => {
        return res;
      });
    },
    getCriminalLawyerFeeCalculatorListData() {
      if (this.criminalFormShow && this.formData.regionCode) {
        console.log("111111111");
        this.formData.caseStage = "";
        return this.getLawyerFeeCalculatorListData().then((res) => {
          this.caseStageList = (res || []).map((item) => ({
            ...item,
            label: (item.labelName || "").replace(/：+/g, ":").trim().split(":")[0] || "",
            value: String(item.id)
          }));
        });
      }
    },
    /* 城市选择*/
    getRegionsName(data) {
      const { value = [], name = [] } = JSON.parse(data);
      /* provinceCode	是	Integer	省编码
regionCode	是	Integer	地区编码*/
      const [provinceCode = "", regionCode = ""] = value;
      const [provinceName = "", regionName = ""] = name;
      this.formData = {
        ...this.formData,
        provinceCode,
        regionCode,
        provinceName,
        regionName,
      };
      // 如果是刑事案件 并且选择了城市  就调用配置 实时获取案件阶段
      this.getCriminalLawyerFeeCalculatorListData();
    },
    resetForm() {
      const data = this.$options.data();
      this.formData = data.formData;
      this.labels = data.labels;
      this.calculateResult = data.calculateResult;
    },
    // 计算
    handleCalculate() {
      this.$refs.caculatorItemForm.validate((valid) => {
        if (valid) {
          this.calculateLawyerFee();
        }
      });
    },
    // 计算律师费
    calculateLawyerFee() {
      // 获取规则
      this.getLawyerFeeCalculatorListData().then(res => {
        // 规则.
        const rules = res || [];
        this.rulesText = rules.map((item, index) => `${index + 1}.${item.labelName}`);
        const calculateResult = calculateLawyerFee({
          amount: this.formData.amount,
          rules,
          ...(this.criminalFormShow ? { id: this.formData.caseStage } : {})
        });
        this.calculateResult = calculateResult.map(item => formatAmount(item)).join("~");
      });
    },
  },
};
</script>

<style scoped lang="scss">

.city {
  ::v-deep {
    .el-cascader {
      width: 100%;
    }
  }
}

.page-form {
  ::v-deep {
    .el-form-item--small.el-form-item {
      margin-bottom: 24px;
    }
  }
}
</style>
