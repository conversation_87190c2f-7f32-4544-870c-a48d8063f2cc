<template>
  <xy-page
    ref="page"
    :column="tableColumn"
    :request="request"
    :query="query"
  >
    <template #handleButton>
      <el-table-column  width="120px" fixed="right" label="操作">
        <template #default="{ row }">
          <div class="w-full flex items-center ">
            <app-button @click="handleDetail(row)" style-type="text" type="primary" size="medium"
            >前往跟进
            </app-button>
          </div>
        </template>
      </el-table-column>
    </template>
  </xy-page>
</template>

<script>
import XyPage from "components/xy-page/index.vue";
import AppButton from "components/appButton/index.vue";
import { clueFollowList } from "@/api/clues";
import {
  tableColumn
} from "views/marketingManagement/automaticallyRobTheSourceOfTheCase/components/automaticRobberySourceRecord/index";
import { turnToClueFollowUp } from "@/utils/turnPage";
import { lcInstitutionStaffList } from "@/api/system";
import global from "utils/constant";


export default {
  name: "AutomaticRobberySourceRecord",
  components: { AppButton, XyPage },
  data() {
    return {
      request: {
        getListUrl: (data) => clueFollowList({
          serverWay: 10,
          ...data
        }),
      },
      query: {
        sortType: 2,
      },
    };
  },
  computed: {
    tableColumn() {
      return [...tableColumn, ...(this.$store.getters["isAdministrator"] ? [{
        prop: "staffId",
        label: "抢单人",
        search: true,
        tableHidden: true,
        type: global.formItemType.select,
        syncData: {
          url: lcInstitutionStaffList,
          value: "id",
          label: "userName",
        },
      }] : [])];
    }
  },
  methods: {
    /** 点击详情 */
    handleDetail(item) {
      turnToClueFollowUp({
        id: item.businessId
      });
    },
  }
};
</script>

<style scoped lang="scss">

</style>
