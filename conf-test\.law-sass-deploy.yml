#引入其他项目预先定义的公共模板
include:
  - project: all/flzx/cicd-devops/common-template
    ref: master
    file:
      - 'test/imlaw-front-for-k8s.yml'


#定义执行步骤
stages:
  - npmBuild
  - dockerBuild
  # - qtfk8sDeploy
  - k8sDeploy

#第一步 npm打包构建
npmBuild:
  stage: npmBuild
  environment: test  
  rules:
    - if: '$CI_COMMIT_BRANCH == "test"'
  before_script:
    - |- 
       rm -rf dist   #清除上次构建的缓存,避免出现未知问题
       regex="[1-9].[0-9].[0-9]"
       if [[ ${BuildVersion} =~ $regex ]];then
         echo "传入版本号值为:${BuildVersion},符合版本号规则"
       else
          BuildVersion=""
          echo "传入版本号不符合规则,默认从上版本取"
       fi      

       echo "获取到的版本号:${BuildVersion}"
       if [ -z "${BuildVersion}" ];then
          echo "未输入版本号,从上版本获取"
          exitsVersion=`cat /home/<USER>/builds/.version/test-web.env |grep ${ServiceName}|awk -F ':' '{print $2}'`
          if [ ! -z "$exitsVersion" ];then
            BuildVersion=$exitsVersion
            echo "上次构建版本号:${exitsVersion}"
          else
            echo "未正确输入版本号,也未正确从版本号获取"
            exit 555
          fi    
       fi
       jsonTxt=`cat package.json|grep "build:test"`
       echo "文件匹配到的是： $jsonTxt"
       envTxt=`echo $jsonTxt|sed "s/version=.*/version=${BuildVersion}\"\,/1"`
       echo "envTxt: $envTxt"
       sed -i "s/${jsonTxt}/${envTxt}/g" package.json  
       echo "package.json 最终构建环境和版本号打印如下:"
       cat package.json|grep "build:test"
       sed -i "s/${ServiceName}:.*/${ServiceName}:${BuildVersion}/1" /home/<USER>/builds/.version/test-web.env

  variables:
    #DEPOLY_ENVIRONMENT: "test"
    # NPM_INSTALL_CMD: "npm install --unsafe-perm"
    NPM_INSTALL_CMD: "npm install --unsafe-perm --registry https://registry.npmjs.org"
    BUILD_CMD: "npm run build:test"
  extends: .front-test-build
  #when: manual

  #缓存构建文件夹              
  cache:
    # untracked: true
    paths:
    - node_modules/
    - dist/ 



#第二步docker镜像构建
dockerBuild:
  stage: dockerBuild
  needs: [npmBuild]
  environment: test
  rules:
    - if: '$CI_COMMIT_BRANCH == "test"'  
  extends: .front-test-dockerBuild
  #when: manual

#获取上一步缓存的构建文件夹
  cache:
    paths:
      - dist/ 
    policy: pull



# #第三步 qtf-k8s文件部署
# qtfk8sDeploy:
#   stage: qtfk8sDeploy
#   needs: [dockerBuild]
#   environment: test
#   variables:
#     DEPLOY_ENV: "qtf测试"  
#   rules:
#     - if: '$CI_COMMIT_BRANCH == "test"' 
#   extends: .front-qtf-test-k8sDeploy
#   #when: manual


#第四步 k8s文件部署
k8sDeploy:
  stage: k8sDeploy
  needs: [dockerBuild]
  environment: test
  rules:
    - if: '$CI_COMMIT_BRANCH == "test"' 
  extends: .front-test-k8sDeploy
  # when: manual
