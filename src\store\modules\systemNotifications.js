

export default {
  state: {
    /* 首次登录新手引导弹窗*/
    firstLoginGuide: false,
    /* 新手引导弹窗*/
    guide: false,
    /* 安源不足提示弹窗*/
    insufficientBalance: false,
    /* 通知数量*/
    noticeCounts: {
      /* 案源推送数*/
      caseSourcePush: 0,
      /* 成单喜报数*/
      goodNewsUnreadNum: 0,
      /* 更新公告数*/
      noticeUnreadNum: 0,
      /* 系统消息数*/
      sysNotificationNum: 0,
    },
    /* 案源数量更新提示*/
    caseSourcePushToast: {
      /* 是否显示*/
      show: false,
      /* 案源数量文案*/
      msg: "",
    }
  },
  getters: {
    /* 首次登录新手引导弹窗*/
    getFirstLoginGuide: state => state.firstLoginGuide,
    /* 新手引导弹窗*/
    getGuide: state => state.guide,
    /* 安源不足提示弹窗*/
    getInsufficientBalance: state => state.insufficientBalance,
    /* 通知数量*/
    getNoticeCounts: state => state.noticeCounts,
    /* 案源数量更新提示*/
    getCaseSourcePushToast: state => state.caseSourcePushToast,
  },
  mutations: {
    /* 首次登录新手引导弹窗*/
    SET_FIRST_LOGIN_GUIDE(state, firstLoginGuide){
      state.firstLoginGuide = firstLoginGuide;
    },
    /* 新手引导弹窗*/
    SET_GUIDE(state, guide){
      state.guide = guide;
    },
    /* 安源不足提示弹窗*/
    SET_INSUFFICIENT_BALANCE(state, insufficientBalance){
      state.insufficientBalance = insufficientBalance;
    },
    /* 通知数量 赋值都是异步的  所以有可能其他地方在减 直接赋值要把数量减去一下*/
    SET_NOTICE_COUNTS(state, noticeCounts = {}){
      Object.entries(noticeCounts).forEach(([key, value]) => {
        const count = state.noticeCounts[key];
        /* 可能是负数  以为是异步的 所有 当前获得的数量 去掉负数*/
        state.noticeCounts[key] = count < 0  ? value + count : value;
      });
    },
    /* 通知数量减少*/
    REDUCE_NOTICE_COUNTS(state, key){
      state.noticeCounts[key] -= 1;
    },
    /* 案源数量更新提示状态*/
    SET_CASE_SOURCE_PUSH_TOAST_SHOW(state, show){
      state.caseSourcePushToast.show = show;
    },
    /* 案源数量更新提示文案*/
    SET_CASE_SOURCE_PUSH_TOAST_MSG(state, msg){
      state.caseSourcePushToast.msg = msg;
    },
  },
  actions: {
  }
};
