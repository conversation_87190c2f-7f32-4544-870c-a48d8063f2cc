<template>
	<div>
		<xy-page
			@handleAdd="addPopup"
			show-add-btn-text="新增员工"
			:column="column"
			:request="request"
			show-add-btn
		>
			<template #pageMiddleContainer>
				<div class=" pb-[16px] flex justify-between">
					<div class="flex items-center">
						<p class="flex items-center text-[14px] text-[#333333]">
							当前系统使用人员：<span class="text-[#3887F5]">{{ staffInfo.staffCount }}</span>
						</p>
						<div class="flex items-center text-[14px] pl-[7px] text-[#333333]">
							子账号可添加上限<span class="text-[#3887F5]">{{ staffInfo.subAccountMaxCount }}</span
							>人（不包含管理员）
						</div>
					</div>
					<!--          <app-button icon="icon-zengjia" @click="addPopup">新增员工</app-button>-->
				</div>
			</template>
			<template slot="handleButton">
				<el-table-column fixed="right" width="265px" label="操作">
					<template slot-scope="{ row }">
						<edit-button
							:disabled="row.isAdmin === 1"
							:list="staffingState"
							:request="switchReq"
							:data="row"
							switch-key="status"
							type="danger"
						/>
						<app-button style-type="text" v-click-await:[row]="handleClick">编辑</app-button>
						<app-button
							:disabled="row.isAdmin === 1"
							style-type="text"
							@click="handleRootClick(row)"
							>权限配置</app-button
						>
						<app-button
							v-if="row.certStatus === 0"
							style="padding: 0"
							style-type="text"
							@click="turnToLawyerAuth({ data: row })"
							>立即认证</app-button
						>
					</template>
				</el-table-column>
			</template>
		</xy-page>
		<!-- 详情   -->
		<add-content
			:form-detail="rowParams"
			v-if="dialogVisible"
			:dialog-visible.sync="dialogVisible"
			:add-upate-url="request"
			:option="popupStyle"
			:table-data="column"
		>
			<template #footerBtn>
				<div class="text-[14px] h-full text-[#EB4738] flex items-center">
					注意：不配置上限，则默认员工使用无上限哦，请谨慎配置
				</div>
			</template>
		</add-content>
		<!--权限配置    -->
		<!--    <root-popup v-if="rootDialogVisible" :row="rowParams" :dialog-visible.sync="rootDialogVisible" />-->
		<!--弹窗提示新增用户    -->
		<app-popup
			@confirm="handleRootPopupClick"
			@close="handleRootPopupClose"
			@cancel="handleRootPopupClose"
			title="温馨提示"
			show-cancel
			width="400px"
			:visible.sync="isShowDeductNum"
			confirm-text="立即前往"
		>
			<div>
				<div class="ml-[8px] py-[16px] px-[24px] text-[14px] text-[#666666] flex items-center">
					<i
						class="text-[24px] w-[24px] h-[24px] mr-[8px] iconfont icon-xianxingtubiao-3 text-[#ff851f]"
					/>
					检测到该账号还未配置权限，请立即前往进行权限配置吧！
				</div>
			</div>
		</app-popup>
	</div>
</template>

<script>
import XyPage from "components/xy-page/index.vue";
import AddContent from "components/addUpateCompoents/index.vue";
import { column } from "views/systemAdministration/staffing/index.js";
import AppButton from "components/appButton/index.vue";
import {
  lcInstitutionStaffDetailByStaffId,
  lcInstitutionStaffGetInstitutionStaffInfo,
  lcInstitutionStaffInsert,
  lcInstitutionStaffPage,
  lcInstitutionStaffUpdate,
  lcInstitutionStaffUpdateStatus,
} from "@/api/system.js";
import global from "utils/constant.js";
import EditButton from "components/xy-buttons/editButton.vue";
import { staffingState } from "@/enum/staffing.js";
import AppPopup from "components/appPopup/index.vue";
import { turnToLawyerAuth } from "utils/turnPage";

export default {
  name: "Staffing",
  components: { AppPopup, EditButton, AppButton, AddContent, XyPage },
  data() {
    return {
      isShowDeductNum: false,
      /* 详情的数据*/
      rowParams: null,
      /* 编辑弹窗*/
      dialogVisible: false,
      /* 菜单权限弹窗*/
      // rootDialogVisible: false,
      popupStyle: {
        title: "新增/编辑",
        labelWidth: "204px",
        formStyle: { height: "470px", overflowY: "auto", margin: 0, paddingTop: "16px" },
      },
      staffingState,
      switchReq: {
        updateUrl: lcInstitutionStaffUpdateStatus,
      },
      /* 当前系统人员配置数量*/
      staffInfo: {
        staffCount: 0, //	当前系统使用人员数
        subAccountMaxCount: 0, //	新增员工上限数
      },
    };
  },
  computed: {
    /* 列表*/
    column() {
      return [
        {
          label: "员工ID",
          prop: "id",
          type: global.formItemType.input,
          level: 1,
          editDisabled: true,
          formHidden: !this.rowParams,
          requireRule: [{ required: true, message: "请输入员工ID", trigger: "blur" }],
          width: "90px",
        },
        ...column,
      ];
    },
    request() {
      const updateUrl = this.rowParams ? lcInstitutionStaffUpdate : lcInstitutionStaffInsert;
      return {
        getListUrl: lcInstitutionStaffPage,
        updateUrl: data => {
          const { workCityList } = data;
          data.workCityList = (workCityList || []).map(item => {
            return {
              province: item[0],
              code: item[1] || "",
            };
          });
          return updateUrl(data).then(res => {
            /* 如果是添加员工 就需要弹出 配权限的弹窗*/
            if (!this.rowParams) {
              this.isShowDeductNum = true;
              this.rowParams = res;
            }
            return res;
          });
        },
      };
    },
  },
  mounted() {
    lcInstitutionStaffGetInstitutionStaffInfo().then(data => {
      this.staffInfo = data;
    });
  },
  methods: {
    turnToLawyerAuth,
    addPopup() {
      this.rowParams = null;
      this.dialogVisible = true;
    },
    handleClick(row) {
      this.rowParams = {};
      return lcInstitutionStaffDetailByStaffId({
        id: row.id,
      }).then(data => {
        this.dialogVisible = true;
        const workCityList = (data.workCityList || []).map(item => {
          const province = !this.basics.isNull(item.province) ? [item.province] : [];
          /* 处理全国*/
          const code =
						!this.basics.isNull(item.code) && item.province !== item.code ? [item.code] : [];
          return [...province, ...code];
        });
        /* 判断是不是金额 并且form显示 就/100*/
        this.column.forEach(i => {
          if (i.isMoney && !i.formHidden && data[i.prop]) {
            data[i.prop] = data[i.prop] / 100;
          }
        });
        this.rowParams = {
          ...data,
          workCityList,
        };
        return this.rowParams;
      });
    },
    /* 权限弹窗*/
    handleRootClick(row) {
      // sysModuleSelectAllList({ level: 2 }).then(data => {
      //   console.log(data);
      // });
      // this.rowParams = row;
      // this.rootDialogVisible = true;
      this.$router.push({
        path: "/permissionsManagement",
        query: {
          id: row.id,
        },
      });
    },
    /* 权限提示确认按钮点击*/
    handleRootPopupClick() {
      this.isShowDeductNum = false;
      this.handleRootClick(this.rowParams);
    },
    handleRootPopupClose() {
      this.rowParams = null;
    },
  },
};
</script>

<style scoped lang="scss"></style>
