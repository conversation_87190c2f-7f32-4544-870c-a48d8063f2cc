<template>
  <app-popup :visible.sync="visible" width="400px" @confirm="handleConfirm" title="温馨提示" confirm-text="我知道了">
    <div class="px-[24px] py-[16px]">
      <p class="text-[14px] text-[#666666]">系统检测到律所累计剩余可抢案源数不足，为保障律所正常使用，请及时联系您的客户经理进行充值。</p>
      <div class="flex justify-between pt-[8px]">
        <div class="flex-1  p-[16px] bg-[#EDF3F7] rounded-[4px]">
          <p class="text-[12px] text-[#666666]">累计可抢案源数</p>
          <p class="font-bold text-[24px] text-[#333333]">{{data.totalClues}}</p>
        </div>
        <div class="flex-1 ml-[4px]  p-[16px] bg-[#FCF1ED] rounded-[4px]">
          <p class="text-[12px] text-[#666666]">累计剩余可抢案源数</p>
          <p class="font-bold text-[24px] text-[#333333]">{{data.remainClues}}</p>
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "components/appPopup/index.vue";
import EventBus from "utils/wsEventBus.js";
import { CMD } from "utils/ws.js";
import { lcSysNotificationRead } from "@/api/common.js";

export default {
  name: "InsufficientSourceOfTheCase",
  components: { AppPopup },
  data() {
    return {
      data: {}
    };
  },
  computed: {
    visible: {
      get() {
        return this.$store.getters["getInsufficientBalance"];
      },
      set(val) {
        this.$store.commit("SET_INSUFFICIENT_BALANCE", val);
      },
    },
  },
  mounted() {
    EventBus.$on("wsMessage", ({ cmd, data }) => {
      if(cmd === CMD.CASE_PUSH_NOT_ENOUGH){
        this.data = data;
        // this.$store.commit("SET_INSUFFICIENT_BALANCE", true);
      }
    });
  },
  methods: {
    handleConfirm(){
      lcSysNotificationRead({ id: this.data.id });
      this.$store.commit("SET_INSUFFICIENT_BALANCE", false);
      this.$store.commit("REDUCE_NOTICE_COUNTS", "sysNotificationNum");
    }
  }
};
</script>

<style scoped lang="scss">

</style>
