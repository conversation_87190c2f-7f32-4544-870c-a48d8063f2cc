import router from "@/router";
import store from "@/store";

/** 跳转到我的喜报 */
export function turnToMyNews() {
  return router.push("/myGoodNews");
}

/** 跳转到喜报广场 */
export function turnToGoodNews() {
  console.log(2222);
  return router.push("/goodNewsSquare");
}

/** 跳转到上传喜报 */
export function turnToUploadGoodNews({ id } = {}) {
  return router.push({
    path: "/uploadGoodNews",
    query: {
      id,
    },
  });
}

/**
 * 打开律师认证弹窗
 * @param callback
 * @param data 如果是替别人提交律师认证，则需要传这个参数
 * @param submitCallback 提交成功后的回调
 */
export function turnToLawyerAuth({ data, submitCallback } = {}) {
  store.commit("SET_CERTIFICATION_DATA", data);
  store.commit("SET_CERTIFICATION_SUBMIT_CALL_BACK", submitCallback);
  store.commit("SET_CERTIFICATION_POPUP", true);
}

/** 跳转到开发票 */
export function turnToInvoice() {
  return router.push("/invoice");
}

/** 跳转到开票记录 */
export function turnToInvoiceRecord() {
  return router.push("/invoiceRecord");
}

/* 案源推送页面*/
export function turnToCasePush() {
  return router.push("/caseSourcePush");
}

// 自动抢案源页面
export function turnToAutoCasePush() {
  return router.push("/automaticallyRobTheSourceOfTheCase");
}

// 线索跟进页面
export function turnToClueFollowUp(query = {}) {
  return router.push({
    path: "/leadFollowUpPage",
    query,
  });
}

/* 跳转默认支付设置页面 */
export function turnToDefaultPaymentSetting() {
  return router.push({
    path: "/defaultCaseGrabOrder",
  });
}
