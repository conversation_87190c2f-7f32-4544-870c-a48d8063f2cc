<template>
  <div class="menu-wrapper">
    <template v-if="!item.children||item.children.length<1">
      <el-menu-item :index="item.path"
                    @click="jumpPage(item.path)">
        <i class="iconfont" :class="item.moduleIcon" />
        <span slot="title">{{ item.meta.title }}</span>
      </el-menu-item>
    </template>
    <template v-else>
      <el-submenu :index="item.path">
        <template slot="title">
          <i class="iconfont" :class="item.moduleIcon" />
          <span slot="title">{{ item.meta.title }}</span>
        </template>
        <template>
          <sidebar-item
            v-for="(route,index) in item.children"
            :key="index"
            :item="route"
            :base-path="route.path"
          />
        </template>
      </el-submenu>
    </template>
  </div>
</template>

<script>
export default {
  name: "SidebarItem",
  props: {
    item: {
      type: Object
    }
  },
  methods: {
    jumpPage(url) {
      this.$router.push(url);
    }
  }
};
</script>

<style scoped lang="scss">
.iconfont {
  margin-right: 8px;
}

.menu-wrapper {

}
</style>
