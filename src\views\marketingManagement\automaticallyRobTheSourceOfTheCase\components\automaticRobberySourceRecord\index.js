import global from "@/utils/constant";
import { dataDetailList } from "@/api/common";

const searchForm = [
  {
    prop: "todo",
    propKey: ["beginDate", "endDate"],
    label: "抢单时间",
    search: true,
    tableHidden: true,
    type: global.formItemType.daterange,
    valueFormat: "yyyyMMdd",
  },
  {
    prop: "clueTypeValue",
    label: "线索类型",
    tableHidden: true,
    search: true,
    filterUrl: true,
    type: global.formItemType.select,
    syncData: {
      url: dataDetailList,
      value: "value",
      label: "label",
      params: { groupCode: "LAWYER_SPECIALITY" },
    },
  },
];

export const tableColumn = [
  ...searchForm,
  {
    prop: "businessId",
    label: "线索ID",
    width: "120px",
  },
  {
    prop: "publishTime",
    label: "发布时间",
    width: "180px",
  },
  {
    prop: "provinceName/regionName",
    label: "发布地区",
    render: (h, { row }) => {
      return (
        <span>{row.provinceName}{row.regionName}</span>
      );
    },
    width: "120px",
  }, {
    prop: "clueInfo",
    label: "线索描述",
    minWidth: "448px",
  },
  {
    prop: "typeLabel",
    label: "线索类型",
    width: "100px",
  }, {
    prop: "userPhone",
    label: "发布人手机号",
    width: "135px"
  },
  {
    prop: "qdInstitutionStaffName",
    label: "抢单人",
    width: "90px",
  },
  {
    prop: "createTime",
    label: "抢单时间",
    width: "180px",
  }
];

