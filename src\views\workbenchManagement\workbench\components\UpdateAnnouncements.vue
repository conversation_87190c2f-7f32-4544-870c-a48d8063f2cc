<template>
  <div class="min-h-[250px] bg-[#FFFFFF] rounded-[2px]">
    <workbench-card-title>
      更新公告
      <template #append>
        <p class="flex items-center !text-[12px] text-[#999999] cursor-pointer" @click="more">更多 <i class="iconfont icon-xianxingtubiao-9" /></p>
      </template>
    </workbench-card-title>
    <div class="pt-[8px] pb-[16px] px-[24px]">
      <div v-for="(i,index) in list" :key="i.id" @click="jump(index)" class="flex justify-between h-[36px] text-[#333333] cursor-pointer hover:text-[#3887F5] items-center">
        <div class="flex items-center">
          <p
            v-if="index === 0"
            class="text-[12px] !mr-[4px] text-center text-[#EB4738] w-[36px] h-[16px] bg-[#FCF1ED] rounded-[2px] border-[1px] border-solid border-[#FBCFC3]">
            NEW</p>
          <p class="text-[14px] announcements-text text-ellipsis">{{i.title}}</p>
          <span v-if="i.read === 0" class="ml-[4px] w-[6px] h-[6px] bg-[#EB4738] rounded-[8px] border-[1px] border-solid border-[#FFFFFF]" />
        </div>
        <p class="text-[12px] text-[#999999] w-[120px]">{{i.pubTime}}</p>
      </div>
    </div>
  </div>
</template>

<script>
import WorkbenchCardTitle from "views/workbenchManagement/workbench/components/CardTitle.vue";
import { lcNoticePage } from "@/api/workbenchManagement.js";

export default {
  name: "WorkbenchUpdateAnnouncements",
  components: { WorkbenchCardTitle },
  data() {
    return {
      list: []
    };
  },
  mounted() {
    lcNoticePage({
      currentPage: 1,
      pageSize: 5
    }).then(data => {
      this.list = data.records || [];
    });
  },
  methods: {
    jump(index){
      this.$set(this.list[index], "read", 1);
      this.$router.push({
        path: "/updateAnnouncements",
        query: {
          id: this.list[index].id
        }
      });
    },
    /* 更多*/
    more() {
      this.$router.push({
        path: "/updateAnnouncements",
      });
    }
  }
};
</script>

<style scoped lang="scss">
.announcements-text {
  max-width: pxToPer(290);
}
</style>
