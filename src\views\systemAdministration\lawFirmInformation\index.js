/* 基础信息*/
import { grabType, staffingState } from "@/enum/staffing.js";
import { dataDetailList } from "@/api/common.js";
import global from "@/utils/constant.js";
import { lcInstitutionStaffList } from "@/api/system";
import { isLeadPackageDisplay } from "components/leadPackageDisplay";
import datePickerOptions from "utils/datePickerOptions";

console.log(global, 90);

export const baseList = [
  {
    label: "机构ID",
    prop: "id",
  },
  {
    label: "入驻时间",
    prop: "createTime",
  },
  {
    label: "律所名称",
    prop: "name",
  },
  {
    label: "机构状态",
    prop: "status",
    list: staffingState,
  },
  {
    label: "律所LOGO",
    prop: "logo",
    type: "img",
  },
  {
    label: "合同协议/合同文件",
    prop: "contractImg",
  },
  {
    label: "所在地城市",
    prop: "regionName",
  },
  {
    label: "入驻类型",
    prop: "settleInType",
    list: "listOfOccupancyTypes",
  },
  {
    label: "线索可见范围",
    prop: "clueRange",
    list: "listOfClueRange",
  },
  {
    label: "机构联系方式（管理员）",
    prop: "managerPhone",
  },
  {
    label: "可见城市范围",
    prop: "workCitiesName",
  },
  {
    label: "机构类型",
    prop: "institutionType",
    list: "listOfInstitutionTypes",
  },
];

/* 线索包面板信息*/
export const cluePanelList = [
  {
    label: "累计可抢案源数",
    prop: "totalCaseSourceNum",
  },
  {
    label: "累计可抢即时咨询数",
    prop: "totalQaMessageNum",
  },
  {
    label: "累计剩余可抢案源数",
    prop: "remainCaseSourceNum",
  },
  {
    label: "累计剩余可抢即时咨询数",
    prop: "remainQaMessageNum",
  },
];

/* 法临币面板信息*/
export const coinPanelList = [
  {
    label: "累计充值法临币",
    prop: "actualAmount",
    type: "twoDecimalPlaces"
  },
  {
    label: "累计剩余充值法临币",
    prop: "remainAmount",
    type: "twoDecimalPlaces"
  }, {
    label: "累计赠送法临币",
    prop: "giftAmount",
    type: "twoDecimalPlaces"
  }, {
    label: "累计剩余赠送法临币",
    prop: "giftRemainAmount",
    type: "twoDecimalPlaces"
  },
];


/* 线索包账户信息*/
export const clueAccountList = [
  {
    prop: "createTime",
    label: "购买时间",
    minWidth: "170px",
  },
  {
    prop: "caseSourceGrabbingCount",
    label: "可抢案源数",
    minWidth: "110px",
  },
  {
    prop: "qaMessageGrabbingCount",
    label: "可抢即时咨询数",
    minWidth: "140px",
  },
  {
    prop: "remainCaseSourceGrabbingCount",
    label: "剩余可抢案源数",
    minWidth: "140px",
  },
  {
    prop: "remainQaMessageGrabbingCount",
    label: "剩余可抢即时咨询数",
    minWidth: "160px",
  },
  {
    prop: "validityTime",
    label: "使用有效期",
    minWidth: "170px",
  },
  {
    prop: "remainDay",
    label: "过期提醒",
    minWidth: "160px",
  },
];

/* 法临币账户信息*/
export const coinAccountList =  [
  {
    prop: "payTime",
    label: "充值时间",
    minWidth: "160px",
  },
  {
    prop: "actualAmount",
    label: "充值法临币",
    type: "twoDecimalPlaces"
  },
  {
    prop: "remainAmount",
    label: "剩余充值法临币",
    type: "twoDecimalPlaces"
  },
  {
    prop: "giftAmount",
    label: "赠送法临币",
    type: "twoDecimalPlaces"
  },
  {
    prop: "giftRemainAmount",
    label: "剩余赠送法临币",
    type: "twoDecimalPlaces"
  },
  {
    prop: "validTime",
    label: "赠送法临币有效期",
  },
];

/* 账户使用信息-充值记录*/
export const rechargeRecord = [
  {
    prop: "lawFirmsInstitutionId",
    label: "机构ID",
    width: "90px",
  },
  {
    prop: "institutionName",
    label: "机构名称",
    minWidth: "120px",
  },
  // {
  //   prop: "orderType",
  //   label: "订单类型",
  //   filterUrl: true,
  //   width: "120px",
  //   syncData: {
  //     url: dataDetailList,
  //     value: "value",
  //     label: "label",
  //     params: { groupCode: "LC_LAW_FIRMS_INSTITUTION_ORDER_TYPE" },
  //   },
  // },
  {
    prop: "payAmount",
    label: "实际支付金额",
    type: "money",
    minWidth: "120px",
  },
  {
    prop: "payType",
    label: "支付方式",
    filterUrl: true,
    width: "120px",
    syncData: {
      url: dataDetailList,
      value: "value",
      label: "label",
      params: { groupCode: "LC_LAW_FIRMS_INSTITUTION_ORDER_PAY_TYPE" },
    },
  },
  {
    prop: "caseSourceGrabbingCount",
    label: "可抢案源数",
    minWidth: "150px",
  },
  {
    prop: "qaMessageGrabbingCount",
    label: "可抢即时咨询数",
    minWidth: "180px",
  },
  {
    prop: "createTime",
    label: "录入时间",
    minWidth: "180px",
  },
  {
    prop: "payTime",
    label: "实际支付时间",
    minWidth: "180px",
  },
  {
    prop: "remainDay",
    label: "使用有效期",
    minWidth: "120px",
  },
];

/* 账户使用信息-案源或者即时咨询消耗明细*/
export const caseConsumptionDetails = (title = "案源", subTile = "线索") => [
  {
    prop: "businessId",
    label: title + "ID",
  }, ...(title === "案源" ? [{
    prop: "publicTime",
    label: "发布时间",
    minWidth: "190px",
  }] : []),
  {
    prop: "createTime",
    label: "抢单时间",
    minWidth: "190px",
  },
  {
    prop: 'institutionQdStaffName',
    label: '初始抢单人'
  },
  {
    prop: "institutionStaffName",
    label: "跟进人",
    minWidth: "150px",
    search: true,
    type: global.formItemType.input
  },
  {
    prop: "info",
    label: subTile + "描述",
    minWidth: "323px",
  },
  {
    prop: "typeLabel",
    label: subTile + "类型",
    minWidth: "150px",
  },
  {
    prop: "provinceName/regionName",
    label: "发布地区",
    render: (h, { row }) => {
      return <span>{row.provinceName}{row.regionName}</span>;
    },
    minWidth: "150px",
  },
  {
    prop: "consumeType",
    label: "抢单类型",
    width: "140px",
    filterUrl: true,
    syncData: {
      url: dataDetailList,
      value: "value",
      label: "label",
      params: { groupCode: "LC_CONSUME_ACCOUNT_TYPE" },
    },
  },
  ...title === "案源" ? [{
    prop: "serverWay",
    label: "抢单方式",
    search: true,
    filterUrl: true,
    type: global.formItemType.select,
    width: "120px",
    syncData: {
      data: grabType,
    },
  }] : [],
  {
    prop: "a1",
    propKey: ["garbTimeStart", "garbTimeEnd"],
    label: "抢单时间",
    search: true,
    tableHidden: true,
    type: global.formItemType.datetimerange,
    valueFormat: "yyyy/MM/dd HH:mm:ss",
    pickerDate: datePickerOptions(90),
  },
  ...isLeadPackageDisplay([{
    prop: "deductNum",
    label: "权益扣除数",
    width: "140px",
  }], []), {
    prop: "falinCoinDeductNum",
    label: "法临币扣除数",
    width: "140px",
    type: "twoDecimalPlaces"
  }
];

/**
 * 线索跟进记录  线索ID、案源描述、线索类型（案源、即时咨询）、、
 * **/
export const leadRecords = [
  {
    prop: "transferTime",
    label: "转移时间",
    propKey: ["startTimeStart", "startTimeEnd"],
    tableHidden: true,
    formHidden: true,
    type: global.formItemType.datetimerange,
    valueFormat: "yyyy-MM-dd HH:mm:ss",
    search: true,
  },
  {
    prop: "businessId",
    label: "线索ID",
    search: true,
    type: global.formItemType.input,
  }, {
    prop: "info",
    label: "线索描述",
  }, {
    prop: "type",
    label: "线索类型",
    filter: global.adminConfig.leadRecordsStatus
  }, {
    prop: "transferTime",
    label: "转移时间",
  }, {
    prop: "operatorName",
    label: "操作人",
  }, {
    prop: "operatorId",
    label: "操作人",
    search: true,
    tableHidden: true,
    type: global.formItemType.select,
    syncData: {
      url: lcInstitutionStaffList,
      value: "id",
      label: "userName",
    },
  }, {
    prop: "transferStaffName",
    label: "转移后跟进人",
  },
];

/* 法临币充值记录*/
export const coinRechargeRecord = [
  {
    prop: "institutionId",
    label: "机构ID",
    width: "90px",
  },
  {
    prop: "name",
    label: "机构名称",
    width: "220px",
  },{
    prop: "rechargeType",
    label: "充值类型",
    width: "104px",
    filter:global.adminConfig.depositType
  },
  // {
  //   prop: "orderType",
  //   label: "订单类型",
  //   width: "104px"
  // },
  {
    prop: "orderAmount",
    label: "充值金额",
    type: "money",
    minWidth: "120px",
  },
  {
    prop: "actualAmount",
    label: "充值法临币",
    minWidth: "120px",
    type: "twoDecimalPlaces"
  }, {
    prop: "giftAmount",
    label: "赠送法临币",
    minWidth: "120px",
    type: "twoDecimalPlaces"
  },
  {
    prop: "payType",
    label: "支付方式",
    minWidth: "120px"
  },
  {
    prop: "payTime",
    label: "充值时间",
    minWidth: "200px",
  },{
    prop: "remark",
    label: "备注",
  }
];
