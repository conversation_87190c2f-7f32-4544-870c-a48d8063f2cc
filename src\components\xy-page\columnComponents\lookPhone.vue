<template>
  <div class="flex">
    {{phone?phone:rowList[this.itemList.prop]}} <app-button v-if="!phone" style="padding-left: 5px" style-type="text" v-click-await="handleLook">查看号码</app-button>
  </div>
</template>

<script>
import AppButton from "components/appButton/index.vue";

export default {
  name: "LookPhone",
  components: { AppButton },
  props: {
    itemList: {
      type: Object,
      default: () => {},
      desc: "表格定义的字段"
    },
    rowList: {
      type: Object,
      default: () => {},
      desc: "表格返回的字段,展示的内容"
    }
  },
  data() {
    return {
      phone: ""
    };
  },
  methods: {
    handleLook(){
      return this.itemList.lookRequest(this.rowList).then(res => {
        this.phone = res.userPhone;
      });
    }
  }
};
</script>

<style scoped lang="scss">

</style>
