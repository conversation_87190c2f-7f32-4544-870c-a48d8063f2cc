<template>
  <div class="form-box">
    <el-form ref="form" :model="form" :rules="ruleConfig" label-width="80px">
      <el-form-item label="线索ID" prop="businessId">
        <el-input v-model.trim="form.businessId" type="number" style="width: 240px" />
        <app-button type="primary" class="ml-[8px]" @click="handleSearch">查询</app-button>
        <!--    退单详情    -->
        <div class="font-14">
          <refund-detail  v-if="detail" :detail="detail" />
        </div>
      </el-form-item>
      <el-form-item label="退单原因" prop="feedbackValueTemp">
        <el-radio-group v-model="form.feedbackValueTemp" @change="handleRadio">
          <el-radio v-for="(item,i) in refundLists" :key="i"  :label="item.value">{{item.label}}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="补充说明" prop="feedbackReason">
        <el-input type="textarea" rows="5" show-word-limit   maxlength="100" v-model.trim="form.feedbackReason" />
      </el-form-item>
      <el-form-item label="补充截图" prop="attachFile">
        <upload-image v-model="form.attachFile" :limit="3" />
        <p class="font-12 text-[#999999]">请上传图片，最多3张</p>
      </el-form-item>
      <el-form-item label-width="0">
        <div class="refund-desc font-14">
            <p>退单说明：</p>
            <div>
              <p>1.只能在锁定案源成功后{{hourVal}}小时内申请退单, 且只有一次退单的机会</p>
              <p>2.在线索-线索退单进行退单申请，再输入需要退单的线索ID号，选择退单原因并填写说明，添加相应附件凭证即可提交退单申请</p>
              <p>3.平台收到申请后会在3~5个工作日核实退款原因</p>
              <p>4.抢单权益数、法临币数将在平台审核通过后当日退回律所账户</p>
            </div>
        </div>
      </el-form-item>
      <el-form-item class="flex flex-space-end">
<!--        <app-button size="medium" type="info">取消</app-button>-->
        <app-button type="primary" class="ml-[16px]" size="medium"  @click="onSubmit">提交</app-button>
      </el-form-item>
    </el-form>

    <!--  线索ID没有查询的时候，二次确认  -->
    <app-popup
      title="线索退单确认"
      width="560px"
      :show-cancel="isShowCancel"
      :confirm-text="confirmText"
      :visible.sync="visible"
    @confirm="confirmSure">
      <div class="texts-word-box">
        <div>您要退单的线索信息如下，请在确认后提交</div>
        <refund-detail  v-if="detail" :detail="detail" class-name="erected" />
      </div>
    </app-popup>


  </div>
</template>

<script>
import UploadImage from "components/uploadImage/index.vue";
import RefundDetail from "views/leadManagement/clueSquare/leadChargebacks/component/refundDetail.vue";
import AppButton from "components/appButton/index.vue";
import { feedBackCaseSourceDetail, feedBackCaseSourceSubmit } from "@/api/clues";
import { dataDetailList } from "@/api/common";
import AppPopup from "components/appPopup/index.vue";

export default {
  name: "FormLeadChargebacks",
  components: { AppPopup, AppButton, RefundDetail, UploadImage },
  props: {
    hourVal: {
      type: Number,
      default: 24
    }
  },
  data(){
    return{
      form: {
        feedbackReason: "",
        attachFile: "",
        feedbackValueTemp: "",
        feedbackLabel: ""
      },
      ruleConfig: {
        businessId: [
          { required: true, message: "请输入退单的线索ID", trigger: "blur" }
        ],
        feedbackReason: [
          { required: true, message: "请输入补充说明", trigger: "blur" }
        ],
        feedbackValueTemp: [
          { required: true, message: "请选择退单原因", trigger: "change" }
        ],
        attachFile: [
          { required: true, message: "请上传图片，最多3张", trigger: "change" }
        ],

      },
      detail: null,
      refundLists: [],   // 退单原因

      visible: false, // 二次确认弹窗
    };
  },
  computed: {
    isShowCancel(){
      return  !(this.detail && this.detail.hintStr);
    },
    confirmText(){
      return  this.detail && this.detail.hintStr ? "我知道了" : "确定";
    }
  },
  async created() {
    this.refundLists = await dataDetailList({ groupCode: "LC_REFUND_LABEL" });
    console.log(this.refundLists, 888);
  },
  methods: {
    /** 二次确认 **/
    confirmSure(){
      if(this.detail && this.detail.hintStr){
        this.visible = false;
      }else {
        this.onSubmit();
      }
    },
    onSubmit() {
      this.$refs["form"].validate((valid) => {
        console.log(valid, 888);
        if (valid) {
          if((!this.detail || Number(this.detail.businessId) !== Number(this.form.businessId)) && !this.visible )  return this.handleSearch(true);
          this.form.feedbackValue = Number(this.form.feedbackValueTemp);
          this.form.serverId = this.detail.id;
          feedBackCaseSourceSubmit(this.form).then(r => {
            this.$message.success("线索退单申请成功，请耐心等待平台审核");
            this.visible = false;
            this.$emit("sure");

            setTimeout(() => {
              this.form = {
                feedbackReason: "",
                attachFile: "",
                feedbackValueTemp: "",
                feedbackLabel: ""
              };
              this.detail = null;
            }, 1500);
          });
        } else {
          console.log("error submit!!");
          return false;
        }
      });
      console.log("submit!", this.form);
    },
    /** 选择退单原因 **/
    handleRadio(val){
      const obj = this.refundLists.find(r => r.value === val);
      this.form.feedbackLabel = obj.label;
      console.log(this.form);
    },
    /** 查询线索ID **/
    handleSearch(val){
      if(!this.form.businessId) return this.$message.warning("请输入线索ID进行查询！");
      this.detail = null;
      feedBackCaseSourceDetail({ id: this.form.businessId }).then(r => {
        this.detail = r;
        this.visible = val;
      });
    }
  }
};
</script>

<style lang="scss" scoped>
.form-box{
  padding: 16px 0;

  .refund-desc{
    background: #F5F5F7;
    border-radius: 4px 4px 4px 4px;
    padding: 16px;
    p{
      line-height: 20px;
      color: #666666;
    }
    >div{
      margin-top: 16px;
      font-weight: 400;
      font-size: 14px;
    }
  }
}
.texts-word-box{
  padding: 16px 24px;
}
</style>
