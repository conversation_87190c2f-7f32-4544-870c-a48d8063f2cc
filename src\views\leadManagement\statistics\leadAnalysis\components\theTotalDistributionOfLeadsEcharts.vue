<template>
<!--  抢线索总分布-->
  <div class="p-[24px]">
    <div class=" pb-[30px]">
      <p class="text-[14px] text-[#666666] flex items-center">总抢线索数：<span class="text-[#333333] font-bold">{{info.grabClueTotal}}</span></p>
    </div>
    <app-echarts ref="echarts" class="echarts" />
  </div>
</template>

<script>
import AppEcharts from "components/appEcharts/index.vue";
import { grabClueStatistics } from "@/api/clues.js";
import moment from "moment";
export default {
  name: "TheTotalDistributionOfLeadsEcharts",
  components: { AppEcharts },
  props: {
    api: {
      type: Function,
      default: grabClueStatistics
    }
  },
  data() {
    return {
      info: {
        lcGrabClueStatisticsByDayList: [],
        grabClueTotal: 0
      }
    };
  },
  mounted() {
    this.initEcharts();
  },
  methods: {
    initEcharts(data = {}){
      this.$nextTick(() => {
        this.api(data).then(res => {
          this.info = res;
          this.$refs.echarts.initEcharts(this.echartsOptions);
        });
      });
    },
    echartsOptions(echarts){
      const day = [];
      const data = [];
      (this.info.lcGrabClueStatisticsByDayList || []).forEach(item => {
        day.push(moment(item.day, "YYYYMMDD").format("yyyy-MM-DD"));
        data.push(item.grabClueTotalByDay);
      });
      return  {
        xAxis: {
          type: "category",
          data: day,
          axisTick: {       // y轴刻度线
            show: false
          },
          axisLabel: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: "#999999",
              width: 1,
            },
          }
        },
        yAxis: {
          type: "value",
          axisTick: {       // y轴刻度线
            show: false
          },
          splitLine: {    // 网格线
            lineStyle: {
              type: "dashed"    // 设置网格线类型 dotted：虚线   solid:实线
            },
            show: true // 隐藏或显示
          },
          axisLine: {
            lineStyle: {
              color: "#666666",
              width: 0
            }
          }
        },
        grid: {
          top: "10%",
          right: "2%",
          left: "5%",
          bottom: "5%",
        },
        tooltip: {
          trigger: "axis",
          formatter: function(params) {
            return (params || []).reduce((a, b) => {
              return a + `${b.axisValue} 抢单${b.value}条<br/>`;
            }, "");
          }
        },
        series: [
          {
            data: data,
            type: "line",
            smooth: true,

            lineStyle: {
              color: "#3887F5"
            },

            itemStyle: {
              color: "#3887F5"
            },
            areaStyle: {
              opacity: 0.8,
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: "#3887F5"
                },
                {
                  offset: 1,
                  color: "#ffffff"
                }
              ])
            }
          }
        ]
      };
    }
  },

};
</script>

<style scoped lang="scss">
.echarts{
  height: 200px;
}
</style>
