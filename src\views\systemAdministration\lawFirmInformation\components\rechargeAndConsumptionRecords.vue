<template>
  <div>
    <div class="flex items-center justify-between pb-[16px]">
      <app-select @change="handleClick" :default-value="activeName" :select-list="selectList" />
      <export-button :file-name="activeLabel" :afferent-column="columnList" :export-option="exportOption" />
    </div>
    <xy-page v-if="state" :request="requestList" :column="columnList" :show-search="activeName!==0&&activeName!==4" style="padding: 0" />
  </div>
</template>

<script>
import AppSelect from "components/AppSelect/index.vue";
import ExportButton from "components/xy-buttons/exportButton.vue";
import XyPage from "components/xy-page/index.vue";
import {
  caseConsumptionDetails, coinRechargeRecord,
  leadRecords,
  rechargeRecord
} from "views/systemAdministration/lawFirmInformation/index.js";
import {
  lcLawFirmsInstitutionFlbAccountInfoPage, lcLawFirmsInstitutionFlbAccountInfoPageExport,
  lcLawFirmsInstitutionOrderExport,
  lcLawFirmsInstitutionOrderPage,
  lcLawFirmsInstitutionPageServerInfo,
  lcLawFirmsInstitutionPageServerInfoExport,
  lcLawFirmsTransferRecord,
  lcLawFirmsTransferRecordExport
} from "@/api/system.js";
import { isLeadPackageDisplay } from "components/leadPackageDisplay";

export default {
  name: "RechargeAndConsumptionRecords",
  components: { XyPage, ExportButton, AppSelect },
  data() {
    return {
      state: true,
      activeName: 0,
      activeLabel: "充值记录",
      searchQuery: {},
      /* 列表*/
      columnList: [],
      /* 导出请求*/
      exportUrl: "",
      /* 列表请求*/
      requestUrl: ""
    };
  },
  computed: {
    exportOption() {
      return {
        url: this.exportUrl,
        data: {
          ...this.searchQuery,
          currentPage: "",
          pageSize: ""
        }
      };
    },
    requestList() {
      return {
        getListUrl: (data) => {
          this.searchQuery = { ...data };
          return this.requestUrl(data);
        }
      };
    },
    selectList(){
      return [...isLeadPackageDisplay([{
        label: "线索包订单记录",
        value: 0,
        list: rechargeRecord,
        url: lcLawFirmsInstitutionOrderPage,
        exportUrl: lcLawFirmsInstitutionOrderExport
      }], []), {
        label: "法临币订单记录",
        value: 4,
        list: coinRechargeRecord,
        url: lcLawFirmsInstitutionFlbAccountInfoPage,
        exportUrl: lcLawFirmsInstitutionFlbAccountInfoPageExport
      }, {
        label: "案源抢单记录",
        value: 2,
        list: caseConsumptionDetails(),
        url: (data) => lcLawFirmsInstitutionPageServerInfo({ ...data, type: 2 }),
        exportUrl: (data) => lcLawFirmsInstitutionPageServerInfoExport({ ...data, type: 2 }),
      }, {
        label: "即时咨询抢单记录",
        value: 1,
        list: caseConsumptionDetails("咨询", "咨询"),
        url: (data) => lcLawFirmsInstitutionPageServerInfo({ ...data, type: 1 }),
        exportUrl: (data) => lcLawFirmsInstitutionPageServerInfoExport({ ...data, type: 1 })
      }, {
        label: "线索转移记录",
        value: 3,
        list: leadRecords,
        url: lcLawFirmsTransferRecord,
        exportUrl: lcLawFirmsTransferRecordExport
      }];
    }
  },
  created() {
    this.handleClick(this.selectList[0]);
  },
  methods: {
    /* 切换Tab */
    handleClick({ value, label, list, url, exportUrl }) {
      this.activeName = value;
      this.activeLabel = label;
      /* 赋值table列表*/
      this.columnList = list;
      /* table列表请求*/
      this.requestUrl = url;
      /* 导出*/
      this.exportUrl = exportUrl;
      this.state = false;
      this.$nextTick(() => {
        this.state = true;
      });
    }
  },
};
</script>

<style scoped lang="scss">

</style>
