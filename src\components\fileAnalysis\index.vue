<template>
  <div class="fileTxt">
    <el-upload
        accept='.txt'
        :show-file-list="false"
        :before-upload="beforeUpload"
        class="editor-slide-upload"
        action="#"
      >
        <el-button size="small" type="primary">
          {{title}}
        </el-button>
      </el-upload>
      <!-- <input type="file" accept=".txt" id='file'  @change="seletfile"> -->
  </div>
</template>

<script>
// import { uplodWord } from '@/api/common' // 上传文件的地址
export default {
  name: "FileTxt",
  props: {
    value: {
      type: Array,
      default: () => {}
    },
    title: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      values: ""
    };
  },
  created() {
    console.log();
  },
  methods: {
    beforeUpload(file) {
      this.read(file);
      return false;
    },
    seletfile() {
      console.log(document.getElementById("file").value);
    },
    read(f) {
      const reader = new FileReader(); const _this = this;
      reader.onload = function(event) {
        let resText = event.target.result;
        resText = resText.replaceAll("\r", "");
        const resArr = resText.split("\n");
        for (let i = resArr.length; i--; i >= 0) {
          const reg = /^\d+(\.\d+)?$/;
          if (!reg.test(resArr[i]) || !resArr[i] || resArr[i] === " ") {
            resArr.splice(i, 1);
          }
        }
        if (resArr.length > 0) {
          _this.$message.success("导入成功");
          //   resArr.pop()
          _this.$emit("update:value", resArr);
        }

        // console.log(resArr.length, resArr)
      };
      reader.readAsText(f);
    }
  }
//   beforeCreate() {
//     // 读取文件
//     FileReader.prototype.reading = function() {
//       const encode = 'GBK'
//       const bytes = new Uint8Array(this.txtResult) // 无符号整型数组
//       const text = new TextDecoder(encode).decode(bytes)
//       return text
//     }
//     /* 重写readAsBinaryString函数 */
//     FileReader.prototype.readAsBinaryString = function(f) {
//       if (!this.onload) {
//         this.onload = e => { // 在this.onload函数中，完成公共处理
//           const rs = this.reading()
//           console.log(rs)
//         }
//       }
//     }
//   }
};
</script>

<style lang="scss" scoped>
  .fileTxt{

  }
</style>
