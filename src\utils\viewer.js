import basics from "utils/basicsMethods.js";

let viewerConstructor = {};
/* 预览图片*/
let sourceImageURLs = [];

/* 避免多次引入*/
if (basics.isObjNull(viewerConstructor)) {
  require("viewerjs/dist/viewer.css");
  const { api } = require("v-viewer");
  console.log("加载v-viewer==============");
  viewerConstructor = api;
}

export const sourceImageURLsPush = (value) => {
  sourceImageURLs.push(value);
};

export const sourceImageURLsClear = (value) => {
  sourceImageURLs = [];
};

export const viewerApiShow = (url) => {
  const indexOf = sourceImageURLs.indexOf(url);
  viewerConstructor({
    options: {
      toolbar: true,
      initialViewIndex: indexOf > -1 ? indexOf : 0
    },
    images: sourceImageURLs
  });
};
