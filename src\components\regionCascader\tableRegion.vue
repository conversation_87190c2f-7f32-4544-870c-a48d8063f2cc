<template>
  <div class="choice-city-wrap">
        <el-card class="box-card">
            <el-checkbox-group class="flex flex-wrap" :disabled='isDisable'  v-model="ruleForm.provinceName" @change="selectProvince" size="small">
                <div v-for="(item,i) in regionList" :key="i" class="city-lists">
                    <el-checkbox  :label="item.name" border />

                    <el-popover
                        placement="right"
                        width="400"
                        trigger="click">
                            <el-checkbox-group :disabled='isDisable'  v-model="ruleForm.regionName" @change="selectCity(item)" size="small">
                                <el-checkbox  v-for="(city,k) in item.children" :key="k" :label="city.name" />
                            </el-checkbox-group>
                            <i slot="reference" @click="showCity(item)" class="cur"> {{filterExist(ruleForm.provinceName ,[item])}}/{{filterExist(ruleForm.regionName ,item.children)}}</i>
                    </el-popover>

                </div>
            </el-checkbox-group>
            <el-divider />
            <div>
                <span style="font-size:14px;font-weight:bold;">选中的城市: </span>
                <el-tag
                    v-for="(tag,i) in checkedCity"
                    :key="i"
                    closable
                    class="m-r-15"
                    :disable-transitions="false"
                    @close="handleClose(tag,ruleForm.regionName)"
                    type="success">
                    {{tag.provinceName+'-' +tag.regionName}}</el-tag>
            </div>
        </el-card>

  </div>
</template>

<script>
import { getAreaList } from "@/api/common";
export default {
  name: "Region",
  props: {
    value: {
      type: Array,
      default: () => [],
      desc: "获取值"
    }
  },
  data() {
    return {
      ruleForm: {
        provinceName: [],
        regionName: []
      },
      regionList: [],
      province: [],
      city: [],
      currentProvince: ""
    };
  },

  computed: {
    // 城市的选中
    checkedCity() {
      const lists = [];
      for (const res of this.regionList) {
        if (this.ruleForm.provinceName.includes(res.name)) {
          res.children && res.children.forEach(reg => {
            const obj = {};
            if (this.ruleForm.regionName.includes(reg.name)) {
              obj["provinceCode"] = res.code;
              obj["provinceName"] = res.name;
              obj["regionCode"] = reg.code;
              obj["regionName"] = reg.name;
              lists.push(obj);
            }
          });
        }
      }
      //   console.log(lists)
      //   this.$emit('input')
      return lists;
    },
    // 编辑的时候不能修改
    isDisable() {
      return false;
    }
  },
  created() {
    this.getRegion().then(res => {
      this.regionList = res;
      console.log(this.regionList);
    });
  },
  methods: {
    /* 获取地区 */
    getRegion(type = 1, value) {
      const _data = {
        type,
        code: value
      };
      return getAreaList(_data);
    },
    /* 获取市级地区 */
    showCity(row, needAll) {
    //   console.log(row, needAll, 888)
      if (row.children) return;
      this.getRegion(2, row.code).then(res => {
        this.$set(row, "children", res);
        if (needAll) {
          this.ruleForm.regionName = [...new Set(this.ruleForm.regionName.concat(res.map(reg => reg.name)))];
        }
      });
    },
    /* 选择省*/
    async selectProvince(item) {
      // console.log(item, this.ruleForm.provinceName, 99999)
      let currentProvince = "";
      // 取当前的选中的省
      if (item.length !== this.currentProvince.length) {
        const currentProvinceLists = this.getDifferValue(item, this.currentProvince);
        currentProvince = currentProvinceLists;
        if (currentProvinceLists.length > 1) currentProvince = [item[item.length - 1]];
      }
      // 取当前操作的地区
      const iterator = this.regionList.find(res => res.name === currentProvince[0]);

      if (iterator) {
        if (!iterator.children) {
          this.showCity(iterator, true);
        } else if (item.includes(iterator.name) && iterator.children) {
          this.ruleForm.regionName = [...new Set(this.ruleForm.regionName.concat(iterator.children.map(reg => reg.name)))];
        } else if (!item.includes(iterator.name) && iterator.children) {
          // 当取消选中的省份
          this.ruleForm.regionName.forEach((element, i) => {
            if (iterator.children.find(rep => rep.name === element)) {
              this.ruleForm.regionName[i] = null;
            }
          });
          this.ruleForm.regionName = this.ruleForm.regionName.filter(Boolean);
        }
      }

      this.currentProvince = item;
    },
    /* 取出两个数组中，不同的值*/
    getDifferValue(arr1, arr2) {
      return arr1.concat(arr2).filter(function(v, i, arr) {
        return arr.indexOf(v) === arr.lastIndexOf(v);
      });
    },
    /* 选择市*/
    selectCity(item) {
      // 保证了，这个省下面的市已经被选中了
      const objItem = item.children.filter(res => this.ruleForm.regionName.includes(res.name));
      // 去看看市有没有，没有就添加，有的话，就不管
      if (objItem && !this.ruleForm.provinceName.includes(item.name)) {
        this.ruleForm.provinceName.push(item.name);
      }
    },
    /* 过滤选中城市字段 */
    filterExist(value, lists) {
      if (!value || !lists) return 0;
      let item = null;
      item = lists.filter(res => value.includes(res.name));

      return item.length;
    },
    /* 关闭选中省/市 */
    handleClose(tag, lists) {
      lists.splice(lists.indexOf(tag.regionName), 1);

      // 当一个省下面的市都没有选中的话，删除选中省
      const sameProvince = [];
      this.checkedCity.map(re => {
        if (this.ruleForm.provinceName.includes(re.provinceName)) {
          if (!sameProvince.includes(re.provinceName)) sameProvince.push(re.provinceName);
        }
      });
      this.ruleForm.provinceName = sameProvince;
    },
    /* 确定选中 */
    submit() {

    },
    /* 关闭弹窗 */
    cancle() {
      this.$emit("update:dialogPrice", false);
    }
  }
};
</script>

<style lang="scss" scoped>
    .choice-city-wrap{

      .city-lists{
          width: 25%;

          i{
              font-size: 12px;
              font-style: normal;
              margin-left: 5px;
              color: $primary-color;
          }
      }
      .el-checkbox.is-bordered + .el-checkbox.is-bordered{
          margin-left: 0;
      }
  }
</style>
