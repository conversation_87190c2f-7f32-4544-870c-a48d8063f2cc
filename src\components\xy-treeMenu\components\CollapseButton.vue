<template>
  <i @click="toggleCollapse" class="ml-[24px] w-[20px] h-[20px] text-[20px] cursor-pointer iconfont" :class="[isCollapse?'icon-xianxingtubiao-1':'icon-xianxingtubiao']" />
</template>

<script>
export default {
  name: "CollapseButton",
  computed: {
    isCollapse() {
      return this.$store.getters.getIsCollapsed;
    },
  },
  methods: {
    /** 打开关闭 */
    toggleCollapse() {
      this.$store.commit("SET_IS_COLLAPSE", !this.$store.getters.getIsCollapsed);
    },
  }
};
</script>



<style scoped lang="scss">

</style>
