<template>
  <div class="form-login">
    <el-form ref="loginForm" class="form" :rules="rules" :model="formData" size="large">
      <el-form-item prop="phone">
        <el-input
          class="ipt"
          maxlength="11"
          placeholder="请输入手机号"
          v-model.trim="formData.phone"
        />
      </el-form-item>
      <el-form-item prop="code">
        <div class="sms-code relative">
          <el-input
            class="code"
            @keyup.enter.native="onSubmit('loginForm')"
            maxlength="4"
            placeholder="请输入验证码"
            v-model.trim="formData.code"
          />
          <div v-if="!disabled" class="code-btn" @click="getCode">{{ codeText }}</div>
          <div v-else class="code-btn disabled">
									<span>{{ time }}</span
                  >s后获取
          </div>
        </div>
      </el-form-item>
      <el-button class="large-btn" type="primary" @click="onSubmit('loginForm')"
      >登 录
      </el-button>
    </el-form>
  </div>
</template>

<script>
import { regular } from "utils/validate";
import { getSmsCode } from "@/api/login";

export default {
  name: "FormLogin",

  data() {
    return {
      formData: {
        phone: "",
        code: "",
      },
      rules: {
        phone: [
          { required: true, message: "请输入手机号", trigger: "blur" },
          { validator: regular("phone"), trigger: "blur" },
        ],
        code: [{ required: true, message: "请输入验证码", trigger: "blur" }],
      },
      codeText: "获取验证码",
      time: 59,
      timer: null,
      disabled: false,
      onceClick: false,
    };
  },
  mounted() {},
  destroyed() {
    this.clear();
  },
  methods: {
    onSubmit(formName) {
      if (this.onceClick) return;
      this.$refs[formName].validate(valid => {
        if (valid) {
          this.onceClick = true;
          this.$store.dispatch("setRequestLoading", false);
          this.$store
            .dispatch("login", this.formData)
            .then(async (data) => {
              this.$emit("loginSuccess", data);
            })
            .finally(() => {
              setTimeout(() => {
                this.onceClick = false;
              }, 3000);
            });
        } else {
          return false;
        }
      });
    },
    // 获取验证码
    getCode() {
      if (!this.formData.phone) {
        return this.$message.warning("请输入用户手机号");
      }
      this.$store.dispatch("setRequestLoading", false);
      /* 获取验证码接口 */
      getSmsCode({ phone: this.formData.phone })
        .then(res => {
          const _this = this;
          _this.disabled = true;
          _this.timer = setInterval(() => {
            if (_this.time > 0) {
              _this.time--;
            } else {
              _this.clear();
            }
          }, 1000);
        })
        .catch(() => {});
    },
    clear() {
      window.clearInterval(this.timer);
      this.timer = null;
      this.time = 59;
      this.disabled = false;
    },
  },
};
</script>

<style scoped lang="scss">
.form-login{
  /deep/ .el-form.form {
    margin-top: 64px;

    .iconfont {
      padding-left: $distance-mini;
    }

    .el-button {
      width: 100%;
      font-size: $font-size-large;
      border-radius: 8px 8px 8px 8px;
      margin-top: 8px;
      padding: 17px 20px;
    }

    .el-form-item {
      margin-bottom: 32px;
    }

    input {
      height: 54px;
      border-radius: 8px 8px 8px 8px;
    }

    .ipt {
      input {
        border-color: #eff1ff;
        box-sizing: border-box;

        &:focus {
          border-color: $primary-color;
        }
      }
    }

    .sms-code {
      @extend .flex;

      input {
        border-color: #eff1ff;

        &:focus {
          border-color: $primary-color;
        }
      }

      .code {
        // width: calc(100% - 105px);
      }

      .code-btn {
        position: absolute;
        right: 0;
        top: 50%;
        transform: translateY(-50%);
        margin-left: 5px;
        width: 100px;
        height: 40px;
        border-radius: 2px;
        // border: 1px solid $weak-color;
        box-sizing: border-box;
        text-align: center;
        cursor: pointer;
        font-size: $font-size-small;
        color: $primary-color;

        &:hover {
          border-color: $primary-color;
        }

        &.disabled {
          // background: #f2f2f2;
          color: #ccc;
          cursor: not-allowed;
          padding-right: 10px;
        }
      }
    }
  }
}
</style>
