user	root;
worker_processes	auto;
pid	/var/run/nginx.pid;
worker_rlimit_nofile 65535;

events {
    use epoll;
    multi_accept on;
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    log_format  access_json  '{"@timestamp":"$time_iso8601", '
			       '"remote_addr": "$remote_addr", '
                               '"body_bytes_sent": "$body_bytes_sent", '
                               '"request_time": "$request_time", '
                               '"status_code": "$status", '
                               '"host": "$host", '
                               '"request": "$request", '
                               '"method": "$request_method", '
                               '"uri": "$uri", '
                               '"http_referrer": "$http_referer", '
                               '"http_x_forwarded_for": "$http_x_forwarded_for", '
                               '"http_user_agent": "$http_user_agent" '
			      '}';

    sendfile  on;
    tcp_nopush  on;
    tcp_nodelay  on;
    server_tokens  off;
    keepalive_timeout  65;
    send_timeout  10;
    client_header_timeout  10;
    client_body_timeout  10;
    client_max_body_size 100m;

    open_file_cache  max=100000  inactive=20s;
    open_file_cache_valid  30s;
    open_file_cache_min_uses  2;
    open_file_cache_errors  on; 

    gzip  on;
    gzip_min_length  1k;
    gzip_buffers  4 16k;
    gzip_http_version  1.1;
    gzip_comp_level  6;
    gzip_types  text/plain application/x-javascript text/css application/xml text/javascript application/x-httpd-php application/javascript application/json;
    gzip_disable  "MSIE [1-6]\.";
    gzip_vary  on;

    include /etc/nginx/conf.d/*.conf;
}