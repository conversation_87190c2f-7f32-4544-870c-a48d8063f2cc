import basics from "utils/basicsMethods";
/**
 * @param timestamp 时间戳（毫秒）
 * @returns {string}
 */
function add0(m) {
  return m < 10 ? "0" + m : m;
}
export function timestampToTime(timestamp) {
  if (!timestamp) return "";
  var time = new Date(timestamp);
  var y = time.getFullYear();
  var m = time.getMonth() + 1;
  var d = time.getDate();
  var h = time.getHours();
  var mm = time.getMinutes();
  var s = time.getSeconds();
  return y + "-" + add0(m) + "-" + add0(d) + " " + add0(h) + ":" + add0(mm) + ":" + add0(s);
}

export function timestampToDay(timestamp) {
  if (!timestamp) return "";
  var time = new Date(timestamp);
  var y = time.getFullYear();
  var m = time.getMonth() + 1;
  var d = time.getDate();
  return y + "-" + add0(m) + "-" + add0(d);
}

/**
 * @param time 时间戳（毫秒）
 * @param cFormat 格式
 * @returns {string}
 */
export function parseTime(time, cFormat) {
  if (time === null || time === undefined) return "-";
  if (arguments.length === 0) {
    return null;
  }
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (("" + time).length === 10) time = parseInt(time) * 1000;
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  };
  return format.replace(/{(y|m|d|h|i|s|a)+}/g, (result, key) => {
    let value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    if (result.length > 0 && value < 10) {
      value = "0" + value;
    }
    return value || 0;
  });
}

export function setFormat(time, format) { // 时间转换 将Thu Jun 14 2018 00:00:00 GMT+0800 (中国标准时间)转换成你所需要的格式 如：yyy-MM-dd-HH-mm-ss
  if (time === "") {
    return "";
  }
  if (new Date(time) === "Invalid Date") {
    return time;
  }
  var t = new Date(time);
  var tf = function(i) {
    return (i < 10 ? "0" : "") + i;
  };
  return format.replace(/yyyy|MM|dd|HH|mm|ss/g, function(a) {
    switch (a) {
    case "yyyy":
      return tf(t.getFullYear());
    case "MM":
      return tf(t.getMonth() + 1);
    case "mm":
      return tf(t.getMinutes());
    case "dd":
      return tf(t.getDate());
    case "HH":
      return tf(t.getHours());
    case "ss":
      return tf(t.getSeconds());
    }
  });
}

/**
 * @param date 时间戳（毫秒）
 * @param fmt 格式
 * @returns {string}
 */
export function parseTimeTwo(time, fmt = "yyyy-MM-dd hh:mm:ss") {
  if (time === "" || !time || time === "-") return "-";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (("" + time).length === 10) time = parseInt(time) * 1000;
    date = new Date(time);
  }
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + "").substr(4 - RegExp.$1.length));
  }
  const o = {
    "M+": date.getMonth() + 1,
    "d+": date.getDate(),
    "h+": date.getHours(),
    "H+": date.getHours(),
    "m+": date.getMinutes(),
    "s+": date.getSeconds()
  };

  // 遍历这个对象
  for (const k in o) {
    if (new RegExp(`(${k})`).test(fmt)) {
      const str = o[k] + "";
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? str : padLeftZero(str));
    }
  }
  return fmt;

  function padLeftZero(str) {
    return ("00" + str).substr(str.length);
  }
}

/**
 * @param enums 数据
 * @param enums 静态数据数组
 * @returns {string}
 */
//  parseStatus(itemList.syncData.data ,itemList.syncData.label,itemList.syncData.value)
export function parseStatus(value, enums, label = "label", val = "value") {
  if (basics.isNull(value)) {
    return "-";
  }
  if (basics.isNull(enums) || basics.isArrNull(enums)) {
    return {
      "value": "-",
      "label": "-"
    };
  }
  // 争对返回值是数组的情况 需要将值'/'连接
  if (basics.isArray(value)) {
    const showArr = enums.filter(it => value.includes(Number(it[val]))).map(item => item[label]);
    return showArr.join("/");
  }
  const filterEnum = enums.filter(item => {
    if (String(item[val]) === String(value)) return item;
  });
  if (filterEnum.length > 0) return filterEnum[0][label];
  else {
    return "-";
  }
}
