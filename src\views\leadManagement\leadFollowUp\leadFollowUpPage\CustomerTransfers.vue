<template>
  <app-popup delay-click footer-border  show-cancel @close="close" @cancel="close" title="客户转移" width="400px" @confirm="confirm" :visible="dialogVisible">
    <div class="px-[24px] pb-[16px]">
      <p class="text-[14px] text-[#666666] py-[16px]">选择接收人员</p>
      <div class="overflow-y-auto h-[280px] rounded-[8px] bg-[#F5F5F7]">
        <div class="flex items-center h-[40px] pl-[16px] cursor-pointer" v-for="(i,index) in staffList" :key="i.id" @click="toggleStaff(index)">
          <i class=" !text-[16px] iconfont " :class="[activeIndex===index?'!text-[#3887F5] icon-danxuanyixuan':'!text-[#CCCCCC] icon-danxuanweixuan']" />
          <p class="text-[14px] pl-[8px] text-[#333333]">
            {{i.userName}}
          </p>
        </div>
      </div>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "components/appPopup/index.vue";
import { lcInstitutionStaffList } from "@/api/system";

export default {
  name: "CustomerTransfers",
  components: { AppPopup },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      activeIndex: 0,
      staffList: []
    };
  },
  watch: {
    dialogVisible(val){
      if(val){
        this.getStaffList();
      }
    }
  },
  methods: {
    /* 获取人员吗*/
    getStaffList(){
      /* 状态(1:正常,0:禁用)*/
      lcInstitutionStaffList({
        status: 1
      }).then((data) => {
        this.activeIndex = 0;
        this.staffList = data;
      });
    },
    close(){
      this.$emit("update:dialogVisible", false);
    },
    confirm(){
      /* 客户转移*/
      this.$emit("customerTransfers", this.staffList[this.activeIndex]);
    },
    /* 切换人员*/
    toggleStaff(index){
      this.activeIndex = index;
    }
  }
};
</script>

<style scoped lang="scss">

</style>
