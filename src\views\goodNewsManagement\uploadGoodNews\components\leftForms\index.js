import { minNumber, regular } from "utils/validate";



export const  ruleConfig = {
  /* 请选择员工姓名*/
  staffId: [
    { required: true, message: "请选择员工姓名", trigger: "change" }
  ],
  /* 上传合同*/
  contractImgUrl: [
    { required: true, message: "请上传合同，最多3张", trigger: "change" }
  ],
  /* 当事人手机号：*/
  userPhone: [
    { required: true, message: "请输入当事人手机号", trigger: "blur" },
    { validator: regular("phone"), trigger: "blur" }
  ],
  /* 案件类型：*/
  typeValue: [
    { required: true, message: "请选择案件类型", trigger: "change" }
  ],
  /* 线索类型：*/
  clueType: [
    { required: true, message: "请选择线索类型", trigger: "change" }
  ],
  /* 成案地点：*/
  cityCodes: [
    { required: true, message: "请选择成案地点", trigger: "change" }
  ],
  /* 成案周期 验证数字*/
  caseSuccessDay: [
    { required: true, message: "请输入成案周期", trigger: "blur" },
    { validator: regular("number"), trigger: "blur" }
  ],
  /* 涉案金额 验证金额格式 /^\d{1,8}$|(^\d{1,8}[\.]{1}[0-9]{1,2}$)/*/
  /* 在获取系统参数的最低金额的时候在更改 */
  amount: [
    { required: true, message: "请输入成案金额", trigger: "blur" },
    { validator: regular("number"), trigger: "blur" },
    { validator: minNumber(2000, "最小输入" + 2000 + "元"), trigger: "blur" }
  ],
  /* 委托金额 验证金额格式*/
  /* 在获取系统参数的最低金额的时候在更改 */
  caseEntrustAmount: [
    { required: true, message: "请输入委托金额", trigger: "blur" },
    { validator: regular("number"), trigger: "blur" },
    { validator: minNumber(2000, "最小输入" + 2000 + "元"), trigger: "blur" }
  ],
  /* 案件详情*/
  caseDetail: [
    { required: true, message: "请输入案件详情", trigger: "blur" },
    { type: "string", max: 100, message: "请输入100字以内", trigger: "blur" }
  ],
  /* 案件回顾*/
  caseReview: [
    { required: true, message: "请输入案件回顾", trigger: "blur" },
    { type: "string", max: 100, message: "请输入100字以内", trigger: "blur" }
  ]
};







