<template>
  <div class="swiper-box" v-if="!basics.isArrNull(list)">
     <el-carousel height="188px">
        <el-carousel-item v-for="item in list" :key="item.id">
          <img @click.stop="jump(item)" class="w-full h-full cursor-pointer" :src="item.imageUrl" alt="" />
        </el-carousel-item>
      </el-carousel>
  </div>
</template>
<script>
import { lawCloudAdvertList } from "@/api/common";

export default {
  name: "BannerSwiper",
  data () {
    return {
      list: []
    };
  },
  mounted() {
    lawCloudAdvertList({
      positionId: 199
    }).then(data => {
      this.list = data || [];
    });
  },
  methods: {
    jump(data) {
      /* jumpType 4新页面 5抽屉展示*/
      const { jumpType, addressUrl = "", title = "" } = data;
      if(this.basics.isNull(addressUrl)) return;
      if (jumpType === 4) {
        window.location.href = addressUrl;
      } else if (jumpType === 5) {
        /* 判断是否需要连登 链接上是否有isToken*/
        const nestApp = addressUrl.indexOf("isToken") > -1;
        this.$store.dispatch("setIframePreviewVisible", {
          title: title,
          previewUrl: addressUrl,
          nestApp
        });
      }
    }
  }
};
</script>
<style scoped lang="scss">
  .swiper-box{
    padding: 24px;
    background: #fff;

  }
</style>
