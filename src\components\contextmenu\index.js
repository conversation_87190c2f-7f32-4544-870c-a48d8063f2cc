import Vue from "vue";
import contextmenu from "components/contextmenu/index.vue";

let instance = "";

/* 使用单列默认 创建菜单*/
const init = () => {
  if (instance) {
    return instance;
  }
  const ContextmenuConstructor = Vue.extend(contextmenu);
  instance = new ContextmenuConstructor();
  const el = instance.$mount();
  document.body.appendChild(el.$el);
  return instance;
};

const initPrototype = (data = { menu: [], x: 0, y: 0 }) => {
  init().openMenu(data);
  return instance;
};
const dataSetKey = "data-binding-value";
/* 新增自定义指令 唤醒菜单*/
const contextmenuDirective = {
  bind(el, binding) {
    el[dataSetKey] = binding.value || {};
    const openMenu = e => {
      initPrototype({
        ...el[dataSetKey],
        x: e.clientX,
        y: e.clientY
      });
      e.preventDefault();
      e.stopPropagation();
    };
    el["openMenu"] = openMenu;
    el.addEventListener("contextmenu", openMenu);
  },
  unbind(el) {
    el.removeEventListener("contextmenu", el.openMenu);
  },
  update(el, binding) {
    el[dataSetKey] = binding.value || {};
  }
};

export default {
  /* 全局挂载菜单*/
  install(Vue) {
    Vue.prototype.$contextmenu = initPrototype;
    Vue.directive("appContextmenu", contextmenuDirective);
  }
};
