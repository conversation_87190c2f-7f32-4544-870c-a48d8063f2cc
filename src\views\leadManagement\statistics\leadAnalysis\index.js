
import { lcInstitutionStaffList } from "@/api/system";
import datePickerOptions from "utils/datePickerOptions";
import { trimesterTimeFormat } from "utils/toolMethods";
import global from "utils/constant";

/* 搜索list*/
export const searchList = [
  {
    label: "员工姓名",
    prop: "staffId",
    type: global.formItemType.select,
    search: true,
    tableHidden: true,
    syncData: {
      url: (data) => lcInstitutionStaffList(data).then(res => {
        /* 追加全部选项*/
        (res || []).unshift({ id: "0", userName: "全部" });
        return res;
      }),
      label: "userName",
      value: "id"
    }
  }, {
    propKey: ["startTimeStart", "startTimeEnd"],
    label: "日期",
    prop: "updateUserName",
    tableHidden: true,
    formHidden: true,
    type: global.formItemType.daterange,
    valueFormat: "yyyy-MM-dd",
    search: true,
    pickerDate: datePickerOptions(180),
    defaultValue: trimesterTimeFormat("YYYYMMDD"),
  }];

/* 抢线索数明细列表*/
export const aDetailedListOfTheNumberOfLeadsGrabbed = [
  {
    label: "员工姓名",
    prop: "institutionLawyerName"
  }, {
    label: "抢线索总数",
    prop: "grabClueTotal"
  }, {
    label: "抢案源线索数",
    prop: "caseSourceTotal"
  }, {
    label: "独享案源线索数",
    prop: "exclusiveCaseSourceTotal"
  }, {
    label: "抢即时咨询数",
    prop: "instantConsultTotal"
  }, {
    label: "跟进中",
    prop: "followIngCount"
  }, {
    label: "跟进结束",
    prop: "followEndCount"
  }];

/* 线索包权益消耗明细*/
export const aDetailedListOfTheConsumptionOfLeadPackageRights = [
  {
    label: "员工姓名",
    prop: "staffName"
  }, {
    label: "可抢权益数",
    prop: "kqEquityNumber"
  }, {
    label: "已消耗权益数",
    prop: "spentEquityNumber"
  }, {
    label: "已消耗独享权益数",
    prop: "dxEquityNumber"
  }, {
    label: "剩余可抢权益数",
    prop: "remainKqEquityNumber"
  }];

/* 法临币消耗明细*/
export const aDetailedListOfTheConsumptionOfLegalCurrency = [{
  label: "员工姓名",
  prop: "staffName"
}, {
  label: "累计可使用法临币",
  prop: "totalFalinCoin",
  type: "twoDecimalPlaces"
}, {
  label: "已使用法临币",
  prop: "totalSpentFalinCoin",
  type: "twoDecimalPlaces"
}, {
  label: "剩余法临币",
  prop: "totalRemainFalinCoin",
  type: "twoDecimalPlaces"
}];
