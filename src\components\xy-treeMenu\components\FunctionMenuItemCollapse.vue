<template>
	<div id="collapse-menu" class="mt-[4px]">
		<div
			:class="[
				{
					'menu-wrapper': showIcon,
				},
			]"
		>
			<!-- 最后一层 -->
			<template v-if="!item.children || item.children.length < 1">
				<el-menu-item :index="item.path" @click="jumpPage(item.path)" :class="{'!p-0 !leading-[1] !w-[64px] !h-[57px]':item.meta.parentId === 0}">
           <div :class="{'flex flex-col items-center justify-center h-full':item.meta.parentId === 0}">
             <i
             v-if="item.meta.parentId === 0"
									class="mx-auto leading-[1] w-[20px] h-[20px] text-[20px] !text-[#FFFFFF] block iconfont"
									:class="item.meta.moduleIcon"
								/>
            <span :class="{'leading-[1] text-[12px] !text-[#FFFFFF] mt-[5px]':item.meta.parentId === 0}">{{ item.meta.title }}</span>
           </div>
				</el-menu-item>
			</template>
			<template v-else>
				<el-submenu :index="item.path">
					<template #title>
						<div class="w-full h-full flex flex-center items-center" v-if="showIcon">
							<div class="mx-auto w-full h-full flex items-center justify-center flex-col">
								<i
									class="mx-auto w-[20px] h-[20px] text-[20px] !text-[#FFFFFF] block iconfont"
									:class="item.meta.moduleIcon"
								/>
								<span class="text-[12px] !text-[#FFFFFF] mt-[5px]">{{
									item.meta.title
								}}</span>
							</div>
						</div>
						<template v-else>
							<span slot="title">{{ item.meta.title }}</span>
						</template>
					</template>
					<template>
						<function-menu-item-collapse
							v-for="(route, index) in item.children"
							:key="index"
							:item="route"
							:base-path="route.path"
						/>
					</template>
				</el-submenu>
			</template>
		</div>
	</div>
</template>

<script>
export default {
  name: "FunctionMenuItemCollapse",
  props: {
    item: {
      type: Object,
    },
    /** 是否显示icon */
    showIcon: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    jumpPage(url) {
      this.$router.push(url);
    },
  },
};
</script>

<style lang="scss" scoped>
#collapse-menu {
	.menu-wrapper {
		::v-deep .el-submenu__title {
			width: 64px !important;
			height: 57px !important;
			padding: 0 !important;
      // 取消line-height
      line-height: 1 !important;
		}

		::v-deep .el-icon-arrow-right {
			display: none !important;
		}

		::v-deep .el-menu-item {
			color: #fff !important;
		}

    ::v-deep .el-submenu__title {
      border-radius: 4px;
      overflow: hidden;
    }

     .is-active {
      ::v-deep .el-submenu__title {
        background-color: #3887F5 !important;
      }
    }
	}

  ::v-deep .el-submenu__title {
    &:hover {
      background-color: #3887F5 !important;
    }
  }

	::v-deep .is-active {
    color: #fff !important;
    background-color: #3887F5 !important;
    border-radius: 6px;

		::v-deep .el-submenu__title {
			&:hover {
				background-color: #3887F5 !important;
			}
		}
	}



  ::v-deep .el-menu-item {
    &:not(.is-active) {
      color: #ffffff !important;
    }



    &:hover {
      background-color: #3887F5 !important;
    }
  }

  ::v-deep .el-submenu__icon-arrow {
    color: #ffffff !important;
  }
}

</style>
