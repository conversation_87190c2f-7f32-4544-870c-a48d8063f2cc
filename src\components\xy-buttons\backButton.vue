
<template>
  <el-button type="warning" size="mini" icon="el-icon-arrow-left" @click="goBack">返回</el-button>
</template>

<script>
import { scrollTop } from "@/utils/toolMethods";
export default {
  name: "BackButton",
  methods: {
    goBack() {
      // 关闭详情页
      this.$store.dispatch("setShowDetailPage", false);
      // 修改面包屑
      this.$store.dispatch("setBreadcrumb", [this.$route.meta.title]);
      // 隐藏返回按钮
      this.$store.dispatch("setShowBackButton", false);

      scrollTop();
    }
  }
};
</script>

<style scoped>

</style>
