/** 生成一个8位的随机字符 */
export function randomWord() {
  let str = "";
  for (let i = 0; i < 8; i++) {
    str += Math.floor(Math.random() * 10);
  }
  return str;
}


const basics = {
  isNull(o) {
    return (o === null || o === "" || o === undefined);
  },
  isObj(o) { // 是否对象
    return Object.prototype.toString.call(o).slice(8, -1) === "Object";
  },
  isObjNull(o) {
    return Object.keys(o).length === 0;
  },
  isArray(o) { // 是否数组
    return (Object.prototype.toString.call(o) === "[object Array]");
  },
  isFunction(o) { // 是否函数
    return Object.prototype.toString.call(o).slice(8, -1) === "Function";
  },
  isString(o) { // 是否字符串
    return Object.prototype.toString.call(o).slice(8, -1) === "String";
  },
  isFile(o) { // 是否文件
    return Object.prototype.toString.call(o).slice(8, -1) === "File";
  },
  isFormData(o) {
    return Object.prototype.toString.call(o).slice(8, -1) === "FormData";
  },
  isArrNull(o) {
    return o.length === 0;
  },
  isJSON(o) {
    if (typeof o === "string") {
      try {
        const obj = JSON.parse(o);
        // 当值为字符串的数字，JSON可以转成number
        if (typeof obj === "number") {
          return false;
        }

        return true;
      } catch (e) {
        return false;
      }
    } else {
      return false;
    }
  },
  isArrStr(arr) {
    var i = arr.length;
    while (i--) {
      if (this.isString(arr[i])) {
        return true;
      }
    }
    return false;
  },
  isNumber(o) {
    return Object.prototype.toString.call(o).slice(8, -1) === "Number";
  },
  Array: {
    remove(arr, remove) { // 根据内容或下标删除
      for (var i = 0; i < arr.length; i++) {
        var el = arr[i];
        if (basics.isNumber(remove)) {
          el = i;
        }

        if (el === remove) {
          for (var a = i; a < arr.length; a++) {
            arr[a] = arr[a + 1];
          }

          arr.length = arr.length - 1;
        }
      }
      return arr;
    }
  },
  Number: { // 数字基本操作
    addition(arg1, arg2) { // 加法
      var r1, r2, m;
      try {
        r1 = arg1.toString().split(".")[1].length;
      } catch (e) {
        r1 = 0;
      }
      try {
        r2 = arg2.toString().split(".")[1].length;
      } catch (e) {
        r2 = 0;
      }
      m = Math.pow(10, Math.max(r1, r2));
      return (arg1 * m + arg2 * m) / m;
    },
    subtraction(arg1, arg2) { // 减法
      var r1, r2, m, n;
      try {
        r1 = arg1.toString().split(".")[1].length;
      } catch (e) {
        r1 = 0;
      }
      try {
        r2 = arg2.toString().split(".")[1].length;
      } catch (e) {
        r2 = 0;
      }
      m = Math.pow(10, Math.max(r1, r2));
      // 动态控制精度长度
      n = (r1 >= r2) ? r1 : r2;
      const temp = ((arg1 * m - arg2 * m) / m).toFixed(n);
      return Number(temp);
    },
    accMul(arg1, arg2, precision = 2) { // 乘法
      var m = 0;
      var s1 = arg1.toString();
      var s2 = arg2.toString();
      try {
        m += s1.split(".")[1].length;
      } catch (e) {
        return false;
      }
      try {
        m += s2.split(".")[1].length;
      } catch (e) {
        return false;
      }
      if (arg1 && arg2) {
        let num = Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m);
        try {
          num = (num.toString().split(".")[1].length > precision) ? Number(num.toFixed(precision)) : num;
        } catch (e) {
          return false;
        }
        return num;
      } else {
        return 0;
      }
    },
    division(arg1, arg2, precision = 2) { // 除法
      var t1 = 0;
      var t2 = 0;
      var r1;
      var r2;
      try {
        t1 = arg1.toString().split(".")[1].length;
      } catch (e) {
        return false;
      }
      try {
        t2 = arg2.toString().split(".")[1].length;
      } catch (e) {
        return false;
      }
      r1 = Number(arg1.toString().replace(".", ""));
      r2 = Number(arg2.toString().replace(".", ""));
      if (arg1 && arg2) {
        let num = (r1 / r2) * Math.pow(10, t2 - t1);
        try {
          num = (num.toString().split(".")[1].length) > precision ? Number(num.toFixed(precision)) : num;
        } catch (e) {
          return false;
        }
        return num;
      } else {
        return 0;
      }
    },
    percent(arg1, arg2, precision = 2) {
      if (arg1 && arg2) {
        const n = this.division(arg1, arg2, 4);
        const m = this.accMul(n, 100).toFixed(precision);
        return m + "%";
      } else {
        return "0%";
      }
    }
  },
  Objcet: {
    delete(obj, str) {
      if (basics.isArray(str)) {
        str.forEach(item => {
          return this.delete(obj, item);
        });
      } else {
        if (!basics.isNull(obj) && !basics.isNull(obj[str])) {
          delete obj[str];
        }
      }
      return obj;
    }
  }
};
basics.install = function(Vue) {
  Vue.prototype.basics = basics;
};
export default basics;

