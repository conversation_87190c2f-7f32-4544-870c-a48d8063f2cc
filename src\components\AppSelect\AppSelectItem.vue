<template>
	<div @click="handleClick" class="flex cursor-pointer">
		<div class="box-border" :class="[style]">
			<p>{{ data[label] }}</p>
		</div>
	</div>
</template>

<script>
const baseStyle =
	"py-[4px] px-[12px] bg-[#EDF3F7] rounded-tl-[4px] rounded-br-[4px] rounded-tr-[4px] rounded-bl-[4px] opacity-100 text-[14px] text-[#333333] ";

const activeStyle = baseStyle + "text-[#3887F5] border-[1px] border-solid border-[#3887F5]";

export default {
  name: "AppSelectItem",
  props: {
    active: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    /** 展示的字段名 */
    label: {
      type: String,
      default: "",
    },
    /** 展示的字段值 */
    value: {
      type: String,
      default: "",
    },
  },
  computed: {
    style() {
      return this.active ? activeStyle : baseStyle;
    },
  },
  methods: {
    handleClick() {
      this.$emit("click", this.data);
    },
  },
};
</script>

<style scoped lang="scss"></style>
