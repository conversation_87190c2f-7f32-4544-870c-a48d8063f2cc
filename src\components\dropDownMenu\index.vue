<template>
  <div class="dropDownMenu">
    <div class="menu d-flex flex-align-center cursor-pointer" :class="{active:i.value===value}" v-for="(i,index) in menu" @click="handleSwitch(i)" :key="index">
      <i v-if="i.icon" class="iconfont" :class="i.icon" />
      <p>{{i.label}}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: "DropDownMenu",
  props: {
    menu: {
      type: Array,
      default: () => []
    },
    value: {
      type: [Number, String],
      default: ""
    }
  },
  methods: {
    handleSwitch(data) {
      this.$emit("input", data.value);
      this.$emit("menuClick", data);
    }
  }
};
</script>

<style scoped lang="scss">
.dropDownMenu{
  box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.12);
  border-radius: 2px 2px 2px 2px;
  overflow: hidden;
  .menu{
    background: #FFFFFF;
    padding: 6px 12px;
    &:hover{
      background: #EDF3F7;
    }
    i{
      font-size: 16px;
      margin-right: 8px;
    }
    p{
      min-width: 56px;
      font-size: 14px;
      font-weight: 400;
      color: #333333;
    }
    &.active{
      background: #EDF3F7;
      p{

        color: #3887F5;
      }
    }
  }
}
</style>
