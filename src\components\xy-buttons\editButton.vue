
<template>
  <app-button :disabled="disabled"  style-type="text" :type="getListNextInfo.type||''"   @click="edit">
    <slot>
      {{getListNextInfo.activeLabel||getListNextInfo.label}}
    </slot>
  </app-button>
</template>

<script>

import AppButton from "components/appButton/index.vue";
import buttonPermissionsEnum from "@/enum/buttonPermissionsEnum";

export default {
  name: "EditButton",
  components: { AppButton },
  props: {
    disabled: {
      type: Boolean,
      default: false
    },
    showField: {
      type: String,
      default: "",
      desc: "权限展示按钮"
    },
    tips: {
      type: String,
      default: "",
      desc: "弹窗提示"
    },
    request: {
      type: Object,
      default: () => {
        return {
          updateUrl: () => Promise.resolve()
        };
      }
    },
    isRefresh: {
      type: Boolean,
      default: true,
      desc: "是否刷新页面"
    },
    isTableRefresh: {
      type: Boolean,
      default: true,
      desc: "操作后刷新页面还是刷新表格"
    },
    isHandleAuto: {
      type: Boolean,
      default: true,
      desc: "是否用组件内的方法"
    },
    data: {
      type: Object,
      default: () => ({})
    },
    list: {
      type: Array,
      default: () => ([])
    },
    switchKey: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      filed: buttonPermissionsEnum.UPDATE
    };
  },
  computed: {
    getListNextInfo() {
      const index = this.list.findIndex(item => String(item.value) === String(this.data[this.switchKey]));
      return this.list[index + 1] || this.list[0];
    }
  },
  methods: {
    edit() {
      if (!this.isHandleAuto) return this.$emit("click");
      this.$confirm(this.tips || this.getListNextInfo.tip || "", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      })
        .then(() => {
          this.handleClick();
        })
        .catch(() => {});
    },
    /**
     * @description:
     * @return {*}
     */
    handleClick() {
      this.request.updateUrl({
        ...this.data,
        [this.switchKey]: this.getListNextInfo.value
      }).then(() => {
        this.$message.success("操作成功!");
        this.$emit("click");
        if (this.isRefresh) {
          if (this.isTableRefresh) {
            // 刷新表格
            this.$store.dispatch("setIsTableRefresh");
          } else {
            // 刷新页面
            this.$store.dispatch("setIsRouterRefresh");
          }
        }
      }).catch(() => {});
    }
  }
};
</script>

<style lang='scss' scoped>
  .inline-block{

  }
</style>
