<template>
  <div>
    <div class="border-0 border-b-[1px] border-solid border-[#EEEEEE] flex space-x-[40px] px-[24px]">
      <div class="text-[16px] cursor-pointer text-[#333333] flex items-center h-[50px]"
           :class="{active:index===activeIndex}"
           @click="handleSwitchTab(index)"
           v-for="(i,index) in tabList" :key="i.key">
        {{i.label}}
        <i v-if="i.hasUnread>0" class="w-[8px] h-[8px] bg-[#EB4738] rounded-[8px] ml-[4px] border-[1px] border-solid border-[#FFFFFF]" />
      </div>
    </div>
    <div class="h-[280px] overflow-y-auto">
      <div class="px-[24px]" v-for="(i) in list" :key="i.id">
        <!--成单喜报   -->
        <template v-if="activeTabData.key==='caseReport'">
          <div @click="handleCaseReport(i)" class="border-0 border-b-[1px] border-solid border-[#EEEEEE] pt-[16px] pb-[16px] text-[#333333] hover:text-[#3887F5] cursor-pointer">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <p class="text-[16px]  max-w-[248px] text-ellipsis">{{i.title}}</p>
<!--                <i v-if="i.read===0" class="w-[8px] h-[8px] ml-[4px] bg-[#EB4738] rounded-[8px] border-[1px] border-solid border-[#FFFFFF]" />-->
              </div>
              <p class="text-[12px] text-[#999999]">{{i.pubTime}}</p>
            </div>
            <p class="text-[14px] text-[#666666] text-ellipsis pt-[7px]">
              {{i.description}}
            </p>
          </div>
        </template>

        <!--   更新公告   -->
        <template  v-if="activeTabData.key==='updateAnnouncements'">
          <div @click="handleUpdateAnnouncements(i)" class="text-[14px] text-[#333333] flex  justify-between items-center h-[36px] hover:text-[#3887F5] cursor-pointer">
            <div class="flex items-center">
              <p class="max-w-[260px] text-ellipsis">
                {{i.title}}
              </p>
<!--              <i  v-if="i.read===0" class="w-[8px] h-[8px] ml-[4px] bg-[#EB4738] rounded-[8px] border-[1px] border-solid border-[#FFFFFF]" />-->
            </div>
            <p class="text-[12px] text-[#999999]">{{i.pubTime}}</p>
          </div>
        </template>

        <!-- 系统消息     -->
        <template v-if="activeTabData.key==='systemMessage'">
          <el-tooltip :open-delay="800" @input="handleSystemMessage($event,i)" class="item" effect="dark" :content="i.content" placement="bottom">
            <div class="text-[14px] text-[#333333] flex  justify-between items-center h-[36px] hover:text-[#3887F5] cursor-pointer">
             <div class="flex items-center">
               <p class="max-w-[260px] text-ellipsis">
                 {{i.content}}
               </p>
<!--               <i  v-if="i.read===0" class="w-[8px] h-[8px] ml-[4px] bg-[#EB4738] rounded-[8px] border-[1px] border-solid border-[#FFFFFF]" />-->
             </div>
              <p class="text-[12px] text-[#999999]">{{i.createTime}}</p>
            </div>
          </el-tooltip>

        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { lcGoodNewsPage, lcNoticePage } from "@/api/workbenchManagement.js";
import { lcSysNotificationPage, lcSysNotificationRead } from "@/api/common.js";

export default {
  name: "MessageNotifications",
  props: {
    state: {
      type: Boolean,
      default: false
    },
  },
  data() {
    return {
      activeIndex: 0,
      /* 分页数据*/
      pageData: {
        currentPage: 1,
        pageSize: 10,
      },
      loading: false,
      list: [],
      /* 没有更多了*/
      noMore: false,
    };
  },
  computed: {
    /* 当前tab选中数据*/
    activeTabData() {
      return this.tabList[this.activeIndex];
    },
    disabled () {
      return this.loading || this.noMore;
    },
    tabList(){
      return [{
        label: "活动通知",
        key: "caseReport",
        unRedKey: "goodNewsUnreadNum",
        /* 是否有未读消息*/
        hasUnread: this.$store.getters.getNoticeCounts.goodNewsUnreadNum,
        api: lcGoodNewsPage
      }, {
        label: "更新公告",
        key: "updateAnnouncements",
        unRedKey: "noticeUnreadNum",
        hasUnread: this.$store.getters.getNoticeCounts.noticeUnreadNum,
        api: lcNoticePage
      }, {
        label: "系统消息",
        key: "systemMessage",
        unRedKey: "sysNotificationNum",
        hasUnread: this.$store.getters.getNoticeCounts.sysNotificationNum,
        api: lcSysNotificationPage,
      }];
    }
  },
  watch: {
    state(){
      if(this.state){
        this.initParams();
        this.getData();
      }
    },
  },
  methods: {
    /* 成单喜报点击*/
    handleCaseReport(data){
      this.readRed(data);
      this.$router.push({
        path: "/goodNews",
        query: {
          id: data.id
        }
      });
    },
    /* 更新公告点击*/
    handleUpdateAnnouncements(data){
      this.readRed(data);
      this.$router.push({
        path: "/updateAnnouncements",
        query: {
          id: data.id
        }
      });
    },
    /* 系统消息点击*/
    handleSystemMessage(state, data){
      if(state){
        lcSysNotificationRead({ id: data.id });
        if(data.read === 0){
          this.$store.commit("REDUCE_NOTICE_COUNTS", this.activeTabData.unRedKey);
        }
        this.readRed(data);
      }
    },
    /* 已读红标*/
    readRed(data){
      this.$set(data, "read", 1);
    },
    /* tab切换*/
    handleSwitchTab(index){
      if(this.activeIndex === index) return;
      this.initParams();
      this.activeIndex = index;
      this.getData();
    },
    /* 初始话参数*/
    initParams(){
      this.pageData.currentPage = 1;
      this.loading = false;
      this.noMore = false;
      this.list = [];
    },
    /* 加载更多*/
    load(){
      this.pageData.currentPage++;
      this.getData();
    },
    /* 获取数据*/
    getData(){
      this.loading = true;
      if(!this.activeTabData.api) return;
      const { currentPage, pageSize } = this.pageData;
      this.activeTabData.api({ currentPage, pageSize }).then((res) => {
        const { records = [], total = 0 } = res;
        this.list = [...this.list, ...records];
        /* 总数大于等于加载数就没有翻页了*/
        if(this.list.length >= total){
          this.noMore = true;
        }
      }).finally(() => {
        this.loading = false;
      });
    }
  }
};
</script>

<style scoped lang="scss">
.active{
  color: #3887F5;
  position: relative;
  &::after{
    content: '';
    position: absolute;
    left: 0;
    right: 0;
    bottom: 0;
    height: 2px;
    background: #3887F5;
  }
}
</style>
