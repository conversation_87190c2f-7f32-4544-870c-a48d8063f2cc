# -*- coding: utf-8 -*-
# This file is auto-generated, don"t edit it. Thanks.

import urllib
import requests
import json

# JIRA群secret
headers = {"Content-Type": "application/json;charset=utf-8"}
# JIRA群WebHook
#dingtalk = "https://oapi.dingtalk.com/robot/send?access_token=618e86878d2348173d8975d6c737a64f3901b06c7a623fb5d5b047f7786de2ad" #自我测试群
dingtalk = "https://oapi.dingtalk.com/robot/send?access_token=7ec65a4dbcdba02d0a6ce223bf110f436d5e96bc16312d73b9c523079d033545" #法律通知群

def msg(title, content, atMobiles=""):
    json_text= {
        "msgtype": "actionCard",
        "actionCard": {
            "title": title,
            "text": content
        },
        "at": {
            "atMobiles": atMobiles,
            "atUserIds": [],
            "isAtAll": False
        }
    }

    # print(requests.post(dingtalk,json.dumps(json_text),headers=headers).content)
    return requests.post(dingtalk,json.dumps(json_text),headers=headers).content


 
if __name__ == "__main__":
    ServiceName = "${ServiceName}"
    job_name = "$CI_JOB_NAME"
    project_name = "$CI_PROJECT_NAME"
    job_url = "$CI_JOB_URL"
    job_status = "$CI_JOB_STATUS"
    user_name = "$RUN_JOB_USER"
    deploy_env = "$DEPLOY_ENV"
    title = "%s发布通知【%s环境】" %(ServiceName,deploy_env)
    if job_status == "success":
        content = "### <font color=\"#1E90FF\">{0}</font>\n ---\n + 发布服务: [{1}]({2}) \n + 发布状态: <font color=\"#00DD00\">成功</font>\n + 发布环境:  {3}\n + 执行人:  {4}\n".format(title, project_name, job_url, deploy_env, user_name)
        msg(title, content)
    else:
        content = "### <font color=\"#1E90FF\">{0}</font>\n ---\n + 发布服务:  [{1}]({2}) \n + 发布状态:  <font color=\"#FF0000\">失败</font>\n + 发布环境:  {3}\n + 执行人:  {4}\n + 失败步骤:  {5}\n".format(title, project_name, job_url, deploy_env, user_name,job_name)
        msg(title, content)
