<template>
  <div
    v-show="visible"
    class="fixed top-[40px] -translate-x-50 left-[50%] flex items-center z-10 px-[16px]  py-[6px] bg-[rgba(0,0,0,0.7)] rounded-[8px]">
    <p class="text-[14px] text-[#FFFFFF] pr-[16px]" v-html="msg" />
    <app-button size="medium" type="success" @click="refreshNow">立即刷新</app-button>
  </div>
</template>

<script>
import AppButton from "components/appButton/index.vue";

export default {
  name: "AppTheListUpdatesTheData",
  components: { AppButton },
  computed: {
    visible: {
      get() {
        return this.$store.getters["getCaseSourcePushToast"].show;
      },
      set(val) {
        this.$store.commit("SET_CASE_SOURCE_PUSH_TOAST_SHOW", val);
      },
    },
    msg(){
      return  this.$store.getters["getCaseSourcePushToast"].msg;
    }
  },
  methods: {
    refreshNow(){
      this.$store.commit("SET_TABLE_CONFIG", {
        currentPage: 1
      });
      this.$store.commit("SET_CASE_SOURCE_PUSH_TOAST_SHOW", false);
    }
  }
};
</script>

<style scoped lang="scss">
</style>
