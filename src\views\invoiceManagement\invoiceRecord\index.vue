<template>
  <div class="bg-[#FFFFFF] p-[24px]">
    <!-- 表格内容 -->
    <xy-page ref="xyPage" :request="request" :column="tableColumn" :query="query" index :show-search="true"
      :show-search-head="false" :is-margin="false">
      <template #handleButton>
        <el-table-column fixed="right" width="180px" label="操作">
          <template #default="{ row }">
            <app-button  style-type="text" type="publicize"
              @click="handleShowDetail(row)">详情
            </app-button>
            <app-button v-if="row.checkStatus === 2" style-type="text" type="primary"
              @click="handleDownload(row)">下载电子发票
            </app-button>
          </template>
        </el-table-column>
      </template></xy-page>

    <!-- 发票详情弹窗 -->
    <invoice-detail
      v-if="detailVisible"
      :visible.sync="detailVisible"
      :invoice-id="currentInvoiceId"
    />
  </div>
</template>

<script>
import { institutionInvoiceInvoiceRecordPage } from "@/api/invoice.js";
import AppButton from "@/components/appButton/index.vue";
import XyPage from "@/components/xy-page/index.vue";
import InvoiceDetail from "@/views/invoiceManagement/invoiceRecord/components/InvoiceDetail.vue";
import { handleDownloadInvoice, tableColumn } from "./index.js";

export default {
  name: "InvoiceRecordPage",
  components: {
    XyPage,
    AppButton,
    InvoiceDetail
  },
  data() {
    return {
      searchForm: {
        dateRange: []
      },
      query: {},
      // 表格列定义
      tableColumn,
      // 请求接口
      request: {
        getListUrl: institutionInvoiceInvoiceRecordPage
      },
      // 详情相关数据
      detailVisible: false,
      currentInvoiceId: null
    };
  },
  methods: {
    // 显示详情弹窗
    handleShowDetail(row) {
      this.currentInvoiceId = row.id;
      this.detailVisible = true;
    },

    // 下载操作
    handleDownload(row) {
      handleDownloadInvoice(row.id);
    },
  }
};
</script>
