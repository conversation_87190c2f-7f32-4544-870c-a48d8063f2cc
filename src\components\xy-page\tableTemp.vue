<!--
 * @Author: your name
 * @Date: 2021-09-01 10:46:14
 * @LastEditTime: 2021-09-02 19:53:33
 * @LastEditors: Please set LastEditors
 * @Description: 表格自定的template
 * @FilePath: \law-front\src\components\xy-page\tableTemp.vue
-->
<template>
  <div class="" :class="[(basics.isNull(itemList.truncate)||itemList.truncate)?'truncate':'']">
    <!--  列表内容   -->
    <template>
         <!-- 自定义常用组件 -->
        <component :is="itemList.renderComponentName" v-if="itemList.renderComponentName" :row-list="rowList" :item-list="itemList" />
        <template v-else-if="!itemList.render&&!itemList.renderComponentName">
        <span v-if="itemList.filter" :style="isCustomStyle">{{ crItemProp | parseStatus(itemList.filter) }}</span>
        <span v-else-if="itemList.filterUrl">{{ crItemProp | parseStatus(itemList.syncData.data ,itemList.syncData.label,itemList.syncData.value) }}</span>
        <span v-else-if="itemList.filterTag" :class="`${ crItemProp ? 'success':'warning'}`">{{
            crItemProp | parseStatus(itemList.filterTag)
            }}</span>
        <span v-else-if="itemList.dateFormat">{{ crItemProp | parseTimeTwo(itemList.dateFormat) }}</span>
        <span v-else-if="itemList.type === 'progress'">{{ formatPercent(crItemProp) }}</span>
        <span v-else-if="itemList.type === 'money'">{{ priceYuan(crItemProp)  }}</span>
        <span v-else-if="itemList.type === 'twoDecimalPlaces'">{{ priceNumber(crItemProp)  }}</span>
        <span v-else-if="itemList.type === 'flbTransformed'">{{ priceNumberTwo(crItemProp)  }}</span>
        <span v-else>{{ basics.isNull(crItemProp) ? '-' : crItemProp }}</span>
        </template>
        <!--  渲染自定义render   -->
        <template-render
        v-else
        :render="itemList.render"
        :results="{'row':rowList,'column': itemList}"
        />
    </template>
  </div>
</template>

<script>
import { TemplateRender } from "@/utils/render";
import { formatPercent, priceNumber, priceNumberTwo, priceYuan } from "@/utils/toolMethods";

// 自定义组件
import FilesShow from "./columnComponents/filesShow";
import InputText from "./columnComponents/inputText.vue";
import LookPhone from "components/xy-page/columnComponents/lookPhone.vue";
export default {
  name: "Tables",
  components: {
    TemplateRender, FilesShow, InputText, LookPhone
  },
  props: {
    itemList: {
      type: Object,
      default: () => {},
      desc: "表格定义的字段"
    },
    rowList: {
      type: Object,
      default: () => {},
      desc: "表格返回的字段"
    }
  },
  computed: {
    crItemProp() {
      return !this.basics.isObjNull(this.rowList) && !this.basics.isObjNull(this.itemList) ? this.rowList[this.itemList.prop] : "";
    },
    isCustomStyle() {
      return this.itemList.filterItemStyleRule ? this.itemList.filterItemStyleRule[this.crItemProp] : "";
    }
  },
  methods: {
    priceNumberTwo,
    priceNumber,
    formatPercent,
    priceYuan
  }
};
</script>
<style lang="scss" scoped>
	.success,
	.warning {
		padding-left: 12px;
		position: relative;

		&:before {
			content: '';
			position: absolute;
			left: 0;
			top: 6px;
			width: 6px;
			height: 6px;
			border-radius: 100%;
		}
	}

	.success {
    color: $primary-color;
		&:before {
			background: $primary-color;
		}
	}

	.warning {
     color: #ff8c0b;
		&:before {
			background: #ff8c0b;
		}
  }
</style>
