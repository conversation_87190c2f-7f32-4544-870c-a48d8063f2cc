module.exports = {
    "env": {
        "browser": true,
        "commonjs": true,
        "es6": true,
        "node":true
    },
    "extends": [
        "eslint:recommended",
        "plugin:vue/essential"
    ],
    "globals": {
        "Atomics": "readonly",
        "SharedArrayBuffer": "readonly",
        'conn': 'writable'
    },
    "parserOptions": {
        "ecmaVersion": 2018,
        "sourceType": "module",
        "parser": "babel-eslint"
    },
    "plugins": [
        "vue"
    ],
  rules: {
    // vue 组件名使用连接符
    "vue/component-name-in-template-casing": ["error", "kebab-case"],
    // .vue文件中name属性使用大驼峰
    "vue/name-property-casing": ["error", "PascalCase"],
    semi: [2, "always"],
    // 没有使用的变量警告
    "no-unused-vars": ["warn"],
    // 箭头函数前后必须要有空格
    "arrow-spacing": [
      "warn",
      {
        before: true,
        after: true,
      },
    ],
    // 要求大括号内必须有空格
    "object-curly-spacing": ["error", "always"],
    // 缩进
    indent: [2, 2],
    // 标签闭合
    "vue/html-self-closing": [
      "error",
      {
        html: {
          void: "always",
        },
      },
    ],
    // 标签后加空格
    "vue/html-closing-bracket-spacing": [
      "error",
      {
        startTag: "never",
        endTag: "never",
        selfClosingTag: "always",
      },
    ],
    // 关键字后加空格
    "space-after-keywords": [0, "always"],
    // 对象字面量中冒号的前后空格
    "key-spacing": [1, { beforeColon: false, afterColon: true }],
    // 逗号前后的空格
    "comma-spacing": 1,
    // 符号后加空格
    "space-infix-ops": ["warn", { int32Hint: false }],
    // 注释 // 或 /* 之后必须有一个空格
    "spaced-comment": ["warn", "always"],
    // js中应使用单引号替代双引号
    quotes: ["warn", "double"],
    // 使用连字符
    "vue/attribute-hyphenation": ["error", "always"],
    "vue/multi-word-component-names": ["off", "always"],
    // vue 组件中属性顺序
    "vue/order-in-components": [
      "warn",
      {
        order: [
          "el",
          "name",
          "key",
          "parent",
          "head",
          "functional",
          ["delimiters", "comments"],
          ["components", "directives", "filters"],
          "extends",
          "mixins",
          ["provide", "inject"],
          "ROUTER_GUARDS",
          "layout",
          "middleware",
          "validate",
          "scrollToTop",
          "transition",
          "loading",
          "inheritAttrs",
          "model",
          ["props", "propsData"],
          "emits",
          "setup",
          "asyncData",
          "data",
          "onLoad",
          "onShow",
          "fetch",
          "computed",
          "watch",
          "watchQuery",
          "LIFECYCLE_HOOKS",
          "methods",
          ["template", "render"],
          "renderError",
        ],
      },
    ],
  },
};
