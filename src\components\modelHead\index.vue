
<!-- 模块顶部标题样式 -->
<template>
  <div :class="['xy-model-head', background?'background':'']">
    <div>
      <span class="title" :style="`font-size:${size}px;font-weight:${weight}`">{{ title }}</span>
      <span>
        <span v-for="(item,index) in titleList" :key="index"><span>{{item.title}}</span><span :style="item.color">{{item.value}}</span><span v-if="index <titleList.length -1" style="padding:0 10px">/</span></span>
      </span>
    </div>
    <div class="right-handle">
      <!-- 自定义按钮 -->
      <slot name="paraphrase" />
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  name: "Index",
  props: {
    title: {
      type: String,
      default: "标题"
    },
    background: {
      type: Boolean,
      default: true
    },
    size: {
      type: [String, Number],
      default: 14
    },
    weight: {
      type: [String, Number],
      default: 500
    },
    titleList: {
      type: Array
    }
  }
};
</script>

<style lang="scss" scoped>
.xy-model-head{
  @extend .flex;
  padding: 0 $distance-normal;
  width: 100%;
  height: 50px;
  box-sizing: border-box;
  &.background{
    background: $menu-group-bg-color;
  }
  .title{
    position: relative;
    padding-left: $distance-normal;
    line-height: 20px;
    &:before{
      content: '';
      position: absolute;
      left: 0;
      top: 2px;
      width: 4px;
      height: 16px;
      background: $primary-color;
    }
  }
  .right-handle{
    font-size: $font-size-mini;
    @extend .flex-center;
    >div{
      margin-left: $distance-normal;
      cursor: pointer;
      i{
        margin-right: $distance-mini;
      }
    }
    .collapsed{
      &:hover{
        color: $primary-color;
      }
    }
  }
}
</style>
