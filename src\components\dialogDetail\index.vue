<template>
	<div class="detail">
		<dialogDetail
			:dialog-visible.sync="dialogVisible"
			@close="close"
			:show-footer="false"
			:options="{ title: '详情', width: '45%' }"
		>
			<div class="lists-theme" v-for="(item, key) in detailInfos" :key="key">
				<div v-if="!item.prop || (item.prop && form[item.prop])">
					<h4>{{ item.name }}</h4>
					<el-form
						v-if="!item.render"
						:model="form"
						label-position="left"
						:label-width="item.labelWidth"
						class="flex item-wrap"
					>
						<div
							class="flex item-wrap"
							v-for="listitem in form[item.prop] || [1]"
							:key="listitem.id"
						>
							<el-form-item
								class="li"
								v-for="(li, i) in item.lists"
								:key="i"
								:label="li.label"
								:style="li.line ? 'width:100%' : ''"
								v-show="!li.formHidden"
							>
								<div v-if="!li.render">
									<span v-if="li.filter">{{
										((item.prop && listitem[li.prop]) || form[li.prop]) | parseStatus(li.filter)
									}}</span>
									<span
										v-else-if="li.filterTag"
										:class="`${form[li.prop] ? 'success' : 'warning'}`"
										>{{
											((item.prop && listitem[li.prop]) || form[li.prop])
												| parseStatus(li.filterTag)
										}}</span
									>
									<span v-else-if="li.dateFormat">{{
										((item.prop && listitem[li.prop]) || form[li.prop])
											| parseTimeTwo(li.dateFormat)
									}}</span>
									<span v-else>{{
										basics.isNull((item.prop && listitem[li.prop]) || form[li.prop])
											? '-'
											: (item.prop && listitem[li.prop]) || form[li.prop]
									}}</span>
									<!-- <p class="desc">{{li.desc}}</p> -->
								</div>

								<!--  渲染自定义render   -->
								<template
									v-else
									:render="li.render"
									:results="{ row: (item.prop && listitem) || form }"
								/>
							</el-form-item>
						</div>
					</el-form>
					<!--  渲染自定义render   -->
					<template v-else :render="item.render" :results="{ row: form }" />
				</div>
			</div>
			<slot name="handleForm" />
		</dialogDetail>
	</div>
</template>

<script>
import dialogDetail from "@/components/xy-dialog/index.vue";

export default {
  name: "Detail",
  components: {
    dialogDetail,
  },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false,
    },
    detailInfos: {
      type: Array,
      default: () => {
        return [];
      },
    },
    dataRow: {
      type: Object,
      default: () => {},
    },
    getDetailUrl: {
      type: Function,
      default: () => Promise.resolve(),
    },
    detailId: [String, Number],
  },
  data() {
    return {
      form: {},
    };
  },
  created() {
    if (this.dataRow) {
      this.form = this.dataRow;
      console.log(this.form, 99999);
    } else {
      this.getDetail();
    }
  },
  methods: {
    getDetail() {
      this.getDetailUrl({ id: this.detailId }).then(res => {
        this.form = res;
      });
    },
    close() {
      this.form = {};
      this.$emit("update:dialog-visible", false);
    },
    sumbit() {
      this.$emit("submit", true);
    },
  },
};
</script>

<style lang="scss" scoped>
.lists-theme {
	margin-bottom: 30px;
	border: 1px solid transparent;
	border-radius: 2px;
	color: $text-color-desc;
	h4 {
		margin-bottom: 10px;
		padding-left: $distance-normal;
		line-height: 44px;
		background: #f8f8f8;
		color: $text-color-secondary;
		font-weight: 600;
	}
	.item-wrap {
		flex-wrap: wrap;
		width: 100%;

		&:last-child {
			border: none !important;
		}
		border-bottom: 1px solid #f5f5f5;
	}
	.li {
		width: 50%;
		padding-left: $distance-normal;
		color: $text-color-desc;
		box-sizing: border-box;
		// display: flex;
		font-size: 14px;
		margin-bottom: 0;

		/deep/ label {
			color: $text-color-desc;
		}

		/deep/ .el-form-item__content {
			line-height: 30px;
		}
		span {
			font-size: 14px;
			color: $text-color-desc;
			display: inline-block;
		}

		p {
			font-size: 12px;
			// width: calc(100% - 120px);
		}
	}
}
</style>
