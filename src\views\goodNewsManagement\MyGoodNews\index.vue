<template>
	<div class="bg-[#FFFFFF]">
		<div class="px-[24px] pt-[16px] pb-[24px]">
			<xy-page
				:is-margin="false"
				ref="page"
				:column="tableColumn"
				:request="request"
				:query="query"
			>
				<template #pageMiddleContainer>
					<div class="mb-[24px]">
						<good-news-square-banner />
					</div>
				</template>
				<template #searchBtn>
					<app-button
						type="success"
						@click="turnToUploadGoodNews"
						icon="icon-shangchuanxibao !text-[16px]"
						>上传喜报</app-button
					>
				</template>
				<template #handleButton>
					<el-table-column fixed="right" width="200px" label="操作">
						<template #default="{ row }">
							<app-button
								v-if="row.checkStatus === 0"
								style-type="text"
								type="danger"
								size="medium"
								@click="handleCancel(row)"
								>撤销审核
							</app-button>
							<app-button
								v-if="row.checkStatus === 1"
								@click="download(row)"
								style-type="text"
								type="primary"
								size="medium"
								>下载我的喜报
							</app-button>
							<app-button
								v-if="[2].includes(row.checkStatus)"
								style-type="text"
								type="primary"
								size="medium"
								@click="handleReUpload(row)"
								>查看
							</app-button>
							<app-button
								v-if="[9].includes(row.checkStatus)"
								style-type="text"
								type="primary"
								size="medium"
								@click="handleReUpload(row)"
								>重新上传
							</app-button>
							<app-button
								v-if="[2, 9].includes(row.checkStatus)"
								@click="handleDelete(row)"
								style-type="text"
								type="danger"
								size="medium"
								>删除</app-button
							>
						</template>
					</el-table-column>
				</template>
			</xy-page>
		</div>
    <div class="fixed top-0 right-[-100%]">
			<good-news-preview class="w-[438px]" is-money :data="downloadData" ref="imgDom" />
		</div>
	</div>
</template>

<script>
import XyPage from "components/xy-page/index.vue";
import AppButton from "components/appButton/index.vue";
import { tableColumn } from "./index.js";
import { turnToUploadGoodNews } from "utils/turnPage";
import GoodNewsPreview from "views/goodNewsManagement/uploadGoodNews/components/goodNewsPreview/index.vue";
import html2canvas from "html2canvas";
import {
  lcGoodNewsV2Cancel,
  lcGoodNewsV2Delete,
  lcGoodNewsV2DetailById,
  lcGoodNewsV2MyGoodNews,
} from "@/api/goodNewsManagement";
import GoodNewsSquareBanner from "views/goodNewsManagement/GoodNewsSquare/components/GoodNewsSquareBanner.vue";

export default {
  name: "MyGoodNews",
  components: { GoodNewsSquareBanner, GoodNewsPreview, AppButton, XyPage },
  data() {
    return {
      request: {
        getListUrl: lcGoodNewsV2MyGoodNews,
      },
      tableColumn,
      query: {},
      downloadData: {},
    };
  },
  methods: {
    turnToUploadGoodNews() {
      turnToUploadGoodNews();
    },
    async download({ id }) {
      try {
        this.downloadData = await lcGoodNewsV2DetailById({ id });

        this.$nextTick(() => {
          const imgDom = this.$refs.imgDom;
          // 拿到目标dom调用一下html2canvas方法就能生成canvas对象了
          // 获取要转换的元素
          html2canvas(imgDom.$el, {
            useCORS: true, // 开启跨域设置，需要后台设置cors
          }).then(canvas => {
            // toDataURL函数生成img标签的可用数据  图片格式转成 base64
            let dataURL = canvas.toDataURL("image/png");
            console.log(dataURL);
            const a = document.createElement("a");
            a.href = dataURL;
            a.download = `${this.downloadData.institutionName}.png`;
            a.click();
          });
        });
      } catch (e) {
        console.log(e);
      }
    },
    /** 删除 */
    handleDelete(row) {
      this.$confirm("确定删除本条数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        lcGoodNewsV2Delete(row).then(() => {
          this.$message.success("删除成功");
          this.$store.dispatch("setIsTableRefresh");
        });
      });
    },
    /** 撤销审核 */
    handleCancel(row) {
      this.$confirm("确定撤销本条数据？", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(() => {
        lcGoodNewsV2Cancel(row).then(() => {
          this.$message.success("撤销成功");
          this.$store.dispatch("setIsTableRefresh");
        });
      });
    },
    /** 重新上传 */
    handleReUpload(row) {
      turnToUploadGoodNews({ id: row.id });
    },
  },
};
</script>
