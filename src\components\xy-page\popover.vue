<!-- 表格头部的释义 -->
<template>
  <div class="popover">
    <span>{{ itemList.label }}</span>
    <el-popover
        v-if="itemList.headTip"
        popper-class="dark-popover"
        placement="top-start"
        width="160"
        trigger="hover"
        :tabindex='999'
        :content="itemList.headTip">
        <span slot="reference"><i style="font-size: 12px;color: #999999;"  class="icons cursor iconfont icon-xianxingtubiao-7" /></span>
    </el-popover>

  </div>
</template>

<script>
export default {
  name: "Popover",
  props: {
    itemList: {
      type: Object,
      default: () => {},
      desc: "表格定义的字段"
    }
  }
};
</script>

<style lang="scss" scoped>
  .icons{
    margin-left: 3px;
    font-weight: normal;
  }
  .popover{
    display: inline-block;
  }
</style>
