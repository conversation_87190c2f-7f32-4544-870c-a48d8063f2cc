<template>
  <div class="case<PERSON>ey pl-[24px] pr-[24px]">
    <div class="text-[14px] text-[#333333] mb-[16px]">请设置关键词 <span class="text-[12px] text-[#999999] font-style">（选填）</span> </div>
    <el-row v-for="(item, index) in keysForm" :key="index" class="mb-[16px] flex items-center">
      <el-col :span="3">
        <span class="text-[14px] text-[#333333] mr-2">关键词{{ index+1 }}:</span>
      </el-col>
      <el-col :span="21">
        <el-input
          v-model="item['key']"
          type="text"
          :placeholder="`请输入关键词`"
          maxlength="6"
          show-word-limit
          @input="caseSure"
        />
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: "<PERSON><PERSON><PERSON>",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: () => {
        return {};
      }
    },
  },
  data() {
    return {
      keysForm: [
        { key: "" },
        { key: "" },
        { key: "" },
      ]
    };
  },
  created() {
    this.keysForm.forEach((item, index) => {
      item.key = this.detail.keyWords[index] || "";
    });
  },
  methods: {
    caseSure() {
      return { keyWords: this.keysForm.filter(item => item.key.trim() !== "").map(item => item.key)  };
    },
  },
};
</script>

<style lang="scss" scoped>
  .caseKey{

  }
</style>
