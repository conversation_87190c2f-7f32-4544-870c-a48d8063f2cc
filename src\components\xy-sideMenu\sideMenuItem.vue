<template>
  <div class="side-menu-item">
    <template v-if="childrenMenu.list && childrenMenu.list.length">
      <div class="group">{{ childrenMenu.title }}</div>
      <ul class="sub-menu">
        <li :class="['menu-item', $route.path === item.modulePath ? 'active':'']" v-for="(item, index) in childrenMenu.list" :key="index" @click="jumpPage(item.modulePath)">
          <span>{{ item.moduleName }}</span>
        </li>
      </ul>
    </template>
    <div v-else :class="['menu-item', $route.path === childrenMenu.modulePath ? 'active':'']" @click="jumpPage(childrenMenu.modulePath)"><span>{{ childrenMenu.moduleName }}</span></div>
  </div>
</template>

<script>
export default {
  name: "SideMenuItem",
  props: {
    childrenMenu: {
      type: Object
    }
  },
  methods: {
    jumpPage (url) {
      if (url) {
        this.$router.push(url);
      } else {
        this.$router.push("/home");
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.side-menu-item{
  font-size: $font-size-mini;
  text-align: center;
  .group{
    height: 50px;
    line-height: 50px;
    background: $menu-group-bg-color;
    box-sizing: border-box;
    border-bottom: 1px solid $weak-color;
  }
  .menu-item{
    text-align: center;
    line-height: 40px;
    border-bottom: 1px solid $weak-color;
    cursor: pointer;
    span{
      position: relative;
      &:before{
        content: '';
        position: absolute;
        left: -10px;
        top: 50%;
        width: 3px;
        height: 3px;
        background: #333333;
        border-radius: 100%;
      }
    }
    &:hover{
      color: $primary-color;
      span:before{
        background: $primary-color;
      }
    }
    &.active{
      color: $primary-color;
      background: #ffffff;
      span:before{
        background: $primary-color;
      }
    }
  }
}
</style>
