/**
 * 地区代码工具方法测试
 */
import { convertRegionCodesToCascaderFormat, getProvinceCodeFromRegionCode } from '../region-utils';

describe('region-utils', () => {
  describe('getProvinceCodeFromRegionCode', () => {
    test('应该正确处理6位地区代码', () => {
      expect(getProvinceCodeFromRegionCode(110100)).toBe(110000); // 北京市东城区 -> 北京市
      expect(getProvinceCodeFromRegionCode(320100)).toBe(320000); // 江苏省南京市 -> 江苏省
      expect(getProvinceCodeFromRegionCode(440100)).toBe(440000); // 广东省广州市 -> 广东省
    });

    test('应该正确处理4位地区代码', () => {
      expect(getProvinceCodeFromRegionCode(1101)).toBe(110000); // 北京市 -> 北京市
      expect(getProvinceCodeFromRegionCode(3201)).toBe(320000); // 南京市 -> 江苏省
    });

    test('应该正确处理直辖市代码', () => {
      expect(getProvinceCodeFromRegionCode(110000)).toBe(110000); // 北京
      expect(getProvinceCodeFromRegionCode(120000)).toBe(120000); // 天津
      expect(getProvinceCodeFromRegionCode(310000)).toBe(310000); // 上海
      expect(getProvinceCodeFromRegionCode(500000)).toBe(500000); // 重庆
    });

    test('应该处理无效输入', () => {
      expect(getProvinceCodeFromRegionCode(null)).toBe(null);
      expect(getProvinceCodeFromRegionCode(undefined)).toBe(null);
      expect(getProvinceCodeFromRegionCode(0)).toBe(null);
    });
  });

  describe('convertRegionCodesToCascaderFormat', () => {
    test('应该正确转换地区代码数组', () => {
      const regionCodes = [110100, 320100, 440100];
      const result = convertRegionCodesToCascaderFormat(regionCodes);
      
      expect(result).toEqual([
        [110000, 110100],
        [320000, 320100],
        [440000, 440100]
      ]);
    });

    test('应该处理空数组', () => {
      expect(convertRegionCodesToCascaderFormat([])).toEqual([]);
    });

    test('应该处理无效输入', () => {
      expect(convertRegionCodesToCascaderFormat(null)).toEqual([]);
      expect(convertRegionCodesToCascaderFormat(undefined)).toEqual([]);
      expect(convertRegionCodesToCascaderFormat('not an array')).toEqual([]);
    });

    test('应该跳过无法处理的地区代码', () => {
      const regionCodes = [110100, 999999, 320100]; // 999999 是无效代码
      const result = convertRegionCodesToCascaderFormat(regionCodes);
      
      expect(result).toEqual([
        [110000, 110100],
        [320000, 320100]
      ]);
    });
  });
});
