import { WS_READY_STATE } from "yoc-im-web";
import { imLoadUser } from "@/api/common.js";
import { getImToken, getToken, setImToken } from "utils/storage.js";
import { EventBus } from "utils/imEventBus.js";
import { headerConfig } from "utils/constant.js";

let isOpen = false;
let shigeto = 0;
export const imLogin = async(fetchToken = false) => {
  try {
    isOpen = false;

    const WebIM = window.WebIM;
    let imToken = getImToken();

    if (!imToken || fetchToken === true) {
      const data = await imLoadUser();
      imToken = data.imToken;
      setImToken(data.imToken);
    }

    /** im 打开和连接时需要的参数 */
    const webImConnParams = {
      accessToken: imToken,
      requestHeaders: {
        clientToken: getToken(),
        ...headerConfig
      },
      wsParams: {
        clientToken: getToken(),
        ...headerConfig
      }
    };

    if (WebIM.conn.getWsReadystate() === WS_READY_STATE.OPEN) {
      WebIM.conn.login(webImConnParams);
    } else {
      WebIM.conn.open(webImConnParams);
    }
  } catch (error) {
    console.log(error);

    return error;
  }
};
/* 监听im 是否链接成功*/
EventBus.$on("onOpened", (message) => {
  isOpen = true;
  shigeto = 0;
});
export const imOnLoad = () => {
  return new Promise((resolve) => {
    if (isOpen) {
      return resolve();
    } else {
      EventBus.$once("onOpened", (message) => {
        isOpen = true;
        shigeto = 0;
        resolve(message);
      });
    }
  });
};

/* 认证失败重新登录*/
export const onAuthenticationFailed = (callback) => {
  EventBus.$on("onAuthenticationFailed", () => {
    if (getToken()) {
      isOpen = false;
      if (shigeto < 5) {
        console.log("认证失败重新登录");
        callback && callback();
      }
      shigeto++;
    }
  });
};
