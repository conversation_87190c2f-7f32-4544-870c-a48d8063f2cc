apiVersion: v1
kind: Service
metadata:
  name: law-sass
  labels:
    app: law-sass
spec:
  ports:                  #service需要暴露的端口列表
    - port: 29000
      name: http
  selector:
    app: law-sass
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: law-sass
spec:
  replicas: 1
  selector:
    matchLabels:
      app: law-sass
      version: v1
  template:
    metadata:
      labels:
        app: law-sass
        version: v1
    spec:                #定义容器模板，该模板可以包含多个容器
      imagePullSecrets:
        - name: imlaw-aliyunregistry-secret
      volumes:
        - name: volume-test-log
          persistentVolumeClaim:
            claimName: test-log
      #部署标签为appType=web的node节点上        
      nodeSelector:
        appType: web             
      containers:
        - name: law-sass
          image: registry-vpc.cn-hangzhou.aliyuncs.com/imlaw-test/law-sass:test-RELEASE
          imagePullPolicy: Always
          ports:
            - containerPort: 29000        #对service暴露端口
          volumeMounts:
            - mountPath: /data/log
              name: volume-test-log
          env:
            - name: FOR_GODS_SAKE_PLEASE_REDEPLOY
              value: 'REPLACED_DURING_BUILD'
