<template>
  <app-popup show-cancel @close="close" @cancel="close" title="权限配置" width="560px" @confirm="confirm" :visible="dialogVisible">
    <div class="text-[14px] pt-[16px] pb-[16px] pl-[24px] text-[#666666]">系统菜单权限</div>
    <div class="form">
      <el-tree
          :data="data"
          show-checkbox
          default-expand-all
          node-key="id"
          ref="tree"
          highlight-current
          :props="defaultProps" />
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "components/appPopup/index.vue";
import { lcInstitutionStaffPermissionConfig, sysModuleSelectAllList, sysModuleSelectByStaffId } from "@/api/system.js";

export default {
  name: "RootPopup",
  components: { AppPopup },
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    row: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      data: [],
      defaultProps: {
        children: "list",
        label: "moduleName"
      }
    };
  },
  mounted() {
    /* 查询用户菜单*/
    sysModuleSelectByStaffId({
      id: this.row.id
    }).then(data => {
      this.$refs.tree.setCheckedKeys(this.filterIds(data) || []);
    });
    /* 查询全部菜单*/
    sysModuleSelectAllList({ level: 2 }).then(data => {
      this.data = data || [];
    });
  },
  methods: {
    filterIds(data){
      const ids = [];
      data.forEach(item => {
        if(item.list && item.list.length){
          ids.push(...this.filterIds(item.list));
        }else{
          /* !只追加最后一级的id*/
          ids.push(item.id);
        }
      });
      return ids;
    },
    close(){
      this.$emit("update:dialogVisible", false);
    },
    confirm(){
      lcInstitutionStaffPermissionConfig({
        id: this.row.id,
        moduleIdList: [...this.$refs.tree.getHalfCheckedKeys(), ...this.$refs.tree.getCheckedKeys()]
      }).then(data => {
        this.$emit("update:dialogVisible", false);
      });
    }
  }
};
</script>

<style scoped lang="scss">
.form{
  margin: 0 24px;
  padding: 0 10px;
  background: #F5F5F7;
  border-radius: 5px;
  overflow: hidden;
  ::v-deep{
    .el-tree{
      background: #F5F5F7;
    }
  }
}
</style>
