<template>
  <div class="flex items-center">
    <el-tooltip v-for="(i,index) in list" :key="index" :disabled="basics.isNull(i.tip)" :content="i.tip"
                placement="bottom">
      <div
        v-if="filterCode.indexOf(i.code) < 0"
        class="flex items-center  box-border ml-[8px] bg-[#FCF1ED]  first-of-type:ml-[0] px-[2px] h-[20px] !text-[12px]  rounded-[4px] border-[1px] border-solid"
        :class="(tagData[i.code]&&tagData[i.code].className)||'border-[#F9E1D9] text-[#D36D64]'"
      >
        <i v-if="tagData[i.code]&&tagData[i.code].icon" class="iconfont leading-[1] !text-[12px]" :class="tagData[i.code]&&tagData[i.code].icon" />
        <p class="pl-[3px] leading-[1]">{{ i.label }}</p>
      </div>
    </el-tooltip>
    <slot />
  </div>
</template>

<script>
/* WEEKEND_REDUCE_PRICE:周末降价;
NIGHT_REDUCE_PRICE:夜间降价;
QUICK_MESSAGE:快捷留言;
TIMEOUT_REDUCE_PRICE:未消耗降价;*/
const tagData = {
  WEEKEND_REDUCE_PRICE: {
    icon: "icon-jiangjia",
    className: "border-[#EB4738] text-[#EB4738]"
  },
  NIGHT_REDUCE_PRICE: {
    icon: "icon-jiangjia",
    className: "border-[#EB4738] text-[#EB4738]"
  },
  QUICK_MESSAGE: {
    icon: "icon-xianxingtubiao-7",
    className: "cursor-pointer border-[#F9E1D9] text-[#D36D64]"
  }
};
export default {
  name: "CaseSourceTag",
  props: {
    list: {
      type: Array,
      default: () => []
    },
    filterCode: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      tagData: Object.freeze(tagData)
    };
  },
};
</script>

<style scoped lang="scss">

</style>
