<template>
	<div ref="dom">
		<slot />
	</div>
</template>

<script>
export default {
  name: "AppDetectedDom",
  data(){
    return {
      observer: null
    };
  },
  mounted() {
    this.observeDom();

  },
  destroyed() {
    this.observer?.disconnect();
  },
  methods: {
    /** 监听元素宽高变化 */
    observeDom() {
      const dom = this.$refs.dom;

      this.observer = new ResizeObserver((entries) => {
        for (let entry of entries) {
          const contentRect = entry.contentRect;

          this.$emit("change", contentRect);
        }
      });

      this.observer.observe(dom);
    },
  },
};
</script>
