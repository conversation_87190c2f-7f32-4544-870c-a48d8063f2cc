<template>
  <div>
    <div class="flex ">
      <!--
            3种状态
            1：激活状态 激活进度全等于 当前下标+1
            2：成功状态 激活进度大于 当前下标+1
            3：未激活状态
            -->
      <div class="flex-1 group" v-for="(i,index) in stepList" :class="{active:active===index+1,success:active>index+1}" :key="index">
        <div class="flex items-center justify-center relative">
          <div class="relative  z-10 text-[14px] text-[#999999] flex items-center justify-center  bg-[#F5F5F7] ">
            <p class="leading-[1] box-border step-warp rounded-[50%] border-[1px] border-solid border-[#CCCCCC] w-[24px] h-[24px] flex items-center justify-center">{{ index + 1}}</p>
            <i class="text-[24px] hidden iconfont step-warp-icon icon-yiwancheng text-[#3887F5]" />
          </div>
          <i class="step-line absolute w-full left-[50%] border-[1px] border-solid border-[#CCCCCC]" />
        </div>
        <div class="pt-[8px]">
          <p class="text-[14px] text-[#999999] step-text text-center">{{i.title}}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "AppSteps",
  props: {
    active: {
      type: Number,
      default: 1
    },
    stepList: {
      type: Array,
      default: () => {
        return [];
      }
    }
  },
};
</script>

<style scoped lang="scss">
.group{
  &:last-of-type{
    .step-line{
      display: none;
    }
  }
  &.active{
    .step-warp{
      background: #3887F5;
      color: white;
      border-color: #3887F5;
    }
    .step-text{
      color: #333333;
    }
  }
  &.success{
    .step-warp{
      display: none;
    }
    .step-warp-icon{
      display: block;
    }
    .step-line{
      border-color: #3887F5;
    }
  }
}
</style>
