<template>
  <div>
    <!--  独享  -->
    <app-popup
      :visible="show"
      @close="cancel"
      :show-close="false"
      :show-footer="false"
      show-cancel
      :width="'560px'"
      @cancel="cancel"
      @confirm="onSubmit('form')"
      title="后置独享支付"
    >
      <!--   线索描述   -->
      <div class="pay-text">
        <h3>线索描述：</h3>
        <p>{{detail.clueInfo}}</p>
        <p class="desc">说明：因上次抢单扣款账户为： {{radioVal === '1' ? '线索包支付' :'充值余额支付'}} ，本次后置独享也只能使用{{radioVal === '1' ? '线索包支付' :'充值余额支付'}}，支付后案源抢单记录将会变更为独享抢单记录</p>
      </div>

      <ul>
        <li v-if="radioVal === '1'"  class="flex flex-align-center flex-space-between">
          <div>
            <img class="icon-img" src="@/assets/images/icon1.png" />
            <div class="texts">
              <p class="fs14 text-[#333333]">线索包剩余次数</p>
              <p class="fs12 text-[#666666]">{{remainingGrabTimes.remainCaseCount}}条</p>
            </div>
          </div>
          <div>
            <label  class="text-[#333333]">抢单扣除权益：{{detail.postExclusiveDeductNum}}次</label>
            <el-radio  v-model="radioVal" label="1"  />
          </div>
        </li>
        <li v-if="radioVal === '3'" class="flex flex-align-center flex-space-between">
          <div>
            <img class="icon-img" src="@/assets/images/icon2.png" />
            <div class="texts">
              <p class="fs14 text-[#333333]">赠送余额</p>
              <p class="fs12 text-[#666666]"> {{priceYuan(remainingGrabTimes.giftRemainAmount)}} </p>
            </div>
          </div>
          <div>
            <label class="text-[#333333]"> 案源线索价格：{{priceYuan(detail.amount)}}</label>
            <el-radio v-model="radioVal"  label="3" />
          </div>
        </li>
        <li v-if="radioVal === '2'" class="flex flex-align-center flex-space-between">
          <div>
            <img class="icon-img" src="@/assets/images/icon3.png" />
            <div class="texts">
              <p class="fs14 text-[#333333]">充值余额</p>
              <p class="fs12 text-[#666666]">{{priceYuan(remainingGrabTimes.remainAmount)}}</p>
            </div>
          </div>
          <div>
            <label  class="text-[#333333]">案源线索价格：{{priceYuan(detail.postExclusiveDeductNum)}}</label>
            <el-radio v-model="radioVal"  label="2" />
          </div>
        </li>
      </ul>
    </app-popup>
  </div>
</template>

<script>
import AppPopup from "components/appPopup/index.vue";
import { priceYuan } from "../../../../utils/toolMethods";
import { caseRemainTimes, postYkjGrab } from "@/api/clues";

export default {
  name: "ExclusivePay",
  components: { AppPopup },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: () => {
        return {};
      }
    },

  },
  data(){
    return{
      deductNum: 1,
      remainingGrabTimes: {
        remainAmount: 0,
        giftRemainAmount: 0,
      }
    };
  },
  computed: {
    radioVal(){
      return  String(this.detail.consumeType);
    },
  },
  created() {
    this.getRemainingGrabTimes();
  },
  methods: { priceYuan,
    cancel(){
      this.$emit("update:show", false);
    },
    onSubmit(){
      // this.$emit("confirmPop", this.radioVal);
      postYkjGrab({ lcLawFirmsServerId: this.detail.serverId }).then(r => {
        this.$message.success("后置独享成功，快去跟进吧~");
        this.$store.dispatch("setIsTableRefresh");
        this.getRemainingGrabTimes();
        this.cancel();
      });


    },
    /** 查询可抢剩余次数 */
    getRemainingGrabTimes() {
      caseRemainTimes().then(res => {
        this.remainingGrabTimes = res;
      });
    },
  }
};
</script>

<style lang="scss" scoped>
ul{
  padding: 24px;
  .icon-img{
    width: 40px;
    height: 40px;
    vertical-align: top;
    margin-right: 8px;
  }
  .texts{
    display: inline-block;
    line-height: 22px;
  }
  label{
    margin-right: 8px;
  }
  /deep/ .el-radio__label{
    display: none;
  }
  li{
    margin-bottom: 24px;

    &:last-child{
      margin-bottom: 0;
    }
  }
}
.pay-text{
  padding: 24px;
  font-weight: 400;
  font-size: 14px;
  color: #666666;

  h3{
    font-weight: 500;
    font-size: 14px;
    color: #333333;
    margin-bottom: 4px;
  }
  .desc{
    background: #FCF1ED;
    border-radius: 4px 4px 4px 4px;
    font-weight: 400;
    font-size: 12px;
    color: #EB4738;
    padding: 10px;
    margin-top: 12px;
  }

}
</style>
