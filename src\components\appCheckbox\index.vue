<template>
  <el-checkbox @change="handleChange" :value="value" v-bind="$attrs" />
</template>

<script>
import { delayClickMixins } from "utils/delayClick";

export default {
  name: "AppCheckbox",
  mixins: [delayClickMixins()],
  props: {
    value: {
      type: [String, Number, Boolean],
      default: ""
    }
  },
  methods: {
    handleChange(value) {
      this.delayClickCallbackWrapper(() => {
        this.$emit("change", value);
        this.$emit("input", value);
      });
    }
  },

};
</script>

<style scoped lang="scss">

</style>
