<template>
  <div>
    <div class="px-[24px] pb-[8px] bg-white pt-[24px]">
      <xy-table-search @handleSearch="handleSearch" @handleSearchRest="handleSearch" :search-form="searchForm" />
    </div>
    <div class="bg-white px-[24px]">
      <app-card class="flex-1" title="抢线索趋势">
        <the-total-distribution-of-leads-echarts ref="echarts1" />
      </app-card>
    </div>
    <div class="px-[24px] bg-white">
      <div class="flex justify-between items-center pt-[24px] pb-[16px]">
        <div class="text-[16px] flex items-center font-normal text-[#333333]">
          抢线索数明细
          <el-tooltip content="该数据列表统计的是抢线索的数量" placement="bottom">
            <i class="ml-[8px] iconfont icon-xianxingtubiao-7 !text-[#666666] !text-[16px]" />
          </el-tooltip>
        </div>

        <xyExportButton
          file-name="抢线索数明细"
          :afferent-column="list"
          :export-option="exportOption"
          :search-query="searchQuery"
        />
      </div>
      <xy-page show-current-table="aBreakdownOfTheBenefitConsumption" ref="rightsTable" :query="searchQuery"
               class="!p-[0px]" index :column="list" :show-search="false" :request="request"  :is-load-table-request="false" />
    </div>
    <lead-package-display>
      <div class="px-[24px] bg-white">
        <div class="flex justify-between items-center pt-[24px] pb-[16px]">
          <div class="text-[16px] flex items-center font-normal text-[#333333]">
            线索包权益消耗明细
            <el-tooltip content="该数据列表统计的是案源使用线索包消耗的权益次数" placement="bottom">
              <i class="ml-[8px] iconfont icon-xianxingtubiao-7 !text-[#666666] !text-[16px]" />
            </el-tooltip>
          </div>

          <xyExportButton
            file-name="线索包权益消耗明细"
            :afferent-column="list2"
            :export-option="breakdownOfBenefitsExportOption"
            :search-query="searchQuery"
          />
        </div>
        <xy-page show-current-table="aBreakdownOfTheBenefitConsumption" ref="rightsTable" :query="searchQuery"
                 class="!p-[0px]" index :column="list2" :request="breakdownOfBenefitsRequest"  :is-load-table-request="false"
                 :show-search="false" />
      </div>
    </lead-package-display>
    <div class="px-[24px] bg-white">
      <div class="flex justify-between items-center pt-[24px] pb-[16px]">
        <div class="text-[16px] flex items-center font-normal text-[#333333]">
          法临币消耗明细
          <el-tooltip content="该数据列表统计的是案源使用法临币消耗的个数" placement="bottom">
            <i class="ml-[8px] iconfont icon-xianxingtubiao-7 !text-[#666666] !text-[16px]" />
          </el-tooltip>
        </div>

        <xyExportButton
          :afferent-column="list3"
          file-name="法临币消耗明细"
          :export-option="flbconsumptionDetailsRequest"
          :search-query="searchQuery"
        />
      </div>
      <xy-page show-current-table="aBreakdownOfTheBenefitConsumption" ref="rightsTable" :query="searchQuery"
               class="!p-[0px] !pb-[24px]" index :column="list3" :request="flbconsumptionDetailsRequest" :is-load-table-request="false"
               :show-search="false" />
    </div>
  </div>
</template>

<script>
import XyPage from "components/xy-page/index.vue";
import AppCard from "components/appCard/index.vue";
import TheTotalDistributionOfLeadsEcharts from "views/leadManagement/statistics/leadAnalysis/components/theTotalDistributionOfLeadsEcharts.vue";
import xyExportButton from "components/xy-buttons/exportButton.vue";
import {
  clueStatistics,
  exportClueStatistics,
  statisticsEquity,
  statisticsEquityExport, statisticsFalinCoinConsumeExport,
  statisticsFalinCoinConsumePage
} from "@/api/clues.js";
import XyTableSearch from "components/xy-tableSearch/index.vue";
import {
  aDetailedListOfTheConsumptionOfLeadPackageRights,
  aDetailedListOfTheConsumptionOfLegalCurrency,
  aDetailedListOfTheNumberOfLeadsGrabbed,
  searchList
} from "views/leadManagement/statistics/leadAnalysis/index";
import LeadPackageDisplay from "components/leadPackageDisplay/index.vue";

export default {
  name: "LeadAnalysis",
  components: {LeadPackageDisplay, XyTableSearch, xyExportButton, TheTotalDistributionOfLeadsEcharts, AppCard, XyPage },
  data() {
    return {
      request: {
        getListUrl: clueStatistics
      },
      exportOption: {
        url: exportClueStatistics
      },
      /* 消耗权益明细*/
      breakdownOfBenefitsRequest: {
        getListUrl: statisticsEquity
      },
      breakdownOfBenefitsExportOption: {
        url: statisticsEquityExport
      },
      /* 法临币消耗明细请求*/
      flbconsumptionDetailsRequest: {
        getListUrl: statisticsFalinCoinConsumePage,
        url: statisticsFalinCoinConsumeExport
      },
      searchQuery: {},
      searchForm: searchList,
      /* 线索包权益消耗明细*/
      list: aDetailedListOfTheNumberOfLeadsGrabbed,
      /* 线索包权益消耗明细*/
      list2: aDetailedListOfTheConsumptionOfLeadPackageRights,
      /* 法临币消耗明细*/
      list3: aDetailedListOfTheConsumptionOfLegalCurrency
    };
  },
  created() {
    /* 请求下搜索里面的异步数据*/
    this.searchForm.forEach(item => {
      if(item.syncData && item.syncData.url){
        item.syncData.url().then(res => {
          this.$set(item.syncData, "data", res);
        });
      }
    });
  },
  methods: {
    handleSearch(data) {
      console.log(data, "data");
      this.searchQuery = data;
      this.$nextTick(() => {
        this.$store.dispatch("setIsTableRefresh", "aBreakdownOfTheBenefitConsumption");
      });
      /* 搜索重新请求echarts数据*/
      const echartsKey = Object.keys(this.$refs);
      echartsKey.forEach(item => {
        const echarts = this.$refs[item];
        if (echarts.initEcharts) {
          echarts.initEcharts(data);
        }
      });
    }
  }
};
</script>

<style scoped lang="scss">

</style>
