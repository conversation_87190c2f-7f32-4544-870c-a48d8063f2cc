<template>
  <div id="chinaMap" class="chinaMap" :style="{height:height,width:width}" />
</template>

<script>
import echarts from "echarts";
import resize from "@/minixs/resize";
import china from "echarts/map/json/china";
// 引入中国地图数据
echarts.registerMap("china", china);
const geoCoordMap = {
  "台湾": [121.5135, 25.0308],
  "黑龙江": [127.9688, 45.368],
  "内蒙古": [110.3467, 41.4899],
  "吉林": [125.8154, 44.2584],
  "北京": [116.4551, 40.2539],
  "辽宁": [123.1238, 42.1216],
  "河北": [114.4995, 38.1006],
  "天津": [117.4219, 39.4189],
  "山西": [112.3352, 37.9413],
  "陕西": [109.1162, 34.2004],
  "甘肃": [103.5901, 36.3043],
  "宁夏": [106.3586, 38.1775],
  "青海": [101.4038, 36.8207],
  "新疆": [87.9236, 43.5883],
  "西藏": [91.11, 29.97],
  "四川": [103.9526, 30.7617],
  "重庆": [108.384366, 30.439702],
  "山东": [117.1582, 36.8701],
  "河南": [113.4668, 34.6234],
  "江苏": [118.8062, 31.9208],
  "安徽": [117.29, 32.0581],
  "湖北": [114.3896, 30.6628],
  "浙江": [119.5313, 29.8773],
  "福建": [119.4543, 25.9222],
  "江西": [116.0046, 28.6633],
  "湖南": [113.0823, 28.2568],
  "贵州": [106.6992, 26.7682],
  "云南": [102.9199, 25.4663],
  "广东": [113.12244, 23.009505],
  "广西": [108.479, 23.1152],
  "海南": [110.3893, 19.8516],
  "上海": [121.4648, 31.2891],
  "澳门": [113.579709, 22.169692],
  "香港": [114.242011, 22.272474]
};
export default {
  name: "ChinaMap",
  mixins: [resize],
  props: {
    width: {
      type: String,
      default: "100%"
    },
    height: {
      type: String,
      default: "500px"
    },
    areaData: {
      type: Array,
      default: () => []
    },
    allUserNum: {
      type: Number
    }
  },
  data() {
    return {
      chart: null
    };
  },
  watch: {
    areaData: {
      handler(val) {
        console.log(val,98989)
        this.initChart();
      },
      deep: true
    }
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById("chinaMap"));
      // const _this = this
      const option = {
        tooltip: {
          trigger: "item",
          formatter: function(params) {
            if (params.value) {
              return params.name +  "：跟进客户 " + params.value + " 个";
            } else {
              return params.name +  "：跟进客户 " + 0 + " 个";
            }
          }
        },

        geo: {
          map: "china",
          show: true,
          // roam: true,
          zoom: 1.2,
          label: {
            normal: {
              show: false
            },
            emphasis: {
              show: false
            }
          },
          itemStyle: {
            normal: {
              areaColor: "#ffffff",
              borderColor: "#DDDDDD", // 线
              shadowColor: "#DDDDDD", // 外发光
              shadowBlur: 2
            },
            emphasis: {
              areaColor: "#0a2dae"// 悬浮区背景
            }
          },
          tooltip: {
            trigger: "item",
            formatter: function(params) {
              // 自定义格式化提示内容
              return params.name + " ：跟进客户 " + params.data.value[2] + " 个";
            }
          }
        },
        series: [{
          name: "中国",
          type: "map",
          mapType: "china",
          // selectedMode: 'single',
          // roam: true,
          zoom: 1.2,
          label: {
            normal: {
              show: true, // 是否显示对应地名
              textStyle: {
                color: "rgba(0,0,0,0.5)"
              }
            },
            emphasis: {
              show: true,
              textStyle: {
                color: "#ffffff"
              }
            }
          },
          itemStyle: {
            normal: {
              areaColor: "#FFFFFF",
              borderColor: "#DDDDDD", // 线
              shadowColor: "#DDDDDD", // 外发光
              shadowBlur: 2,
              color: "#ffffff"
            },
            emphasis: {
              areaColor: "#62A4FF", // 悬浮区背景
              color: "#ffffff"
            }
          },
          data: this.areaData
        }, {
          type: "scatter",
          coordinateSystem: "geo",
          symbol: "pin",
          symbolSize: [50, 50],
          label: {
            normal: {
              show: true,
              textStyle: {
                color: "#fff",
                fontSize: 12
              },
              formatter(value) {
                return value.data.value[2];
              }
            }
          },
          itemStyle: {
            normal: {
              color: '#3887F5' // 标志颜色
            }
          },
          data: this.convertData(this.areaData),
          showEffectOn: "render",
          rippleEffect: {
            brushType: "stroke"
          },
          hoverAnimation: true,
          zlevel: 4
        }]
      };
      this.chart.setOption(option);
    },
    convertData(data) {
      var res = [];
      for (var i = 0; i < data.length; i++) {
        var geoCoord = geoCoordMap[data[i].name];
        if (geoCoord) {
          res.push({
            name: data[i].name,
            value: geoCoord.concat(data[i].value)
          });
        }
      }
      return res;
    }
  }
};
</script>

<style scoped>

</style>
