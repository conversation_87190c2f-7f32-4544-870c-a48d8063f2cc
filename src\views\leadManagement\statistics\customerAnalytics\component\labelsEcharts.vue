<template>
<!--  跟进状态分布-->
  <div>
    <app-echarts  ref="echarts" class="echarts" />
  </div>
</template>

<script>
import AppEcharts from "components/appEcharts/index.vue";

export default {
  name: "LabelsEcharts",
  components: { AppEcharts },
  props: {
    infoData: {
      type: Array,
      default: () => {
        return [];
      }
    },
    radius: {
      type: Array,
      default: () => {
        return ["45%", "55%"];
      }
    },
    textKey: {
      type: String,
      default: "text"
    },
    valueKey: {
      type: String,
      default: "count"
    }
  },
  data() {
    return {
      info: {}
    };
  },
  computed: {
    dataLabels(){
      const { labels, colors, total } = this.mapInfoData;
      return [labels, colors, total];
    },
    dataMap(){
      return this.mapInfoData.data
    },
    mapInfoData(){
      const data = [];
      let labels = [], colors = [], total = 0;
      this.infoData.forEach(item => {
        data.push({
          ...item,
          name: item[this.textKey],
          ...(this.isNull(item[this.valueKey]) ? {} : { value: item[this.valueKey] })
        });
        labels.push(item[this.textKey]);
        colors.push(item.color);
        total = total + Number(item[this.valueKey] || 0);
      });
      return {
        data,
        labels,
        colors,
        total
      };
    },
    dataList(){
      return  this.isNotNull() ? (this.dataMap || []) : (this.dataMap || []).map(item => ({ ...item, value: 0 }));
    }
  },
  watch: {
    infoData: {
      handler() {
        this.initChart();
      },
      deep: true
    }
  },
  mounted() {
    this.initChart();
  },
  methods: {
    isNotNull(){
      const total = this.dataLabels[2];
      return !this.basics.isNull(total) && total > 0;
    },
    isNull(o) {
      return this.basics.isNull(o)||o==='0'
    },
    initChart(){
      this.$nextTick(() => {
        this.$refs.echarts.initEcharts(this.echartsOptions);
      });
    },
    echartsOptions(){
      return  {
        title: {
          text: this.isNotNull() ? "总数（条）" : "暂无数据", // 主标题文本
          subtext: this.dataLabels[2] || "0", // 副标题文本
          left: "center",
          top: "32%",
          textStyle: {
            fontSize: 11,
            color: "#666666",
            align: "center",
            fontWeight: "400"
          },
          subtextStyle: {
            fontSize: 19,
            color: "#333333",
            align: "center",
            fontWeight: "600"
          }
        },
        tooltip: {
          trigger: "item",
          align: "left",
          formatter: "{a} <br/>{b}: {c} ({d}%)"
        },
        legend: {
          data: this.dataLabels[0],
          bottom: 10,
          icon: "circle",
          itemWidth: 6,  // 设置宽度
          itemHeight: 6, // 设置高度,
          textStyle: {
            fontSize: 14,
            color: "#999999"
          }
        },
        color: this.dataLabels[1],
        series: [
          {
            name: "跟进状态分布",
            type: "pie",
            center: ["50%", "40%"],
            radius: this.radius,
            // radius: ['50%', '40%'],
            avoidLabelOverlap: true,
            labelLine: {
              lineStyle: {
                color: "#999999",
              }
            },
            label: {
              show: this.isNotNull(),
              formatter: ({ percent, value }) => {
                return [
                  `\n{c|${value}\n}`,
                  `\n{d|${percent}%}\n`
                ].join("\n");
              },
              // 增加标签间隔的角度
              distance: 20,
              lineHeight: 7,
              rich: {
                c: {
                  fontSize: 14,
                  color: "#000000",
                  align: "center"
                },
                d: {
                  fontSize: 14,
                  color: "#000000",
                  align: "center"
                }
              }
            },
            itemStyle: {
              borderWidth: 2, // 设置border的宽度有多大
              borderColor: "#fff",
            },
            hoverAnimation: false,
            emphasis: {
              itemStyle: {
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)"
              }
            },
            data: this.dataList
          }
        ]
      };
    }
  }
};
</script>

<style scoped lang="scss">

.echarts{
  width: 100%;
  height: 258px;
}
</style>
