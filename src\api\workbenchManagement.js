
/* 查询系统使用人员数和可添加上限数*/
import { request } from "utils/axios.js";

/* 律客云工作台数据概览-今日数据概览*/
export const stagingOverview = (params) => request.post("/law-cloud/lc/statistics/staging/overview", params);

/* 律客云工作台数据概览-抢单排名*/
export const stagingRankingForGrabbingOrders = (params) => request.post("/law-cloud/lc/statistics/staging/rankingForGrabbingOrders", params);

/* 律客云工作台数据概览-律所动态*/
export const stagingDynamics = (params) => request.post("/law-cloud/lc/statistics/staging/dynamics", params);

/* 更新公告分页查询*/
export const lcNoticePage = (params) => request.post("/law-cloud/lcNotice/page", params);

/* 更新公告详情*/
export const lcNoticeDetailById = (params) => request.post("/law-cloud/lcNotice/detailById", params);

/* 喜报分页查询*/
export const lcGoodNewsPage = (params) => request.post("/law-cloud/lcGoodNews/page", params);

/* 根据id查询喜报详情*/
export const lcGoodNewsDetailById = (params) => request.post("/law-cloud/lcGoodNews/detailById", params);

/* 律师事务所机构信息-机构详情-充值使用信息*/
export const lcLawFirmsInstitutionAllAccountInfo = (params) => request.post("/law-cloud/lcLawFirmsInstitution/allAccountInfo", params);

/* 工作台-个人线索统计*/
export const lcLawFirmsInstitutionGetCaseData = (params) => request.post("/law-cloud/lcLawFirmsInstitution/getCaseData", params);

/* 工作台-个人线索统计*/
export const privateGrabClueStatistics = (params) => request.post("/law-cloud/lcLawFirmsInstitution/lc/privateGrabClueStatistics", params);
