<template>
	<div>
		<div
			class="bg-[#F9F9F9] py-[16px] px-[24px] box-border border-0 border-b-[1px] border-dashed border-[#DDDDDD] flex items-center justify-between"
		>
			<div>
				<div class="flex items-center text-[16px] text-[#666666]">
					<i class="iconfont icon-Casesource pr-[10px] !text-[18px]" />
					抢案源时间段：<span class="text-[#333333]">{{ timeRangeDisplay }}</span>
				</div>
				<div class="flex items-center text-[16px] text-[#666666] pt-[12px]">
					<i class="iconfont icon-limit pr-[10px] !text-[18px]" />
					抢案源上限：<span class="text-[#333333]">{{ upperLimitDisplay }}</span>
				</div>
			</div>
			<div class="flex items-center">
				<app-switch v-model="switchStatus" />
			</div>
		</div>
		<div class="p-[24px]">
			<div class="flex pt-[20px] first-of-type:pt-[0px]">
				<div class="text-[14px] text-[#666666] text-right flex-shrink-0 min-w-[88px]">
					当事人所在地
				</div>
				<div class="text-[14px] pl-[24px] text-[#333333] h-[40px] overflow-hidden">
					{{ regionDisplay }}
				</div>
			</div>
			<div class="flex pt-[20px] first-of-type:pt-[0px]">
				<div class="text-[14px] text-[#666666] text-right flex-shrink-0 min-w-[88px]">
					匹配关键词
				</div>
				<div class="text-[14px] pl-[24px] text-[#333333] h-[40px] overflow-hidden">
					{{ keywordDisplay }}
				</div>
			</div>
			<div class="flex pt-[20px] first-of-type:pt-[0px]">
				<div class="text-[14px] text-[#666666] text-right flex-shrink-0 min-w-[88px]">
					案件类型偏好
				</div>
				<div class="text-[14px] pl-[24px] text-[#333333] h-[40px] overflow-hidden">
					{{ questTypeDisplay }}
				</div>
			</div>
		</div>
		<div class="p-[16px]">
			<div
				class="px-[13px] py-[16px] rounded-[6px] flex items-center justify-between"
				:style="{ backgroundColor: statusInfo.bgColor }"
			>
				<div class="flex items-center" :style="{ color: statusInfo.color }">
					<i class="iconfont" :class="statusInfo.icon" />
					<div class="font-[500] pl-[4px] text-[14px]">
						{{ statusInfo.text }}
					</div>
				</div>
				<div class="flex items-center">
					<app-button style-type="text" type="danger" @click="handleDelete">删除计划</app-button>
					<app-button style-type="text" type="success" @click="handleEdit">修改</app-button>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
import AppSwitch from "components/appSwitch/index.vue";
import AppButton from "components/appButton/index.vue";
import { autoGrabPlanDelete, autoGrabPlanUpdateStatus } from "@/api/clues";

export default {
  name: "PlanCards",
  components: { AppSwitch, AppButton },
  props: {
    planData: {
      type: Object,
      default: () => ({}),
    },
    getIsSetDefaultPayment: {
      type: Function,
      default: () => Promise.resolve(),
    },
  },
  data() {
    return {
      // 内部状态管理
    };
  },
  computed: {
    /* 开关状态 */
    switchStatus: {
      get() {
        const { status } = this.planData;
        
        return status === 1;
      },
      set() {
        this.handleSwitch();
      },
    } /* 时间范围显示 */,
    timeRangeDisplay() {
      const { beginTime, endTime } = this.planData;
      if (beginTime && endTime) {
        return `${beginTime}～${endTime}`;
      }
      return "";
    },
    /* 抢案源上限显示 */
    upperLimitDisplay() {
      const { caseUpperLimit } = this.planData;
      return `${caseUpperLimit || ""}条`;
    },
    /* 当事人所在地显示 */
    regionDisplay() {
      const { regionName } = this.planData;
      return regionName;
    },
    /* 匹配关键词显示 */
    keywordDisplay() {
      const { keyWord } = this.planData;
      if (Array.isArray(keyWord) && keyWord.length > 0) {
        return keyWord.join("、");
      }
      return keyWord;
    },
    /* 案件类型显示 */
    questTypeDisplay() {
      const { questTypeName } = this.planData;
      return questTypeName;
    },
    /* 抓取状态显示 */
    statusInfo() {
      const { grabStatus } = this.planData;
      const statusMap = {
        1: {
          text: "",
          color: "#F78C3E",
          bgColor: "#F5F5F7",
        },
        2: {
          text: "律客云助手抢案源完成",
          color: "#22BF7E",
          bgColor: "rgba(34,191,126,0.08)",
          icon: "icon-yiwancheng",
        },
        3: {
          text: "律客云助手自动抢案源中",
          color: "#3887F5",
          bgColor: "rgba(56,135,245,0.08)",
          icon: "el-icon-loading",
        },
      };
      return statusMap[grabStatus] || statusMap[3];
    },
  },
  methods: {
    async handleSwitch() {
      try {
        await this.getIsSetDefaultPayment();
        await autoGrabPlanUpdateStatus({
          planId: this.planData.planId,
          status: this.planData.status === 1 ? 0 : 1,
        });
        
        this.$emit("refresh-list");
      } catch (e) {
        return;
      }
    },
    /* 删除计划 */
    handleDelete() {
      autoGrabPlanDelete({
        planId: this.planData.planId,
      }).then(() => {
        this.$emit("refresh-list");
      });
    },
    /* 修改计划 */
    handleEdit() {
      this.$emit("edit-plan", this.planData);
    },
  },
};
</script>

<style scoped lang="scss"></style>
