<template>
  <app-button icon="icon-xiazai" v-root-btn="buttonPermissionsEnum.EXPORT" :size="size" @click="handleExport"> <slot>导出数据</slot> </app-button>
</template>

<script>
// import qs from 'qs'
// import { createRequestSign } from 'utils/toolMethods'
// import { Base64 } from 'js-base64'
// import { getToken } from 'utils/storage'
import { Message } from "element-ui";
import AppButton from "components/appButton/index.vue";
import buttonPermissionsEnum from "@/enum/buttonPermissionsEnum";
export default {
  name: "ExportButton",
  components: { AppButton },
  props: {
    exportOption: {
      type: Object,
      default: () => {
        return {
          url: "",
          data: {}
        };
      }
    },
    searchData: {
      type: Object,
      default: () => {
        return {};
      },
      desc: "传入表格的数据，做筛选"
    },
    searchQuery: {
      type: Object,
      default: () => {
        return {};
      }
    },
    afferentColumn: {
      type: Array,
      default: () => {
        return [];
      },
      desc: "传入的列表字段后端需求"
    },
    size: {
      type: String,
      default: "large"
    },
    fileName: {
      type: String,
      default: ""
    }

  },
  data() {
    return {
      unwantedField: ["beginDate", "endDate"] // 当这些字段存在，就不需要分页
    };
  },
  computed: {
    buttonPermissionsEnum() {
      return buttonPermissionsEnum
    },
  // 菜单名族
    breadcrumb() {
      const title = this.$store.getters.breadcrumb || [];
      return this.fileName || title[0] || this.$route.meta.title;
    },
    // 处理传入的列表字段
    handleAfferentColumn() {
      /**
       * 1、先过滤掉搜索的内容
       */

      const arr1 = this.afferentColumn.filter(res => (!res.tableHidden || !res.formHidden));

      return arr1;
    }
  },
  methods: {
    handleExport() {
      // 当不需要条件，直接下载所有
      const isAllDownLoad = this.exportOption.data ? this.exportOption.data.isAllDownLoad : false;
      const query = Object.assign({}, this.searchData, this.exportOption.data, isAllDownLoad ? { pageSize: 100000 } : this.searchQuery);

      if (!this.basics.isObjNull(query)) {
        for (const key in query) {
          // 删除没有值的字段
          if (this.basics.isNull(query[key]) || key === "undefined") delete query[key];

          // 当有日期的时候，导出不做分页
          if (this.unwantedField.includes(key)) {
            if (query["currentPage"]) delete query["currentPage"];
            query["pageSize"] = 100000;
          }
        }
      }

      // 当选择下载所有，删除标识字段
      if (query && query.isAllDownLoad) delete query.isAllDownLoad;
      // console.log(query,  '导出的参数')

      this.exportOption.url(Object.assign({}, { elements: this.handleAfferentColumn, fileName: this.breadcrumb }, query)).then(res => {
        if (!res) return;
        const url = res.total ? res.filePath : res;
        // 下载5000条数限制
        if (res.total && res.total > 5000) {
          Message({
            message: "超出最大导出限制，请重新选择筛选条件",
            type: "error",
            duration: 2000
          });
          return;
        }
        window.location.href = url;
      });
      // window.open(this.$global.baseApi + this.exportOption.url + '?' + qs.stringify(newData), '_blank')
      // window.location.href = this.$global.baseApi + this.exportOption.url + '?' + qs.stringify(newData)
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep.el-button{
  border: none;
  background: #EDF3F7;
  color: #3887F5;
}
</style>
