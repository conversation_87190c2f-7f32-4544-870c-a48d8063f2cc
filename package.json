{"name": "fl-admin", "version": "2.1.6", "private": true, "scripts": {"tailwindcss": "tailwindcss -i ./src/assets/tailwind.css -o ./src/assets/tailwindout.css --watch", "dev": "vue-cli-service serve --version=1.0.7", "dev:concurrently": "concurrently \"npx tailwindcss -i ./src/assets/tailwind.css -o ./src/assets/tailwindout.css --watch\" \"vue-cli-service serve\"", "build:dev": "vue-cli-service build --mode development  --version=1.0.7", "build:test": "vue-cli-service build --mode test --version=1.0.7", "build:prod": "vue-cli-service build  --mode production --version=1.0.7", "build:pre": "vue-cli-service build --mode pre", "build": "vue-cli-service build", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint", "analyzer": "vue-cli-service serve --mode analyzer"}, "dependencies": {"axios": "^0.21.0", "babel-eslint": "^10.1.0", "core-js": "^3.6.5", "crypto-js": "^4.2.0", "dayjs": "^1.10.7", "echarts": "^4.9.0", "element-ui": "^2.15.13", "file-saver": "^2.0.5", "fuse.js": "^6.4.6", "html2canvas": "^1.2.2", "js-base64": "^3.6.0", "js-md5": "^0.7.3", "moment": "^2.29.1", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.1", "qrcodejs2": "0.0.2", "qs": "^6.9.4", "string-replace-all-ponyfill": "^1.0.1", "throttle-debounce": "^5.0.0", "v-viewer": "^1.6.4", "vue": "^2.6.11", "vue-baidu-map": "^0.21.22", "vue-clipboard2": "^0.3.1", "vue-photo-preview": "^1.1.3", "vue-router": "^3.2.0", "vue-virtual-scroller": "^1.1.2", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "wangeditor": "^4.7.1", "xlsx": "^0.18.5", "yoc-im-web": "^0.1.114"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-unit-mocha": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^5.1.2", "@vue/test-utils": "^1.0.3", "babel-eslint": "^10.1.0", "babel-plugin-transform-remove-console": "^6.9.4", "babel-plugin-transform-runtime": "^6.23.0", "chai": "^4.1.2", "concurrently": "^8.2.2", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "mockjs": "^1.1.0", "node-sass": "^4.12.0", "sass-loader": "^8.0.2", "tailwindcss": "^3.3.5", "tailwindcss-line-clamp": "^1.0.5", "vue-template-compiler": "^2.6.11", "webpack": "^4.46.0", "webpack-bundle-analyzer": "^4.2.0"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "@vue/standard"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"prefer-const": 0}, "overrides": [{"files": ["**/__tests__/*.{j,t}s?(x)", "**/tests/unit/**/*.spec.{j,t}s?(x)"], "env": {"mocha": true}}]}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "volta": {"node": "14.20.0"}}