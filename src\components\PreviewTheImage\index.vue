<template>
  <div v-show="false">
    <el-image ref="elImage" :preview-src-list="srcList" />
  </div>
</template>

<script>
export default {
  name: "PreviewTheImage",
  props: {
    /* 监听得DOM*/
    container: {
      type: String,
      default: ""
    }
  },
  data() {
    return {
      srcList: []
    };
  },
  mounted() {
    console.log("mounted");
    this.addListener();
  },
  beforeDestroy() {
    console.log("beforeDestroy");
    this.removeListener();
  },
  methods: {
    previewTheImage(e){
      console.log(e);
      const target = e.target;
      /* 判断是不是图片点击*/
      if(target.tagName === "IMG"){
        const src = target.src;
        this.srcList = [src];
        this.$nextTick(() => {
          this.$refs.elImage.clickHandler();
        });
      }
    },
    /* 添加监听*/
    addListener(){
      this.$nextTick(() => {
        /* 这里防止多测监听*/
        this.removeListener();
        document.querySelector(this.container) && document.querySelector(this.container).addEventListener("click", this.previewTheImage);
      });
    },
    /* 移除监听*/
    removeListener(){
      document.querySelector(this.container) && document.querySelector(this.container).removeEventListener("click", this.previewTheImage);
    }
  }
};
</script>

<style scoped lang="scss">

</style>
