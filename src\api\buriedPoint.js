import { request } from "utils/axios";
import basics from "utils/basicsMethods";


export const BURY_POINT_TYPE = {
  // 事件类型[1:页面;2:前端事件;3:后端业务埋点]
  PAGE: 1,
  FRONTEVENT: 2,
  BACKEND: 3
};

export const buriedPoint = (data) => {
  let { extra } = data;
  if (basics.isNull(extra)) extra = {};
  const params = {
    type: 1,
    ...data,
    extra: {
      ...extra
    }
  };
  return request.post("/law-cloud/lc/point", params);
};
