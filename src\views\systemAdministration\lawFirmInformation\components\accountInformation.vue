<template>
  <div>
    <div class="flex items-center justify-between">
      <slot />
    </div>
    <div class="flex items-center justify-between pb-[32px]">
      <app-select :default-value="tabValue" :select-list="tabs" @change="tabChange" />
      <export-button file-name="账户信息" :afferent-column="column" :export-option="requestUrl" />
    </div>
    <div class="panel">
      <div class="overflow-hidden w-[25%] box-border pl-[16px] first-of-type:pl-[0px]"  v-for="(item, index) in panelList" :key="index">
        <div class="panel-item flex flex-col justify-center">
          <div class="title">{{ item.label }}</div>
          <div class="num" v-if="item.type&&item.type==='twoDecimalPlaces'">{{priceNumber(info[item.prop])}}</div>
          <div class="num" v-else>{{ info[item.prop]||'-'}}</div>
        </div>
      </div>
    </div>
    <xy-page :request="requestUrl" show-current-table="AccountInformation" style="padding: 0" :column="column" :show-search="false" />
  </div>
</template>

<script>
import XyPage from "components/xy-page/index.vue";
import {
  clueAccountList,
  cluePanelList,
  coinAccountList,
  coinPanelList
} from "views/systemAdministration/lawFirmInformation/index.js";
import {
  lcLawFirmsInstitutionAccountInfo,
  lcLawFirmsInstitutionFlbAccountInfo,
  lcLawFirmsInstitutionFlbAccountInfoPage,
  lcLawFirmsInstitutionFlbAccountInfoPageExport,
  lcLawFirmsInstitutionOrderExport,
  lcLawFirmsInstitutionOrderPage
} from "@/api/system.js";
import ExportButton from "components/xy-buttons/exportButton.vue";
import AppSelect from "components/AppSelect/index.vue";
import { isLeadPackageDisplay } from "components/leadPackageDisplay";
import { priceNumber } from "@/utils/toolMethods";

export default {
  name: "AccountInformation",
  components: { AppSelect, ExportButton, XyPage },
  data() {
    return {
      tabValue: 1,
      panelList: [],
      column: [],
      info: {},
      requestUrl: {
        /* 列表*/
        getListUrl: "",
        /* 导出*/
        url: "",
        /* 详细信息*/
        infoUrl: ""
      }
    };
  },
  computed: {
    tabs() {
      return [...isLeadPackageDisplay([{
        label: "线索包账户",
        value: 1,
        /* 面板数据*/
        panelList: cluePanelList,
        list: clueAccountList,
        infoUrl: lcLawFirmsInstitutionAccountInfo,
        getListUrl: lcLawFirmsInstitutionOrderPage,
        url: lcLawFirmsInstitutionOrderExport
      }], []), {
        label: "法临币账户",
        value: 2,
        /* 面板数据*/
        panelList: coinPanelList,
        list: coinAccountList,
        infoUrl: lcLawFirmsInstitutionFlbAccountInfo,
        getListUrl: lcLawFirmsInstitutionFlbAccountInfoPage,
        url: lcLawFirmsInstitutionFlbAccountInfoPageExport
      }];
    }
  },
  created() {
    this.tabChange(this.tabs[0]);
  },
  methods: {
    priceNumber,
    getInfo(){
      /* 账户信息*/
      this.info = {};
      this.requestUrl.infoUrl().then(data => {
        console.log(data, "1111");
        this.info = data || {};
      });
    },
    tabChange(data){
      this.panelList = data.panelList;
      this.column = data.list;
      this.tabValue = data.value;
      Object.keys(this.requestUrl).forEach(key => {
        this.requestUrl[key] = data[key];
      });
      this.getInfo();
      this.$store.dispatch("setIsTableRefresh", "AccountInformation");
    }
  }
};
</script>

<style scoped lang="scss">
.panel {
  display: flex;
  padding-bottom: 16px;
  overflow: hidden;

  .panel-item {
    padding-left: 16px;
    height: 84px;
    border-radius: 4px 4px 4px 4px;
    border: 1px solid #EEEEEE;

    .title {
      font-size: 12px;
      padding-bottom: 4px;
      font-weight: 400;
      color: #666666;
    }

    .num {
      font-size: 24px;
      font-weight: 500;
      color: #333333;
    }
  }
}
</style>
