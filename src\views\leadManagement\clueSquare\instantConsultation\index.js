import global from "@/utils/constant";
import datePickerOptions from "utils/datePickerOptions.js";

const searchForm = [
  {
    prop: "smsTemplateTitle",
    label: "发布地区",
    tableHidden: true,
    search: true,
    multiple: true,
    type: global.formItemType.regioncascader,
    checkStrictly: false
  },
  {
    prop: "a1",
    propKey: ["startTime", "endTime"],
    label: "发布时间",
    search: true,
    tableHidden: true,
    type: global.formItemType.datetimerange,
    valueFormat: "yyyy/MM/dd HH:mm:ss",
    pickerDate: datePickerOptions(90),
  },
  {
    prop: "info",
    label: "搜索",
    tableHidden: true,
    search: true,
    type: global.formItemType.input,
    placeholder: "关键字模糊匹配",
    maxlength: 5,
  },
];

export const tableColumn = [
  ...searchForm,
  {
    prop: "qaMessageId",
    label: "咨询ID",
    width: "120px",
  },
  {
    prop: "createTime",
    label: "发布时间",
    width: "192px",
  },

  {
    prop: "provinceName/regionName",
    label: "发布地区",
    width: "174px",
    render: (h, { row }) => {
      return <span>{row.provinceName}{row.regionName}</span>;
    },
  },
  {
    prop: "info",
    label: "咨询描述",
    minWidth: "334px",
  },

  {
    prop: "serverAmount",
    label: "线索价格",
    type: "money",
    width: "90px",
  },
  {
    prop: "userPhone",
    label: "发布人手机号",
    width: "133px",
  },
  {
    prop: "typeLabel",
    label: "咨询类型",
    width: "90px",
  },
];
