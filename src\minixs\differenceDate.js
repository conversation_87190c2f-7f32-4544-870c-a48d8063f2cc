// import moment from 'moment'
export default {

  computed: {
    // 统计部分设置的时间只能选择30天
    statisticsPickerOptionsThirty() {
      const _this = this;
      return {
        disabledDate(time) {
          const times = 86400000 * 30; // 2个月的毫秒数
          const curSelectTime = new Date(_this.minDate).getTime(); //
          const before = curSelectTime - times;// 前2个月毫秒数
          const after = curSelectTime + times;// 后2个月毫秒数

          return time.getTime() > after || time.getTime() < before || time.getTime() > Date.now();
        },
        // 选中开始时间和最后的时间
        onPick({ maxDate, minDate }) {
          if (!maxDate) {
            _this.minDate = minDate;
          }
        }
      };
    },
    statisticsPickerOptionsNinety() {
      const _this = this;
      return {
        disabledDate(time) {
          const times = 86400000 * 60; // 2个月的毫秒数
          const curSelectTime = new Date(_this.minDate).getTime(); //
          const before = curSelectTime - times;// 前2个月毫秒数
          const after = curSelectTime + times;// 后2个月毫秒数

          return time.getTime() > after || time.getTime() < before || time.getTime() > Date.now();
        },
        // 选中开始时间和最后的时间
        onPick({ maxDate, minDate }) {
          if (!maxDate) {
            _this.minDate = minDate;
          }
        }
      };
    }
  }

};
