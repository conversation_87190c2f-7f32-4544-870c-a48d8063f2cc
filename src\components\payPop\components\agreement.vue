<template>
  <div>
    <app-popup
      @confirm="handleRootPopupClick"
      @close="handleRootPopupClick"
      @cancel="handleRootPopupClick"
      title="律客云充值协议"
      :show-cancel="false"
      width="800px"
      :visible.sync="showVisible"
      confirm-text="确定"
    >
      <div class="box">
        <iframe  ref="iframe" width="100%" height="100%" :src="url" />
      </div>
    </app-popup>
  </div>
</template>

<script>
import AppPopup from "components/appPopup/index.vue";
import { agreementLinks } from "utils/config";

export default {
  name: "Agreement",
  components: { AppPopup },
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    showVisible: {
      get(){
        return this.show;
      },
      set(val){
        this.$emit("update:show", val);
      }
    },
    url(){
      return agreementLinks.lawyerCloudRechargeProtocol;
    }
  },
  methods: {
    handleRootPopupClick(){
      this.$emit("update:show", false);
    },
  }
};
</script>

<style lang="scss" scoped>
.box{
  padding: 24px;
  height: 400px;

  ::v-deep iframe{
    border: 1px solid #EEEEEE;
  }
}
</style>
