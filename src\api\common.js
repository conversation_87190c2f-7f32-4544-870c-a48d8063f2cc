import { request } from "utils/axios";
import { storeRequest } from "utils/storeRequest";

/* --------------通用功能------------------ */

const baseURL = process.env.VUE_APP_BASE_URL;

export const uplodImages = baseURL + "/cms/upload/image"; // 图片上传
export const uplodWord = baseURL + "/cms/upload/words"; // 图片上传
export const uplodPdf = baseURL + "/cms/upload/pdf"; // 图片上传
export const uplodFile = baseURL + "/cms/upload/file"; // 文件上传
export const uploadAppletExcel = baseURL + "/cms/applet/uploadExcel/upload"; // 小程序文件上传
export const uploadAppletExcelCover = baseURL + "/cms/applet/uploadExcel/excelUploadCover"; // 小程序文件上传覆盖
export const uploadCase = baseURL + "/cms/caseSource/addImport";
export const uploadCaseFree = baseURL + "/cms/caseSourceFree/addImport"; // 公共免费案源导入
export const uploadLawFirm = baseURL + "/cms/lawFirms/addImport"; // 律所案源导入
export const uploadLawFirmLawyers = baseURL + "/cms/lawFirms/lawyerBatchImport/"; // 律所律所导入
// export const uplodTxt = (params) => request.post('/upload/txt', params) // 文本上传
// export const uplodExcel = (params) => request.post('/upload/excel', params) // excel上传
// export const uplodVideo = (params) => request.post('/upload/video', params) // 视频上传
// export const uplodApk = (params) => request.post('/upload/apk', params) // 视频上传

export const uploadH5 = baseURL + "/cms/statistics2CH5Channel/uploadCashSpend";

/* --------------地区------------------ */
export const getAreaList = (params) => request.post("/core/area/getArea", params); // 地区
export const getAreaByName = (params) => request.post("/area/getAreaByCityName", params); // 根据市名查找省市
// export const cityList = (params) => requestCore.post('/area/city/' + params) // 城市
// export const countyList = (params) => requestCore.post('/area/county/' + params) // 区
export const queryServiceProductList = (client) => request.post(`/noticeTemplate/serviceProduct/${client}`); // 获取各个应用服务产品的选项
export const queryTargetList = (client) => request.post(`/noticeTemplate/target/${client}`); // 各个应用的触达目的选项

export const downLoadCaseCity = (params) => request.post("/grade/float/importDataTemplate", params); // 案源等级城市浮动价格导入模板下载
export const uploadCaseCityImportData = baseURL + "/cms/grade/float/importData"; // 案源等级城市浮动价格导入

export const uploadCaseFractionCityImportData = baseURL + "/cms/caseSourceScoreConfigDetail/city/importData"; // 案源分数城市的导入

/* 获取IM*/
export const imLoadUser = (data) => request.post("/core/im/loadUser", data);

/* 派单状态更新 */
export const updatePdStatus = (params) => request.post("/law-cloud/lawyerSelfEmployed/updatePdStatus", params);

/* 获取派单状态 */
export const getPdStatus = (params) => request.post("/law-cloud/lawyerSelfEmployed/getPdStatus", params);

/* 用户协议*/
export const userAgreement = (data) => request.post("/core/user/agreement", data);

/* 获取服务信息*/
export const getServerInfo = (caseSourceServerV2Id) => request.post(`/paralegal/caseSourceServerV2/getServerInfo/${caseSourceServerV2Id}`);

/* 获取律师关键指标查询 */
export const KeyIndicators = (data) => request.post("/law-cloud/lawPlatformMain/keyIndicators", data);

/* 获取律师关键指标查询 */
export const serverNumSurvey = (data) => request.post("/law-cloud/lawPlatformMain/serverNumSurvey", data);

/* 个人资料 */
export const userDetail = (data) => request.post("/paralegal/user/detail", data);

/* 更新个人资料 */
export const updateAllLawyerInfo = (data) => request.post("/paralegal/update/allLawyerInfo", data);

/* 字典库查询 */
export const queryDataDetailList = (params) => request.post("/core/dataDetail/list", params);

/* 开始服务 */
export const serverStartLock = (caseSourceServerV2Id) => request.post("/paralegal/caseSourceServerV2/startLock/" + caseSourceServerV2Id);

/* 字典库查询 */
export const dataDetailList = (params) => request.post("/law-cloud/dataDetail/list", params);

/* 字典缓存查询*/
export const dataDetailCache = (data) => storeRequest({
  api: dataDetailList,
  data,
  cacheName: "dataDetailList",
  storeMaxTime: 0
});

/** 省市区县列表查询接口 */
export const areaGetArea = (params) => request.post("/law-cloud/area/getArea", params);

/** 根据跟进id查询手机号 */
export const getPhoneByFollowId = (params) => request.post("/law-cloud/lc/getPhoneByFollowId", params);

/** 获取系统参数值 */
export const commonConfigKey = (params) => request.post("/law-cloud/common/config/key", params);

/* 律所下面查询城市*/
export const getAreaByInstitutionId = (params) => request.post("/law-cloud/area/getAreaByInstitutionId", params);

/* 跟进标签专用字典查询*/
export const dataDetailLabelList = (params) => {
  return request.post("/law-cloud/dataDetail/labelList", params).then((data) => {
    if(params.groupCode === "LC_LAW_CLUE_LABLE"){
      /* 平坦化数组*/
      let flatData = [];
      data.forEach(item => {
        if(item.list){
          flatData = [...flatData, ...item.list];
        }
      });
      return {
        data,
        flatData,
      };
    }
    return data;
  });
};

/* 用户未读消息数*/
export const lcInstitutionStaffUnread = (params) => request.post("/law-cloud/lc/lcInstitutionStaff/unread", params);

/* 系统通知列表*/
export const lcSysNotificationPage = (params) => request.post("/law-cloud/lcSysNotification/page", params);

/* 读取系统通知*/
export const lcSysNotificationRead = (params) => request.post("/law-cloud/lcSysNotification/read", params);

/* 线索广场-新增案源数量*/
export const lcNewCaseSourceClueCount = (params) => request.post("/law-cloud/lc/newCaseSourceClueCount", params);

/* 即时咨询-新增问答数量*/
export const lcNewQaMessageCount = (params) => request.post("/law-cloud/lc/newQaMessageCount", params);

/* 没有权限 平台省市区县列表查询接口*/
export const areaGetPlatformArea = (params) => request.post("/law-cloud/area/getPlatformArea", params);

/** 上传实名认证 */
export const uploadCertInfo = (params) => request.post("/law-cloud/lcInstitutionStaff/uploadCertInfo", params);

/** 通过员工id获取实名认证 */
export const getCertInfoByStaffId = (staffId) => request.post(`/law-cloud/lcInstitutionStaff/getCertInfoByStaffId/${staffId}`);

/** 查询律客云广告配置 */
export const lawCloudAdvertList = (params) => request.post("/law-cloud/lc/advertList", params);
