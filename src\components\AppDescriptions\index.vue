<script>
export default {
  name: "AppDescriptions",
  props: {
    infoProps: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Object,
      default: () => ({})
    }
  },
  render(createElement, context) {
    console.log(this.$attrs, "this.$props");

    return (
      <div>
        <el-descriptions column={this.$attrs.column} >
          {this.infoProps.map(item => {
            return <el-descriptions-item labelClassName="text-[14px] text-[#666666]" contentClassName="text-[14px] text-[#333333]" label={item.label}>{item.render ? item.render(this.data, createElement) : this.data[item.value]}</el-descriptions-item>;
          })}
        </el-descriptions>
      </div>
    );
  },
};
</script>
