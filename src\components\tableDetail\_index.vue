<template>
	<div class="tableDetail">
		<ul class="table-detail-wrap">
			<li
				v-for="(item, key) in tableList"
				:key="key"
				:class="item.className"
				v-show="!item.formHidden"
			>
				<div class="table-name">{{ item.label }}</div>
				<!-- 内容 -->
				<div class="table-text" v-if="item.tableSlot">
					<slot :name="item.tableSlot" />
				</div>
				<div class="table-text" v-else>
					<div v-if="!item.render">
						<span v-if="item.filter">{{ tableDetail[item.prop] | parseStatus(item.filter) }}</span>
						<span v-else-if="item.filterUrl">{{
							tableDetail[item.prop]
								| parseStatus(item.syncData.data, item.syncData.label, item.syncData.value)
						}}</span>
						<span
							v-else-if="item.filterTag"
							:class="`${tableDetail[item.prop] ? 'success' : 'warning'}`"
							>{{ tableDetail[item.prop] | parseStatus(item.filterTag) }}</span
						>
						<span v-else-if="item.dateFormat">{{
							tableDetail[item.prop] | parseTimeTwo(item.dateFormat)
						}}</span>
						<!-- 针对按预案的分使用不同key处理 -->
						<span v-else-if="item.actProp && tableDetail[item.actProp]">{{
							basics.isNull(tableDetail[item.actProp]) ? '-' : tableDetail[item.actProp]
						}}</span>
						<span v-else>{{
							basics.isNull(tableDetail[item.prop]) ? '-' : tableDetail[item.prop]
						}}</span>
					</div>
					<div v-else>
						<template :render="item.render" :results="{ row: tableDetail, column: tableDetail }" />
					</div>
				</div>
			</li>
		</ul>
	</div>
</template>

<script>
// import { parseTimeTwo, parseStatus } from '@/filters/index'

export default {
  name: "TableDetail",
  components: {},
  props: {
    tableList: {
      type: Array,
      default: () => [],
    },
    getDetail: {
      type: Function,
      default: () => Promise.resolve(),
    },
    tableDetail: {
      type: Object,
      default: () => {},
    },
    query: {
      type: Object,
      default: () => {},
    },
  },
  data() {
    return {
      infos: {},
    };
  },
  computed: {
    searchForm() {
      let temp = [];
      if (!this.basics.isArrNull(this.tableList)) {
        temp = this.tableList.filter(item => !item.formHidden);
      }
      return temp;
    },
  },
  created() {
    this.handleAsyncSearch();
  },
  methods: {
    /** 获取详情信息 */
    getDetailList() {
      if (!this.basics.isObjNull(this.tableDetail)) return;
      this.getDetail(this.query).then(res => {
        this.tableDetail = res;
      });
    },
    // 处理筛选中异步数据
    handleAsyncSearch() {
      if (!this.basics.isArrNull(this.searchForm)) {
        this.searchForm.forEach(item => {
          if (item.syncData && item.syncData.url && this.basics.isFunction(item.syncData.url)) {
            const request = item.syncData.url;
            // const params = item.syncData.params || {}
            const params = this.basics.isNull(item.syncData.params) ? {} : item.syncData.params;
            request(params).then(res => {
              // console.log(res)
              this.$set(item.syncData, "data", res);
              // item.syncData.data = res
            });
          }
        });
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.table-detail-wrap {
	display: flex;
	width: 100%;
	border: 1px solid #eee;
	flex-wrap: wrap;
	// justify-content: center;
	align-content: center;
	border-bottom: 0;
	margin-top: 20px;
	margin-bottom: 20px;
	li {
		width: 50%;
		border-bottom: 1px solid #eee;
		display: flex;

		&.table-tr {
			width: 100% !important;
			.table-name {
				width: 150px !important;
				background: #f5f5f5;
			}

			> div {
				width: calc(100% - 150px);
				word-break: break-all;
			}
		}

		> div {
			display: table;
			padding: 10px 10px;
			box-sizing: border-box;

			&.table-name {
				width: 150px;
				background: #f5f5f5;
			}

			&.table-text {
				width: calc(100% - 150px);
			}
		}
	}
}
</style>
