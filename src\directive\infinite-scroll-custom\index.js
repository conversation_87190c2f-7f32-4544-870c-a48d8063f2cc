import Vue from "vue";
import { getScrollContainer } from "element-ui/src/utils/dom.js";
import { throttle } from "throttle-debounce";

const getOptions = (el, key) => {
  const attr = el.getAttribute(key);
  const { vm } = el[scope];
  return vm[attr];
};

const handleScroll = (el) => {
  return () => {
    const { container, cb } = el[scope];
    if (container.scrollTop <= 0) {
      const disabled = getOptions(el, "infinite-scroll-disabled");
      if (cb && !disabled) {
        cb();
      }
    }
  };
};
const scope = "infinite-scroll-custom";
Vue.directive("infinite-scroll-custom", {
  inserted(el, binding, vnode) {
    const cb = binding.value;
    const vm = vnode.context;
    const container = getScrollContainer(el, true);
    const onScroll = throttle(200, handleScroll(el));
    el[scope] = { container, onScroll, cb, vm };
    /* 监听container滚动 滚动到顶部100px 触发回调*/
    container.addEventListener("scroll", onScroll);
  },
  unbind(el) {
    const { container, onScroll } = el[scope];
    container.removeEventListener("scroll", onScroll);
  }
});
