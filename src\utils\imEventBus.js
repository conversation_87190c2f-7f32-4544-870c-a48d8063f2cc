import ImWebSdk, { HTTP_ENVIRONMENT } from "yoc-im-web";
import Vue from "vue";
import wsEventBus from "utils/wsEventBus.js";

export const EventBus = wsEventBus;
Vue.prototype.$createWebIM = function() {
  const WebIM = new ImWebSdk({
    httpEnvironment: process.env.VUE_APP_EVN_CONFIG !== "prod" ? HTTP_ENVIRONMENT.TEST : HTTP_ENVIRONMENT.PRO,
    https: true
  });
  Vue.prototype.$WebIM = WebIM;
  WebIM.listen({
    // 连接成功回调
    // eslint-disable-next-line no-unused-vars
    onOpened: (message) => {
      console.log("连接成功回调");
      EventBus.$emit("onOpened");
    },
    // 连接关闭回调
    onClosed: function(message) {
      console.log("连接关闭回调");
      EventBus.$emit("onClosed", message);
    },
    // 收到文本消息
    onTextMessage: (message) => {
      console.log("收到文本消息");
      console.log(message);
      EventBus.$emit("onTextMessage", message);
    },
    // 收到图片消息
    onPictureMessage: function(message) {
      console.log("收到图片消息");
      EventBus.$emit("onPictureMessage", message);
    },
    // 收到文件消息
    onFileMessage: function(message) {
      console.log("收到文件消息", message);
      EventBus.$emit("onFileMessage", message);
    },
    // 收到自定义消息
    onCustomMessage: function(message) {
      console.log("收到自定义消息", message);
      EventBus.$emit("onCustomMessage", message);
    },
    // 收到自定义消息
    onSystemMessage: function(message) {
      console.log("收到系统消息");
      EventBus.$emit("onSystemMessage", message);
    },
    //
    onReceivedMessage: function(message) {
      EventBus.$emit("onReceivedMessage", message);
    },
    // 收到所有消息
    onMessage: function(message) {
      console.log("收到所有消息");
      EventBus.$emit("onMessage", message);
    },
    // 收到消息送达客户端回执
    // eslint-disable-next-line no-unused-vars
    onDeliveredMessage: function(message) {
    },
    onOtherCommandMessage: function(message) {
      console.log("收到指令消息", message);
      EventBus.$emit("onOtherCommandMessage", message);
    },
    onAuthenticationFailed: function(message) {
      console.log("收到认证失败指令", message);
      EventBus.$emit("onAuthenticationFailed", message);
    }
  });
  window.WebIM = {
    conn: WebIM
  };
  return WebIM;
};

export default Vue.prototype.$createWebIM;
/* export default function({ store }) {
  EventBus.$on('onOpened', () => {
    // 设置为打开状态
    store.commit('im/SET_OPEN', true)
  })
  EventBus.$on('onClosed', () => {
    // 设置为打开状态
    store.commit('im/SET_OPEN', false)
  })
}*/
