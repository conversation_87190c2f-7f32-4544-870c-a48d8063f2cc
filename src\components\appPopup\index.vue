<template>
  <el-dialog
    append-to-body
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    custom-class="app-popup_class"
    :visible.sync="getVisible"
    :show-close="false"
    v-bind="$attrs"
    :top="top"
  >
    <template #title>
      <div />
    </template>
    <div v-if="showTitle" class="app-popup_body d-flex flex-align-center flex-space-between">
      <p class="title">{{ title }}</p>
      <i @click="close" class="iconfont icon-xianxingtubiao-13 cursor-pointer" />
    </div>
    <slot />
    <div
      v-if="!hideFooter"
      class="app-popup_footer d-flex flex-space-between"
      :class="{ 'app-popup_footer_border': footerBorder }"
    >
      <div>
        <slot name="footerBtn" />
      </div>
      <div>
        <app-button :delay-click="delayClick" :delay="delay" v-if="showCancel" class="app-btn" size="large" type="info" @click="cancel"
        >{{ cancelText }}
        </app-button>
        <app-button :delay-click="delayClick" :delay="delay" v-if="showConfirm" class="app-btn" size="large" @click="confirm">{{ confirmText }}</app-button>
      </div>
    </div>
    <template #footer>
      <div />
    </template>
  </el-dialog>
</template>

<script>
import AppButton from "components/appButton/index.vue";

export default {
  name: "AppPopup",
  components: { AppButton },
  inheritAttrs: false,
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "",
    },
    /** 是否显示title */
    showTitle: {
      type: Boolean,
      default: true,
    },
    showCancel: {
      type: Boolean,
      default: false,
    },
    /* 是否显示确定按钮 */
    showConfirm: {
      type: Boolean,
      default: true,
    },
    hideFooter: {
      type: Boolean,
      default: false,
    },
    confirmText: {
      type: String,
      default: "确认",
    },
    cancelText: {
      type: String,
      default: "取消",
    },
    footerBorder: {
      type: Boolean,
      default: false,
    },
    top: {
      type: String,
      default: "13vh"
    },
    /* 是否需要延迟*/
    delayClick: {
      type: Boolean,
      default: false
    },
    delay: {
      type: Number
    }
  },
  data() {
    return {};
  },
  computed: {
    getVisible: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
  methods: {
    confirm() {
      // this.$emit('update:visible', false)
      this.$emit("confirm");
    },
    cancel() {
      this.getVisible = false;
      this.$emit("cancel");
    },
    close() {
      this.$emit("update:visible", false);
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss">
.app-popup_class {
  border-radius: 8px 8px 8px 8px;

  .el-dialog__header,
  .el-dialog__footer,
  .el-dialog__body {
    min-height: auto !important;
    padding: 0 !important;
    border-bottom: 0 !important;
  }

  .app-popup_body {
    height: 56px;
    padding: 0 24px;
    border-bottom: 1px solid #eeeeee;

    p {
      font-size: 16px;
      font-weight: 500;
      color: #333333;
    }

    i {
      font-size: 24px;
    }
  }

  .app-popup_footer {
    padding: 8px 24px 24px;
    &_border {
      border-top: 1px solid #eeeeee;
    }

    .app-btn + .app-btn {
      margin-left: 16px;
    }
  }
}
</style>
