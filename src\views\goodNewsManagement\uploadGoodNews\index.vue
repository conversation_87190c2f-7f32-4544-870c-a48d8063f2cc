<template>
  <div class="flex h-[calc(100vh_-_126px)] bg-[#FFFFFF] px-[24px]">
    <div class="flex-1 overflow-y-auto">
      <left-forms @handleSubmit="handleSubmit" :disabled="isDisabled" :show-city="showCity" :form.sync="data" />
    </div>
    <div class="right overflow-y-auto  pl-[24px]">
      <p class="flex font-bold text-[16px] text-[#333333] items-center !pb-[16px] !pt-[24px]"><i
        class="w-[4px] h-[16px] bg-[#3887F5] mr-[8px]" />示例 <span class="font-normal text-[#666666] text-[14px]">（审核通过后即可下载专属成单喜报）</span></p>
      <good-news-preview :data="data" ref="imgDom" />
    </div>
  </div>
</template>

<script>
import LeftForms from "views/goodNewsManagement/uploadGoodNews/components/leftForms/index.vue";
import GoodNewsPreview from "views/goodNewsManagement/uploadGoodNews/components/goodNewsPreview/index.vue";
import { lcGoodNewsV2DetailById, lcGoodNewsV2ReUpload, lcGoodNewsV2Upload } from "@/api/goodNewsManagement";
import { divMethod, mulMethod } from "utils/toolMethods";
import { turnToMyNews } from "utils/turnPage";

export default {
  name: "Index",
  components: { GoodNewsPreview, LeftForms },
  data() {
    return {
      data: {},
      /* !城市回显问题 没得办法 只有加个状态控制显示*/
      showCity: true
    };
  },
  computed: {
    /* 链接上可能会带 喜报id*/
    goodNewsId() {
      return this.$route.query.id;
    },
    /* 判断是不是详情*/
    isDetail() {
      return !this.basics.isNull(this.goodNewsId);
    },
    /* 是不是禁用*/
    isDisabled() {
      /* —checkStatus	否	Integer	审核状态 0未审核 1 审核通过 2审核失败 9律所撤销审核*/
      return this.isDetail && !this.basics.isNull(this.data.checkStatus) && (this.data.checkStatus === 1);
    }
  },
  activated() {
    this.getInstitutionDetail();
  },
  methods: {

    /* 获取机构信息*/
    async getInstitutionDetail(){
      const info = this.$store.getters.userInfo || {};
      const detail = await this.getDetail();
      this.data = {
        institutionLogo: info.logo,
        institutionName: info.institutionName,
        staffId: info.id,
        institutionProvinceName: info.provinceName,
        institutionRegionName: info.regionName,
        staffName: info.userName,
        ...detail
      };
      /* 是需要详情的时候 重新渲染一下city*/
      if (this.isDetail) {
        this.showCity = false;
        this.$nextTick(() => {
          this.showCity = true;
        });
      }
    },
    /* 获取详情*/
    async getDetail(){
      let detail = {};
      if(this.isDetail){
        try {
          detail = await lcGoodNewsV2DetailById({ id: this.goodNewsId });
          detail = {
            ...detail,
            cityCodes: [detail.provinceCode, detail.regionCode],
            amount: this.basics.isNull(detail.amount) ? "" : divMethod(detail.amount, 100),
            caseEntrustAmount: this.basics.isNull(detail.caseEntrustAmount) ? "" : divMethod(detail.caseEntrustAmount, 100)
          };
        }catch (e) {
          console.log(e);
        }
      }
      return detail;
    },
    /* 提交表单*/
    handleSubmit(data) {
      (this.isDetail ? lcGoodNewsV2ReUpload : lcGoodNewsV2Upload)({
        ...data,
        amount: mulMethod(data.amount, 100),
        caseEntrustAmount: mulMethod(data.caseEntrustAmount, 100)
      }).then(() => {
        this.$message.success("上传成功");
        turnToMyNews();
      });
    }

  }
};
</script>

<style scoped lang="scss">
.right{
  width: 445px;
}
</style>
