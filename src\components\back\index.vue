<template>
<!-- v-el-drag='true' -->
<div>
  <div :class="'back-wrap  drag-dom fixed-style'" title="可拖动">
     <el-backtop target=".detail-wrap" :bottom="80" />
        <div class="drag-dom-con">
          <el-button type="warning" @click="goBack">返回</el-button>
          <!-- <el-page-header @back="goBack" :content='title'></el-page-header> -->
        </div>
  </div>
</div>
</template>

<script>
import elDrag from "@/directive/el-drag";
export default {
  name: "Back",
  directives: { elDrag },
  props: {
    title: {
      type: String,
      default: "详情"
    },
    isFixed: {
      type: Boolean,
      default: true
    }

  },
  methods: {
    /* 关闭详情页 */
    goBack() {
      this.$emit("colseFun");
    }
  }
};
</script>

<style lang="scss" scoped>
  .back-wrap{
      margin-bottom: 15px;

  }
  .fixed-style{
    margin-bottom: 15px;
    position: fixed;
    // background: #ffffff;
    z-index: 20;
    // border-bottom: 1px solid #f5f5f5;
    right: 82px;
    top: 98px;
    // padding: 10px;
    box-sizing: border-box;
    // color: $warning-color;
    // width: 175px;
    // box-shadow: 0 0 6px #0000001f;
    border-radius: 10px;
    button{
      height: 28px;
      line-height: 11px;
    }
  }
  .back-wrap /deep/ .el-page-header .el-page-header__content{
    // color: $text-color-main !important;

  }
</style>
