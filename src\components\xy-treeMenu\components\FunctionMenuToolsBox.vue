<template>
  <div class="w-[64px] mx-auto mb-[8px]">
    <el-dropdown trigger="click" @visible-change="handleVisibleChange" placement="right-start" @command="handleCommand">
      <div
        class="w-[64px] h-[64px] relative bg-[#031633] rounded-[4px] flex flex-col items-center justify-center cursor-pointer text-[#FFFFFF]"
        :class="{ 'bg-[#3887f5]': isAnyToolActive }">
        <span v-if="badge" class="absolute z-[5] ml-[6px] w-[64px] h-[20px] flex items-center justify-center rounded-[50%] top-[-8px] left-[50%]  font-[500] text-[12px] text-[#FFFFFF] bg-[#F54A3A] rounded-tl-[10px] rounded-br-[10px] rounded-tr-[10px] rounded-bl-[2px]">
          常用工具
        </span>
        <i class="el-icon-suitcase text-[20px] mb-[4px]" />
        <span>工具箱</span>
      </div>
      <el-dropdown-menu slot="dropdown">
        <el-dropdown-item v-for="item in toolsConfig" :key="item.command" :command="item.command">
          <div class="h-[40px] text-[13px] text-[#333333] flex items-center">
            <span :class="{ 'text-[#3887f5]': activeTools[item.command] }">{{ item.label }}</span>
          </div>
        </el-dropdown-item>
      </el-dropdown-menu>
    </el-dropdown>
    <function-menu-service-q-r v-model="serviceQR" />
  </div>
</template>

<script>
import { turnToGoodNews, turnToInvoice, turnToInvoiceRecord } from "utils/turnPage";
import goodNewsManagement from "router/modules/goodNewsManagement";
import FunctionMenuServiceQR from "components/xy-treeMenu/components/FunctionMenuServiceQR.vue";
import { getTriggerTime, getTriggerTimeByKey, setTriggerTime } from "utils/storage";

export default {
  name: "FunctionMenuToolsBox",
  components: {
    FunctionMenuServiceQR,
  },
  data() {
    return {
      badge: true,
      serviceQR: false
    };
  },
  computed: {
    /** 检查当前是否在喜报广场相关路由 */
    isInGoodNews() {
      const currentRouteName = this.$route.name;
      return this.isInGoodNewsManagement(currentRouteName);
    },

    /** 各工具的激活状态 */
    activeTools() {
      const currentRouteName = this.$route.name;
      const result = {};

      this.toolsConfig.forEach(tool => {
        result[tool.command] = currentRouteName && currentRouteName === tool.command;
      });

      return result;
    },
    /** 是否有任何工具处于激活状态 */
    isAnyToolActive() {
      return Object.values(this.activeTools).some(active => active);
    },
    /* 判断是不是超级管理员*/
    isAdministrator() {
      return this.$store.getters.isAdministrator;
    },
    toolsConfig(){
      return [
        { command: "invoice", show: this.isAdministrator, label: "开发票", click: turnToInvoice },
        { command: "invoiceRecord", show: this.isAdministrator, label: "开票记录", click: turnToInvoiceRecord },
        { command: "laborCalculator", label: "劳动仲裁计算器", click: () => {this.openTools(1);} },
        { command: "lawyerFeeCalculator", label: "律师费计算器", click: () => {this.openTools(0);} },
        { command: "goodNewsSquare", label: "喜报广场", click: turnToGoodNews },
        {
          command: "serviceQR", label: "客服", click: () => {
            this.serviceQR = true;
          }
        },
      ].filter(item => {
        return this.basics.isNull(item.show) || item.show;
      });
    }
  },
  mounted() {
    this.badge = this.basics.isNull(getTriggerTimeByKey("FunctionMenuToolsBox"));
  },
  methods: {
    /* 打开工具*/
    openTools(activeTabBarIndex) {
      this.$store.commit("SET_CALCULATOR", {
        isShow: true,
        showIndex: activeTabBarIndex
      });
    },

    /** 处理下拉菜单命令 */
    handleCommand(command) {
      const selectedTool = this.toolsConfig.find(tool => tool.command === command);

      if (selectedTool) {
        selectedTool?.click();
      }
    },
    handleVisibleChange(state){
      if(state){
        setTriggerTime("FunctionMenuToolsBox", 2);
        this.badge = false;
      }
    },
    /** 检查当前路由是否在喜报广场管理中 */
    isInGoodNewsManagement(routeName) {
      function findRoute(routes) {
        if (!Array.isArray(routes)) return false;

        for (const route of routes) {
          if (route.name === routeName) {
            return true;
          }

          if (route.children) {
            const found = findRoute(route.children);
            if (found) return true;
          }
        }

        return false;
      }
      return findRoute(goodNewsManagement);
    },
  }
};
</script>
