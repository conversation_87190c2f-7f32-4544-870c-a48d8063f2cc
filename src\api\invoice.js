import { request } from "utils/axios";

/** 可开票记录分页查询 */
export const institutionInvoicePage = params =>
  request.post("/law-cloud/institutionInvoice/page", params);

/** 全选可开票 */
export const institutionInvoiceSelectAll = params =>
  request.post("/law-cloud/institutionInvoice/selectAll", params);

/** 开票记录-分页查询 */
export const institutionInvoiceInvoiceRecordPage = params =>
  request.post("/law-cloud/institutionInvoice/invoiceRecordPage", params);

/** 开票 */
export const institutionInvoiceInvoice = params =>
  request.post("/law-cloud/institutionInvoice/invoice", params);

/** 发票详情-根据发票id查询 */
export const institutionInvoiceDetailByInvoiceId = params =>
  request.post("/law-cloud/institutionInvoice/detailByInvoiceId", params);

/** 发票详情-根据发票id查询明细分页 */
export const institutionInvoiceDetailByInvoiceIdPage = params =>
  request.post("/law-cloud/institutionInvoice/detailByInvoiceIdPage", params);
