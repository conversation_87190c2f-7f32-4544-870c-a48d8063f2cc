.container {
  width: 100%
}

@media (min-width: 640px) {
  .container {
    max-width: 640px
  }
}

@media (min-width: 768px) {
  .container {
    max-width: 768px
  }
}

@media (min-width: 1024px) {
  .container {
    max-width: 1024px
  }
}

@media (min-width: 1280px) {
  .container {
    max-width: 1280px
  }
}

@media (min-width: 1536px) {
  .container {
    max-width: 1536px
  }
}

.visible {
  visibility: visible
}

.\!invisible {
  visibility: hidden !important
}

.collapse {
  visibility: collapse
}

.fixed {
  position: fixed
}

.absolute {
  position: absolute
}

.relative {
  position: relative
}

.sticky {
  position: sticky
}

.-right-\[5px\] {
  right: -5px
}

.-top-\[56px\] {
  top: -56px
}

.-top-\[5px\] {
  top: -5px
}

.bottom-0 {
  bottom: 0px
}

.bottom-\[150px\] {
  bottom: 150px
}

.bottom-\[26px\] {
  bottom: 26px
}

.bottom-\[27px\] {
  bottom: 27px
}

.left-0 {
  left: 0px
}

.left-\[50\%\] {
  left: 50%
}

.left-\[52px\] {
  left: 52px
}

.left-\[58px\] {
  left: 58px
}

.right-0 {
  right: 0px
}

.right-\[-100\%\] {
  right: -100%
}

.right-\[12px\] {
  right: 12px
}

.right-\[16px\] {
  right: 16px
}

.right-\[24px\] {
  right: 24px
}

.right-\[44px\] {
  right: 44px
}

.right-\[4px\] {
  right: 4px
}

.top-0 {
  top: 0px
}

.top-\[-8px\] {
  top: -8px
}

.top-\[16px\] {
  top: 16px
}

.top-\[192px\] {
  top: 192px
}

.top-\[40px\] {
  top: 40px
}

.top-\[4px\] {
  top: 4px
}

.z-0 {
  z-index: 0
}

.z-10 {
  z-index: 10
}

.z-50 {
  z-index: 50
}

.z-\[1\] {
  z-index: 1
}

.z-\[20\] {
  z-index: 20
}

.z-\[5\] {
  z-index: 5
}

.z-\[9990\] {
  z-index: 9990
}

.z-\[9999\] {
  z-index: 9999
}

.m-20 {
  margin: 5rem
}

.m-36 {
  margin: 9rem
}

.m-48 {
  margin: 12rem
}

.m-8 {
  margin: 2rem
}

.m-auto {
  margin: auto
}

.mx-\[14px\] {
  margin-left: 14px;
  margin-right: 14px
}

.mx-\[24px\] {
  margin-left: 24px;
  margin-right: 24px
}

.mx-auto {
  margin-left: auto;
  margin-right: auto
}

.my-\[12px\] {
  margin-top: 12px;
  margin-bottom: 12px
}

.my-\[8px\] {
  margin-top: 8px;
  margin-bottom: 8px
}

.\!mb-0 {
  margin-bottom: 0px !important
}

.\!mr-0 {
  margin-right: 0px !important
}

.\!mr-\[4px\] {
  margin-right: 4px !important
}

.\!mt-\[4px\] {
  margin-top: 4px !important
}

.\!mt-\[7px\] {
  margin-top: 7px !important
}

.-mt-\[7px\] {
  margin-top: -7px
}

.mb-16 {
  margin-bottom: 4rem
}

.mb-\[-28px\] {
  margin-bottom: -28px
}

.mb-\[16px\] {
  margin-bottom: 16px
}

.mb-\[20px\] {
  margin-bottom: 20px
}

.mb-\[24px\] {
  margin-bottom: 24px
}

.mb-\[4px\] {
  margin-bottom: 4px
}

.mb-\[8px\] {
  margin-bottom: 8px
}

.ml-\[12px\] {
  margin-left: 12px
}

.ml-\[16px\] {
  margin-left: 16px
}

.ml-\[24px\] {
  margin-left: 24px
}

.ml-\[40px\] {
  margin-left: 40px
}

.ml-\[4px\] {
  margin-left: 4px
}

.ml-\[6px\] {
  margin-left: 6px
}

.ml-\[8px\] {
  margin-left: 8px
}

.mr-2 {
  margin-right: 0.5rem
}

.mr-\[10px\] {
  margin-right: 10px
}

.mr-\[12px\] {
  margin-right: 12px
}

.mr-\[16px\] {
  margin-right: 16px
}

.mr-\[24px\] {
  margin-right: 24px
}

.mr-\[26px\] {
  margin-right: 26px
}

.mr-\[4px\] {
  margin-right: 4px
}

.mr-\[8px\] {
  margin-right: 8px
}

.mt-\[-2px\] {
  margin-top: -2px
}

.mt-\[-47px\] {
  margin-top: -47px
}

.mt-\[10px\] {
  margin-top: 10px
}

.mt-\[12px\] {
  margin-top: 12px
}

.mt-\[16px\] {
  margin-top: 16px
}

.mt-\[20px\] {
  margin-top: 20px
}

.mt-\[24px\] {
  margin-top: 24px
}

.mt-\[27px\] {
  margin-top: 27px
}

.mt-\[2px\] {
  margin-top: 2px
}

.mt-\[32px\] {
  margin-top: 32px
}

.mt-\[48px\] {
  margin-top: 48px
}

.mt-\[4px\] {
  margin-top: 4px
}

.mt-\[52px\] {
  margin-top: 52px
}

.mt-\[5px\] {
  margin-top: 5px
}

.mt-\[8px\] {
  margin-top: 8px
}

.box-border {
  box-sizing: border-box
}

.block {
  display: block
}

.inline-block {
  display: inline-block
}

.inline {
  display: inline
}

.flex {
  display: flex
}

.table {
  display: table
}

.grid {
  display: grid
}

.hidden {
  display: none
}

.\!h-\[57px\] {
  height: 57px !important
}

.h-\[103px\] {
  height: 103px
}

.h-\[108px\] {
  height: 108px
}

.h-\[116px\] {
  height: 116px
}

.h-\[120px\] {
  height: 120px
}

.h-\[140px\] {
  height: 140px
}

.h-\[160px\] {
  height: 160px
}

.h-\[164px\] {
  height: 164px
}

.h-\[16px\] {
  height: 16px
}

.h-\[200px\] {
  height: 200px
}

.h-\[20px\] {
  height: 20px
}

.h-\[21px\] {
  height: 21px
}

.h-\[24px\] {
  height: 24px
}

.h-\[266px\] {
  height: 266px
}

.h-\[274px\] {
  height: 274px
}

.h-\[280px\] {
  height: 280px
}

.h-\[28px\] {
  height: 28px
}

.h-\[312px\] {
  height: 312px
}

.h-\[32px\] {
  height: 32px
}

.h-\[36px\] {
  height: 36px
}

.h-\[374px\] {
  height: 374px
}

.h-\[37px\] {
  height: 37px
}

.h-\[388px\] {
  height: 388px
}

.h-\[38px\] {
  height: 38px
}

.h-\[390px\] {
  height: 390px
}

.h-\[40px\] {
  height: 40px
}

.h-\[44px\] {
  height: 44px
}

.h-\[460px\] {
  height: 460px
}

.h-\[46px\] {
  height: 46px
}

.h-\[48px\] {
  height: 48px
}

.h-\[50px\] {
  height: 50px
}

.h-\[52px\] {
  height: 52px
}

.h-\[54px\] {
  height: 54px
}

.h-\[56px\] {
  height: 56px
}

.h-\[57px\] {
  height: 57px
}

.h-\[60px\] {
  height: 60px
}

.h-\[648px\] {
  height: 648px
}

.h-\[64px\] {
  height: 64px
}

.h-\[6px\] {
  height: 6px
}

.h-\[70px\] {
  height: 70px
}

.h-\[72px\] {
  height: 72px
}

.h-\[80px\] {
  height: 80px
}

.h-\[8px\] {
  height: 8px
}

.h-\[96px\] {
  height: 96px
}

.h-\[calc\(100vh_-_126px\)\] {
  height: calc(100vh - 126px)
}

.h-full {
  height: 100%
}

.min-h-\[142px\] {
  min-height: 142px
}

.min-h-\[250px\] {
  min-height: 250px
}

.min-h-\[348px\] {
  min-height: 348px
}

.min-h-\[362px\] {
  min-height: 362px
}

.min-h-\[390px\] {
  min-height: 390px
}

.min-h-\[438px\] {
  min-height: 438px
}

.min-h-full {
  min-height: 100%
}

.\!w-\[360px\] {
  width: 360px !important
}

.\!w-\[64px\] {
  width: 64px !important
}

.w-0 {
  width: 0px
}

.w-2\/4 {
  width: 50%
}

.w-\[100px\] {
  width: 100px
}

.w-\[112px\] {
  width: 112px
}

.w-\[120px\] {
  width: 120px
}

.w-\[123px\] {
  width: 123px
}

.w-\[140px\] {
  width: 140px
}

.w-\[14px\] {
  width: 14px
}

.w-\[160px\] {
  width: 160px
}

.w-\[16px\] {
  width: 16px
}

.w-\[180px\] {
  width: 180px
}

.w-\[192px\] {
  width: 192px
}

.w-\[196px\] {
  width: 196px
}

.w-\[200px\] {
  width: 200px
}

.w-\[20px\] {
  width: 20px
}

.w-\[220px\] {
  width: 220px
}

.w-\[224px\] {
  width: 224px
}

.w-\[230px\] {
  width: 230px
}

.w-\[24px\] {
  width: 24px
}

.w-\[25\%\] {
  width: 25%
}

.w-\[260px\] {
  width: 260px
}

.w-\[264px\] {
  width: 264px
}

.w-\[266px\] {
  width: 266px
}

.w-\[284px\] {
  width: 284px
}

.w-\[28px\] {
  width: 28px
}

.w-\[32px\] {
  width: 32px
}

.w-\[330px\] {
  width: 330px
}

.w-\[360px\] {
  width: 360px
}

.w-\[365px\] {
  width: 365px
}

.w-\[36px\] {
  width: 36px
}

.w-\[389px\] {
  width: 389px
}

.w-\[390px\] {
  width: 390px
}

.w-\[408px\] {
  width: 408px
}

.w-\[40px\] {
  width: 40px
}

.w-\[438px\] {
  width: 438px
}

.w-\[44px\] {
  width: 44px
}

.w-\[462px\] {
  width: 462px
}

.w-\[48px\] {
  width: 48px
}

.w-\[49\%\] {
  width: 49%
}

.w-\[4px\] {
  width: 4px
}

.w-\[50\%\] {
  width: 50%
}

.w-\[512px\] {
  width: 512px
}

.w-\[560px\] {
  width: 560px
}

.w-\[568px\] {
  width: 568px
}

.w-\[56px\] {
  width: 56px
}

.w-\[580px\] {
  width: 580px
}

.w-\[60px\] {
  width: 60px
}

.w-\[64px\] {
  width: 64px
}

.w-\[6px\] {
  width: 6px
}

.w-\[72px\] {
  width: 72px
}

.w-\[75px\] {
  width: 75px
}

.w-\[80px\] {
  width: 80px
}

.w-\[8px\] {
  width: 8px
}

.w-\[98px\] {
  width: 98px
}

.w-full {
  width: 100%
}

.min-w-\[198px\] {
  min-width: 198px
}

.min-w-\[56px\] {
  min-width: 56px
}

.min-w-\[88px\] {
  min-width: 88px
}

.max-w-\[248px\] {
  max-width: 248px
}

.max-w-\[250px\] {
  max-width: 250px
}

.max-w-\[260px\] {
  max-width: 260px
}

.max-w-\[269px\] {
  max-width: 269px
}

.flex-1 {
  flex: 1 1 0%
}

.flex-shrink {
  flex-shrink: 1
}

.flex-shrink-0 {
  flex-shrink: 0
}

.shrink-0 {
  flex-shrink: 0
}

.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y))
}

.cursor-pointer {
  cursor: pointer
}

.select-all {
  -webkit-user-select: all;
     -moz-user-select: all;
          user-select: all
}

.resize {
  resize: both
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr))
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr))
}

.flex-col {
  flex-direction: column
}

.flex-wrap {
  flex-wrap: wrap
}

.items-end {
  align-items: flex-end
}

.items-center {
  align-items: center
}

.justify-end {
  justify-content: flex-end
}

.justify-center {
  justify-content: center
}

.justify-between {
  justify-content: space-between
}

.gap-\[5px\] {
  gap: 5px
}

.gap-\[8px\] {
  gap: 8px
}

.space-x-\[24px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(24px * var(--tw-space-x-reverse));
  margin-left: calc(24px * calc(1 - var(--tw-space-x-reverse)))
}

.space-x-\[40px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(40px * var(--tw-space-x-reverse));
  margin-left: calc(40px * calc(1 - var(--tw-space-x-reverse)))
}

.space-y-\[12px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(12px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(12px * var(--tw-space-y-reverse))
}

.space-y-\[16px\] > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(16px * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(16px * var(--tw-space-y-reverse))
}

.overflow-hidden {
  overflow: hidden
}

.overflow-y-auto {
  overflow-y: auto
}

.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap
}

.text-ellipsis {
  text-overflow: ellipsis
}

.whitespace-pre-wrap {
  white-space: pre-wrap
}

.rounded-\[13px\] {
  border-radius: 13px
}

.rounded-\[21px\] {
  border-radius: 21px
}

.rounded-\[2px\] {
  border-radius: 2px
}

.rounded-\[4px\] {
  border-radius: 4px
}

.rounded-\[50\%\] {
  border-radius: 50%
}

.rounded-\[5px\] {
  border-radius: 5px
}

.rounded-\[6px\] {
  border-radius: 6px
}

.rounded-\[8px\] {
  border-radius: 8px
}

.rounded-full {
  border-radius: 9999px
}

.rounded-bl-\[2px\] {
  border-bottom-left-radius: 2px
}

.rounded-bl-\[4px\] {
  border-bottom-left-radius: 4px
}

.rounded-bl-none {
  border-bottom-left-radius: 0px
}

.rounded-br-\[10px\] {
  border-bottom-right-radius: 10px
}

.rounded-br-\[2px\] {
  border-bottom-right-radius: 2px
}

.rounded-br-\[4px\] {
  border-bottom-right-radius: 4px
}

.rounded-br-\[8px\] {
  border-bottom-right-radius: 8px
}

.rounded-tl-\[10px\] {
  border-top-left-radius: 10px
}

.rounded-tl-\[2px\] {
  border-top-left-radius: 2px
}

.rounded-tl-\[4px\] {
  border-top-left-radius: 4px
}

.rounded-tl-\[8px\] {
  border-top-left-radius: 8px
}

.rounded-tr-\[10px\] {
  border-top-right-radius: 10px
}

.rounded-tr-\[2px\] {
  border-top-right-radius: 2px
}

.rounded-tr-\[4px\] {
  border-top-right-radius: 4px
}

.rounded-tr-\[8px\] {
  border-top-right-radius: 8px
}

.\!border-0 {
  border-width: 0px !important
}

.border {
  border-width: 1px
}

.border-0 {
  border-width: 0px
}

.border-\[0px\] {
  border-width: 0px
}

.border-\[1px\] {
  border-width: 1px
}

.border-\[6px\] {
  border-width: 6px
}

.border-b {
  border-bottom-width: 1px
}

.border-b-\[1px\] {
  border-bottom-width: 1px
}

.border-l-\[1px\] {
  border-left-width: 1px
}

.border-r-\[1px\] {
  border-right-width: 1px
}

.border-t-\[0\] {
  border-top-width: 0
}

.border-t-\[1px\] {
  border-top-width: 1px
}

.border-solid {
  border-style: solid
}

.border-dashed {
  border-style: dashed
}

.border-dotted {
  border-style: dotted
}

.border-none {
  border-style: none
}

.border-\[\#3887F5\] {
  --tw-border-opacity: 1;
  border-color: rgb(56 135 245 / var(--tw-border-opacity))
}

.border-\[\#A0CFFB\] {
  --tw-border-opacity: 1;
  border-color: rgb(160 207 251 / var(--tw-border-opacity))
}

.border-\[\#B9F2D3\] {
  --tw-border-opacity: 1;
  border-color: rgb(185 242 211 / var(--tw-border-opacity))
}

.border-\[\#CCCCCC\] {
  --tw-border-opacity: 1;
  border-color: rgb(204 204 204 / var(--tw-border-opacity))
}

.border-\[\#DDDDDD\] {
  --tw-border-opacity: 1;
  border-color: rgb(221 221 221 / var(--tw-border-opacity))
}

.border-\[\#EB4738\] {
  --tw-border-opacity: 1;
  border-color: rgb(235 71 56 / var(--tw-border-opacity))
}

.border-\[\#EEEEEE\] {
  --tw-border-opacity: 1;
  border-color: rgb(238 238 238 / var(--tw-border-opacity))
}

.border-\[\#F9E1D9\] {
  --tw-border-opacity: 1;
  border-color: rgb(249 225 217 / var(--tw-border-opacity))
}

.border-\[\#FBCFC3\] {
  --tw-border-opacity: 1;
  border-color: rgb(251 207 195 / var(--tw-border-opacity))
}

.border-\[\#FFCBC8\] {
  --tw-border-opacity: 1;
  border-color: rgb(255 203 200 / var(--tw-border-opacity))
}

.border-\[\#FFFFFF\] {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity))
}

.border-\[rgba\(255\2c 255\2c 255\2c 0\.3\)\] {
  border-color: rgba(255,255,255,0.3)
}

.\!bg-\[rgba\(0\2c 0\2c 0\2c 0\.7\)\] {
  background-color: rgba(0,0,0,0.7) !important
}

.bg-\[\#031633\] {
  --tw-bg-opacity: 1;
  background-color: rgb(3 22 51 / var(--tw-bg-opacity))
}

.bg-\[\#091627\] {
  --tw-bg-opacity: 1;
  background-color: rgb(9 22 39 / var(--tw-bg-opacity))
}

.bg-\[\#22BF7E\] {
  --tw-bg-opacity: 1;
  background-color: rgb(34 191 126 / var(--tw-bg-opacity))
}

.bg-\[\#3887F5\] {
  --tw-bg-opacity: 1;
  background-color: rgb(56 135 245 / var(--tw-bg-opacity))
}

.bg-\[\#3887f5\] {
  --tw-bg-opacity: 1;
  background-color: rgb(56 135 245 / var(--tw-bg-opacity))
}

.bg-\[\#800003\] {
  --tw-bg-opacity: 1;
  background-color: rgb(128 0 3 / var(--tw-bg-opacity))
}

.bg-\[\#E6F5EC\] {
  --tw-bg-opacity: 1;
  background-color: rgb(230 245 236 / var(--tw-bg-opacity))
}

.bg-\[\#EB4738\] {
  --tw-bg-opacity: 1;
  background-color: rgb(235 71 56 / var(--tw-bg-opacity))
}

.bg-\[\#EDF3F7\] {
  --tw-bg-opacity: 1;
  background-color: rgb(237 243 247 / var(--tw-bg-opacity))
}

.bg-\[\#F54A3A\] {
  --tw-bg-opacity: 1;
  background-color: rgb(245 74 58 / var(--tw-bg-opacity))
}

.bg-\[\#F5F5F7\] {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 247 / var(--tw-bg-opacity))
}

.bg-\[\#F9F9F9\] {
  --tw-bg-opacity: 1;
  background-color: rgb(249 249 249 / var(--tw-bg-opacity))
}

.bg-\[\#FAEDDC\] {
  --tw-bg-opacity: 1;
  background-color: rgb(250 237 220 / var(--tw-bg-opacity))
}

.bg-\[\#FCE0D7\] {
  --tw-bg-opacity: 1;
  background-color: rgb(252 224 215 / var(--tw-bg-opacity))
}

.bg-\[\#FCF1ED\] {
  --tw-bg-opacity: 1;
  background-color: rgb(252 241 237 / var(--tw-bg-opacity))
}

.bg-\[\#FFEFEF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 239 239 / var(--tw-bg-opacity))
}

.bg-\[\#FFF1E1\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 241 225 / var(--tw-bg-opacity))
}

.bg-\[\#FFFFFF\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity))
}

.bg-\[\#ffffff\] {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity))
}

.bg-\[rgba\(0\2c 0\2c 0\2c 0\.6\)\] {
  background-color: rgba(0,0,0,0.6)
}

.bg-\[rgba\(0\2c 0\2c 0\2c 0\.7\)\] {
  background-color: rgba(0,0,0,0.7)
}

.bg-\[rgba\(255\2c 255\2c 255\2c 0\.9\)\] {
  background-color: rgba(255,255,255,0.9)
}

.bg-\[rgba\(56\2c 135\2c 245\2c 0\.08\)\] {
  background-color: rgba(56,135,245,0.08)
}

.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity))
}

.bg-\[linear-gradient\(_180deg\2c _\#EBF2FC_0\%\2c _rgba\(235\2c 242\2c 252\2c 0\)_100\%\)\] {
  background-image: linear-gradient( 180deg, #EBF2FC 0%, rgba(235,242,252,0) 100%)
}

.bg-\[linear-gradient\(_94deg\2c _\#F2F9FE_0\%\2c _\#BDE0FC_100\%\)\] {
  background-image: linear-gradient( 94deg, #F2F9FE 0%, #BDE0FC 100%)
}

.bg-\[length\:100\%_100\%\] {
  background-size: 100% 100%
}

.object-cover {
  -o-object-fit: cover;
     object-fit: cover
}

.\!p-0 {
  padding: 0px !important
}

.\!p-\[0\] {
  padding: 0 !important
}

.\!p-\[0px\] {
  padding: 0px !important
}

.\!p-\[24px\] {
  padding: 24px !important
}

.p-0 {
  padding: 0px
}

.p-\[0\] {
  padding: 0
}

.p-\[12px\] {
  padding: 12px
}

.p-\[16px\] {
  padding: 16px
}

.p-\[24px\] {
  padding: 24px
}

.px-\[10px\] {
  padding-left: 10px;
  padding-right: 10px
}

.px-\[12px\] {
  padding-left: 12px;
  padding-right: 12px
}

.px-\[13px\] {
  padding-left: 13px;
  padding-right: 13px
}

.px-\[16px\] {
  padding-left: 16px;
  padding-right: 16px
}

.px-\[24px\] {
  padding-left: 24px;
  padding-right: 24px
}

.px-\[2px\] {
  padding-left: 2px;
  padding-right: 2px
}

.px-\[32px\] {
  padding-left: 32px;
  padding-right: 32px
}

.px-\[4px\] {
  padding-left: 4px;
  padding-right: 4px
}

.px-\[8px\] {
  padding-left: 8px;
  padding-right: 8px
}

.py-\[16px\] {
  padding-top: 16px;
  padding-bottom: 16px
}

.py-\[17px\] {
  padding-top: 17px;
  padding-bottom: 17px
}

.py-\[24px\] {
  padding-top: 24px;
  padding-bottom: 24px
}

.py-\[4px\] {
  padding-top: 4px;
  padding-bottom: 4px
}

.py-\[64px\] {
  padding-top: 64px;
  padding-bottom: 64px
}

.py-\[6px\] {
  padding-top: 6px;
  padding-bottom: 6px
}

.\!pb-\[10px\] {
  padding-bottom: 10px !important
}

.\!pb-\[16px\] {
  padding-bottom: 16px !important
}

.\!pb-\[24px\] {
  padding-bottom: 24px !important
}

.\!pb-\[32px\] {
  padding-bottom: 32px !important
}

.\!pb-\[8px\] {
  padding-bottom: 8px !important
}

.\!pl-\[12px\] {
  padding-left: 12px !important
}

.\!pl-\[14px\] {
  padding-left: 14px !important
}

.\!pl-\[16px\] {
  padding-left: 16px !important
}

.\!pl-\[24px\] {
  padding-left: 24px !important
}

.\!pl-\[8px\] {
  padding-left: 8px !important
}

.\!pr-\[0\] {
  padding-right: 0 !important
}

.\!pr-\[16px\] {
  padding-right: 16px !important
}

.\!pr-\[8px\] {
  padding-right: 8px !important
}

.\!pt-\[10px\] {
  padding-top: 10px !important
}

.\!pt-\[16px\] {
  padding-top: 16px !important
}

.\!pt-\[18px\] {
  padding-top: 18px !important
}

.\!pt-\[24px\] {
  padding-top: 24px !important
}

.\!pt-\[36px\] {
  padding-top: 36px !important
}

.\!pt-\[40px\] {
  padding-top: 40px !important
}

.\!pt-\[4px\] {
  padding-top: 4px !important
}

.\!pt-\[8px\] {
  padding-top: 8px !important
}

.pb-0 {
  padding-bottom: 0px
}

.pb-\[0px\] {
  padding-bottom: 0px
}

.pb-\[12px\] {
  padding-bottom: 12px
}

.pb-\[15px\] {
  padding-bottom: 15px
}

.pb-\[16px\] {
  padding-bottom: 16px
}

.pb-\[24px\] {
  padding-bottom: 24px
}

.pb-\[30px\] {
  padding-bottom: 30px
}

.pb-\[32px\] {
  padding-bottom: 32px
}

.pb-\[36px\] {
  padding-bottom: 36px
}

.pb-\[72px\] {
  padding-bottom: 72px
}

.pb-\[8px\] {
  padding-bottom: 8px
}

.pl-\[10px\] {
  padding-left: 10px
}

.pl-\[16px\] {
  padding-left: 16px
}

.pl-\[22px\] {
  padding-left: 22px
}

.pl-\[24px\] {
  padding-left: 24px
}

.pl-\[26px\] {
  padding-left: 26px
}

.pl-\[3px\] {
  padding-left: 3px
}

.pl-\[40px\] {
  padding-left: 40px
}

.pl-\[44px\] {
  padding-left: 44px
}

.pl-\[4px\] {
  padding-left: 4px
}

.pl-\[7px\] {
  padding-left: 7px
}

.pl-\[80px\] {
  padding-left: 80px
}

.pl-\[8px\] {
  padding-left: 8px
}

.pr-\[10px\] {
  padding-right: 10px
}

.pr-\[16px\] {
  padding-right: 16px
}

.pr-\[20px\] {
  padding-right: 20px
}

.pr-\[24px\] {
  padding-right: 24px
}

.pr-\[39px\] {
  padding-right: 39px
}

.pr-\[40px\] {
  padding-right: 40px
}

.pr-\[4px\] {
  padding-right: 4px
}

.pr-\[80px\] {
  padding-right: 80px
}

.pt-\[12px\] {
  padding-top: 12px
}

.pt-\[16px\] {
  padding-top: 16px
}

.pt-\[20px\] {
  padding-top: 20px
}

.pt-\[24px\] {
  padding-top: 24px
}

.pt-\[255px\] {
  padding-top: 255px
}

.pt-\[32px\] {
  padding-top: 32px
}

.pt-\[45px\] {
  padding-top: 45px
}

.pt-\[48px\] {
  padding-top: 48px
}

.pt-\[4px\] {
  padding-top: 4px
}

.pt-\[52px\] {
  padding-top: 52px
}

.pt-\[72px\] {
  padding-top: 72px
}

.pt-\[7px\] {
  padding-top: 7px
}

.pt-\[8px\] {
  padding-top: 8px
}

.text-center {
  text-align: center
}

.text-right {
  text-align: right
}

.text-end {
  text-align: end
}

.indent-\[12px\] {
  text-indent: 12px
}

.indent-\[8px\] {
  text-indent: 8px
}

.\!text-\[12px\] {
  font-size: 12px !important
}

.\!text-\[16px\] {
  font-size: 16px !important
}

.\!text-\[18px\] {
  font-size: 18px !important
}

.\!text-\[20px\] {
  font-size: 20px !important
}

.text-\[12px\] {
  font-size: 12px
}

.text-\[13px\] {
  font-size: 13px
}

.text-\[14px\] {
  font-size: 14px
}

.text-\[16px\] {
  font-size: 16px
}

.text-\[18px\] {
  font-size: 18px
}

.text-\[20px\] {
  font-size: 20px
}

.text-\[22px\] {
  font-size: 22px
}

.text-\[23px\] {
  font-size: 23px
}

.text-\[24px\] {
  font-size: 24px
}

.text-\[54px\] {
  font-size: 54px
}

.font-\[500\] {
  font-weight: 500
}

.font-\[600\] {
  font-weight: 600
}

.font-\[700\] {
  font-weight: 700
}

.font-bold {
  font-weight: 700
}

.font-medium {
  font-weight: 500
}

.font-normal {
  font-weight: 400
}

.italic {
  font-style: italic
}

.\!leading-\[1\] {
  line-height: 1 !important
}

.\!leading-\[24px\] {
  line-height: 24px !important
}

.leading-\[16px\] {
  line-height: 16px
}

.leading-\[1\] {
  line-height: 1
}

.leading-\[20px\] {
  line-height: 20px
}

.leading-\[23px\] {
  line-height: 23px
}

.leading-\[35px\] {
  line-height: 35px
}

.leading-\[36px\] {
  line-height: 36px
}

.leading-\[44px\] {
  line-height: 44px
}

.leading-\[54px\] {
  line-height: 54px
}

.leading-none {
  line-height: 1
}

.\!text-\[\#3887F5\] {
  --tw-text-opacity: 1 !important;
  color: rgb(56 135 245 / var(--tw-text-opacity)) !important
}

.\!text-\[\#666666\] {
  --tw-text-opacity: 1 !important;
  color: rgb(102 102 102 / var(--tw-text-opacity)) !important
}

.\!text-\[\#999999\] {
  --tw-text-opacity: 1 !important;
  color: rgb(153 153 153 / var(--tw-text-opacity)) !important
}

.\!text-\[\#CCCCCC\] {
  --tw-text-opacity: 1 !important;
  color: rgb(204 204 204 / var(--tw-text-opacity)) !important
}

.\!text-\[\#FFFFFF\] {
  --tw-text-opacity: 1 !important;
  color: rgb(255 255 255 / var(--tw-text-opacity)) !important
}

.text-\[\#222222\] {
  --tw-text-opacity: 1;
  color: rgb(34 34 34 / var(--tw-text-opacity))
}

.text-\[\#22BF7E\] {
  --tw-text-opacity: 1;
  color: rgb(34 191 126 / var(--tw-text-opacity))
}

.text-\[\#333333\] {
  --tw-text-opacity: 1;
  color: rgb(51 51 51 / var(--tw-text-opacity))
}

.text-\[\#3887F5\] {
  --tw-text-opacity: 1;
  color: rgb(56 135 245 / var(--tw-text-opacity))
}

.text-\[\#3887f5\] {
  --tw-text-opacity: 1;
  color: rgb(56 135 245 / var(--tw-text-opacity))
}

.text-\[\#666666\] {
  --tw-text-opacity: 1;
  color: rgb(102 102 102 / var(--tw-text-opacity))
}

.text-\[\#999999\] {
  --tw-text-opacity: 1;
  color: rgb(153 153 153 / var(--tw-text-opacity))
}

.text-\[\#CCCCCC\] {
  --tw-text-opacity: 1;
  color: rgb(204 204 204 / var(--tw-text-opacity))
}

.text-\[\#D36D64\] {
  --tw-text-opacity: 1;
  color: rgb(211 109 100 / var(--tw-text-opacity))
}

.text-\[\#DDDDDD\] {
  --tw-text-opacity: 1;
  color: rgb(221 221 221 / var(--tw-text-opacity))
}

.text-\[\#EB4738\] {
  --tw-text-opacity: 1;
  color: rgb(235 71 56 / var(--tw-text-opacity))
}

.text-\[\#F2AF30\] {
  --tw-text-opacity: 1;
  color: rgb(242 175 48 / var(--tw-text-opacity))
}

.text-\[\#F34747\] {
  --tw-text-opacity: 1;
  color: rgb(243 71 71 / var(--tw-text-opacity))
}

.text-\[\#F78C3E\] {
  --tw-text-opacity: 1;
  color: rgb(247 140 62 / var(--tw-text-opacity))
}

.text-\[\#FF6D55\] {
  --tw-text-opacity: 1;
  color: rgb(255 109 85 / var(--tw-text-opacity))
}

.text-\[\#FFDFAE\] {
  --tw-text-opacity: 1;
  color: rgb(255 223 174 / var(--tw-text-opacity))
}

.text-\[\#FFFFFF\] {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity))
}

.text-\[\#ff851f\] {
  --tw-text-opacity: 1;
  color: rgb(255 133 31 / var(--tw-text-opacity))
}

.underline {
  text-decoration-line: underline
}

.line-through {
  text-decoration-line: line-through
}

.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale
}

.opacity-100 {
  opacity: 1
}

.opacity-50 {
  opacity: 0.5
}

.shadow {
  --tw-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow)
}

.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px
}

.outline {
  outline-style: solid
}

.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.grayscale {
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow)
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms
}

.duration-500 {
  transition-duration: 500ms
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1)
}

.\!\[box-shadow\:0px_2px_12px_0px_rgba\(0\2c _0\2c _0\2c _0\.12\)\] {
  box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.12) !important
}

.\[box-shadow\:0px_0px_4px_0px_rgba\(0\2c 0\2c 0\2c 0\.1\)\] {
  box-shadow: 0px 0px 4px 0px rgba(0,0,0,0.1)
}

.\[box-shadow\:0px_2px_10px_0px_rgba\(0\2c 0\2c 0\2c 0\.1\)\] {
  box-shadow: 0px 2px 10px 0px rgba(0,0,0,0.1)
}

.\[box-shadow\:0px_2px_12px_0px_rgba\(0\2c 0\2c 0\2c 0\.12\)\] {
  box-shadow: 0px 2px 12px 0px rgba(0,0,0,0.12)
}

.\[box-shadow\:0px_2px_8px_0px_rgba\(0\2c 0\2c 0\2c 0\.12\)\] {
  box-shadow: 0px 2px 8px 0px rgba(0,0,0,0.12)
}

.\[box-shadow\:2px_0px_8px_0px_rgba\(0\2c 0\2c 0\2c 0\.08\)\] {
  box-shadow: 2px 0px 8px 0px rgba(0,0,0,0.08)
}

.\[box-shadow\:inset_0px_-1px_0px_0px_\#EEEEEE\] {
  box-shadow: inset 0px -1px 0px 0px #EEEEEE
}

.\[box-shadow\:inset_0px_1px_0px_0px_\#EEEEEE\] {
  box-shadow: inset 0px 1px 0px 0px #EEEEEE
}

.\[text-decoration-line\:line-through\] {
  text-decoration-line: line-through
}

.\[text-shadow\:0px_0px_0px_rgba\(255\2c 255\2c 255\2c 0\.41\)\2c _inset_0px_0px_0px_rgba\(211\2c 46\2c 27\2c 0\.93\)\] {
  text-shadow: 0px 0px 0px rgba(255,255,255,0.41), inset 0px 0px 0px rgba(211,46,27,0.93)
}

.first-of-type\:ml-\[0\]:first-of-type {
  margin-left: 0
}

.first-of-type\:ml-\[0px\]:first-of-type {
  margin-left: 0px
}

.first-of-type\:mt-\[4px\]:first-of-type {
  margin-top: 4px
}

.first-of-type\:pl-\[0px\]:first-of-type {
  padding-left: 0px
}

.first-of-type\:pt-\[0px\]:first-of-type {
  padding-top: 0px
}

.hover\:bg-\[\#EDF3F7\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(237 243 247 / var(--tw-bg-opacity))
}

.hover\:bg-\[\#F5F5F7\]:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(245 245 247 / var(--tw-bg-opacity))
}

.hover\:bg-\[rgba\(255\2c 255\2c 255\2c 0\.1\)\]:hover {
  background-color: rgba(255,255,255,0.1)
}

.hover\:text-\[\#3887F5\]:hover {
  --tw-text-opacity: 1;
  color: rgb(56 135 245 / var(--tw-text-opacity))
}

.group:hover .group-hover\:\!visible {
  visibility: visible !important
}

.\[\&\:not\(\:first-child\)\]\:mt-\[24px\]:not(:first-child) {
  margin-top: 24px
}
