<template>
  <div class="case-wrap">
    <case-update />
    <div class="text-[14px] text-[#333333] flex  pb-[16px]">
      推送案源<span class="text-[#3887F5]">{{ casePushNum }}</span> 条 ，仅展示{{ dayDes }}的推荐案源哦~
    </div>
    <div class="  flex rounded-[4px] border-[1px] border-solid border-[#EEEEEE]">
      <div class="flex-shrink-0 translate-x-0 w-[462px] border-0 box-border  border-r-[1px] border-solid border-[#EEEEEE]">
        <div class="case-wrap-c relative">
          <div v-if="list.length <1" class="none-data tc">
            <img class="w-[160px] pt-[72px] pb-[15px]" src="@/assets/images/empty.png" />
            <p class="text-[14px] text-[#666666]">当前条件下暂无数据～</p>
          </div>
          <div class="pb-[72px]" v-else>
            <div class="px-[24px] hover:bg-[#F5F5F7]" v-for="(i,index) in list" :key="i.caseSourceV2Id"
                 @click="handleListItem(index)"
                 :class="[ Number(i.caseSourceV2Id) === Number(detailId) && 'bg-[#F5F5F7]']">
              <div class=" py-[16px]  border-0 box-border cursor-pointer  border-b-[1px] border-solid border-[#EEEEEE]">
                <div class="flex justify-between items-center ">
                  <p class="font-bold text-[16px] text-[#333333] flex  items-center">
                    {{ i.typeLabel }}
                    <i
                      v-if="i.readStatus===0"
                      class="ml-[4px] w-[8px] h-[8px] bg-[#EB4738] rounded-[8px] border-[1px] border-solid border-[#FFFFFF]" />
                  </p>
                  <p class="text-[12px] text-[#999999]">{{ i.createTime }}</p>
                </div>
                <p class="leading-[35px] text-ellipsis text-[14px] text-[#666666]">{{ i.info }}</p>
                <div class="flex justify-between items-center ">
                  <case-source-tag :list="i._tags" />
                  <p class="text-[12px] text-[#999999]">{{ i.provinceName }}{{ i.regionName }}</p>
                </div>
              </div>
            </div>
            <!--    分页      -->
            <div  class="fixed bg-[#ffffff] border-0 border-[#EEEEEE] border-solid  border-t-[1px] bottom-0 left-0 right-0 px-[24px] pt-[12px] pb-[24px] ">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page.sync="currentPage"
                :page-size="pageSize"
                :pager-count="5"
                layout="prev, pager, next, jumper"
                :total="total" />
            </div>
          </div>
        </div>
      </div>
      <div class="flex-1 case-wrap-c">
        <div v-if="!basics.isNull(detail)&&!basics.isObjNull(detail)">
          <div class="p-[24px] pb-[0px]  ">
            <div class="pb-[24px] border-0 border-b-[1px] border-dotted border-[#DDDDDD]">
              <p class="font-bold text-[23px] text-[#333333]">{{ detail.typeLabel }}</p>
              <p class="text-[12px] text-[#999999] !pt-[8px]">{{ detail.createTime }}</p>
            </div>
            <div class="grid grid-cols-2 pt-[16px]">
              <div class="flex items-center pt-[20px]" v-for="(i,index) in detailList" :key="index">
                <p class="text-[14px] text-[#666666]">{{i.label}}</p>
                <p class="text-[14px] text-[#333333] !pl-[8px]" :class="i.className">
                  {{basics.isString(i.key) ? detail[i.key] : i.key(detail)}}
                </p>
              </div>
              <div class="flex items-center pt-[20px]">
                <p class="text-[14px] text-[#666666]">线索价格：</p>
                <p class="text-[14px] text-[#333333] flex items-center justify-center !pl-[8px]">
                  {{priceYuan(detail.serverAmount)}}
                  <span v-if="detail.originalServerAmount" class="pl-[8px] text-[14px] text-[#999999] [text-decoration-line:line-through]">{{priceYuan(detail.originalServerAmount)}}</span>
                </p>
              </div>
              <div class="flex items-center pt-[20px]">
                <p class="text-[14px] text-[#666666]">独享价格：</p>
                <p class="text-[14px] text-[#333333]  flex items-center justify-center !pl-[8px]">
                  {{priceYuan(detail.ykjAmount)}}
                  <span v-if="detail.originalYkjAmount" class="pl-[8px] text-[14px] text-[#999999] [text-decoration-line:line-through]">{{priceYuan(detail.originalYkjAmount)}}</span>
                </p>
              </div>
            </div>
            <div class=" pt-[20px]">
              <p class="text-[14px] !pb-[8px] text-[#666666]">描述：</p>
              <div class="bg-[#F5F5F7] rounded-[4px] p-[12px]">
                <p class="text-[14px] text-[#666666] word-wrap">
                  {{detail.info}}
                </p>
              </div>
            </div>
          </div>
          <div class="sticky left-0 right-0 bottom-0">
            <div class="p-[24px] flex items-center justify-end">
              <app-button
                v-if="detail.status !== 3"
                type="primary"
                size="large"
                @click="
										handleGrabOrder({
											id: detail.caseSourceV2Id,
											amount: detail.serverAmount,
											isCaseSource: true
										})
									">抢单
              </app-button>
              <app-button
                class="ml-[16px]"
                v-if="detail.status === 1"
                type="success"
                size="large"
                @click="
										handleGrabOrder({
											id: detail.caseSourceV2Id,
											way: 1,
											amount: detail.ykjAmount,
											isCaseSource: true
										})
									"
              >独享
              </app-button>
            </div>
          </div>
        </div>
      </div>
    </div>
    <app-popup
      @confirm="grabOrder"
      title="温馨提示"
      show-cancel
      width="400px"
      :visible.sync="isShowGrab"
      confirm-text="继续抢"
    >
      <div>
        <div class="ml-[8px] py-[16px] px-[24px] text-[14px] text-[#666666] flex items-center">
          <i
            class="text-[24px] w-[24px] h-[24px] mr-[8px] iconfont icon-xianxingtubiao-3 text-[#ff851f]"
          />
          {{ showGrabMessage }}
        </div>
      </div>
    </app-popup>
    <app-popup
      @confirm="grabOrder"
      title="温馨提示"
      show-cancel
      width="400px"
      :visible.sync="isShowDeductNum"
    >
      <div>
        <div class="ml-[8px] py-[16px] px-[24px] text-[14px] text-[#666666] flex items-center">
          <i
            class="text-[24px] w-[24px] h-[24px] mr-[8px] iconfont icon-xianxingtubiao-3 text-[#ff851f]"
          />
          独享抢单将扣除{{ deductNum }}次权益哦~
        </div>
      </div>
    </app-popup>

    <!--  1.0.9抢单流程  -->
    <grab-order-pop :remaining-grab-times="remainingGrabTimes" :show.sync="dialogVisible" :detail="currentParams" @confirmPop="submitPop" />
  </div>
</template>

<script>
import {
  caseGrab,
  caseGrabCheck, caseGrabV2,
  caseRemainTimes,
  caseSourceCluesWeek,
  lcCluePlanV2RecordRead,
  caseGrabV3
} from "@/api/clues";
import { commonConfigKey, dataDetailList } from "@/api/common";
import AppPopup from "components/appPopup/index.vue";
import CaseUpdate from "views/systemAdministration/caseSourcePush/components/caseUpdate.vue";
import AppButton from "components/appButton/index.vue";
import CaseSourceTag from "components/CaseSourceTag/index.vue";
import { priceYuan } from "../../../utils/toolMethods";
import GrabOrderPop from "views/leadManagement/clueSquare/cluesToTheSourceOfTheCase/grabOrderPop.vue";
import { isLeadPackageDisplay } from "components/leadPackageDisplay";
import { judgeBalance } from "views/leadManagement/clueSquare/cluesToTheSourceOfTheCase";
import handleGrabOrder from "@/minixs/handleGrabOrder";


export default {
  name: "CaseSourcePush",
  components: { GrabOrderPop, CaseSourceTag, AppButton, CaseUpdate, AppPopup },
  mixins: [handleGrabOrder],
  data() {
    return {
      query: {
        typeValue: "",
      },
      /** 显示是否被同律所抢单 */
      isShowGrab: false,
      /** 显示扣除次数弹窗 */
      isShowDeductNum: false,
      /** 当前点击 */
      currentParams: {},
      /** 显示是否被同律所抢单提示信息 */
      showGrabMessage: "",
      /** 扣除次数 */
      deductNum: 0,
      dayDes: "",
      currentPage: 1,
      pageSize: 10,
      total: 0,
      list: [],
      /* 详情数据*/
      detail: {},
      /* 选中的详情id*/
      detailId: null,
      /* 金额类型*/
      amountGrade: {},
      detailList: Object.freeze([{
        label: "线索ID：",
        key: "caseSourceV2Id"
      }, {
        label: "发布时间：",
        key: "createTime"
      }, {
        label: "已跟进律师：",
        key: (row) => {
          return `${row.consumptionCount}/${row.consumptionCountMax}`;
        }
      }, {
        label: "发布人手机号：",
        key: "userPhone"
      }, {
        label: "发布地区：",
        key: (row) => {
          return `${row.provinceName}/${row.regionName}`;
        }
      }, {
        label: "涉案金额：",
        key: "amountGradeLabel",
        className: "text-[#F78C3E]"
      }, {
        label: "线索类型：",
        key: "typeLabel"
      },
      //   {
      //   label: "线索价格：",
      //   key: (row) => {
      //     return `￥${priceYuanNum(row.serverAmount || 0)}`;
      //   }
      // }
      ]),
      dialogVisible: false,
      remainingGrabTimes: {}
    };
  },
  computed: {
    casePushNum() {
      return this.$store.getters.getCasePushNum;
    }
  },
  created() {
    this.getRemainingGrabTimes();
    this.getDeductNum();
    /* 获取金额类型*/
    dataDetailList({
      groupCode: "AMOUNT_GRADE"
    }).then((r) => {
      (r || []).forEach(item => {
        this.amountGrade[item.value] = item.label;
      });
      this.getList();
    });
    /* 制空案源推送数量*/
    // this.$store.commit("SET_THE_NUMBER_OF_CASE_FEEDS", 0);
  },
  methods: {
    priceYuan,
    /** 获取列表**/
    getList() {
      const data = {
        currentPage: this.currentPage,
        pageSize: this.pageSize
      };
      caseSourceCluesWeek(data).then((r) => {
        let findIndex = 0;
        const { records = [] } = r;
        this.list = (records || []).map(item => {
          const amountGradeLabel = this.amountGrade[String(item.amountGrade)];
          return{
            ...item,
            typeLabel: item.typeLabel || "未知",
            amountGradeLabel: amountGradeLabel || "-",
            _tags: [...(amountGradeLabel ? [{
              label: amountGradeLabel,
            }] : []), ...(item.caseLabelVos || [])]
          };
        });
        this.total = r.total;
        if (this.basics.isArrNull(records)) {
          findIndex = -1;
        }else if (this.$route.query.id) {
          findIndex = records.findIndex((s) => Number(s.caseSourceV2Id) === Number(this.$route.query.id));
        }
        this.getDetail(findIndex);
      });
    },
    /** 获取详情 **/
    getDetail(index) {
      if(index < 0) {
        this.detailId = null;
        this.detail = {};
        return;
      }
      const item = this.list[index];
      this.detailId = item.caseSourceV2Id;
      this.detail = item;
      /* 未读才变成已读*/
      if(item.readStatus === 0){
        lcCluePlanV2RecordRead({ caseSourceV2Id: this.detailId }).then(() => {
          // 推送数量 header上小红点
          this.$store.commit("REDUCE_NOTICE_COUNTS", "caseSourcePush");
        });
      }

      this.$set(item, "readStatus", 1);
      // lcNoticeDetailById({ id: this.detailId }).then(r => {
      //   console.log(r, "详情");
      //   if (r.pic) r.imgs = r.pic.split(",");
      //   this.detail = r;
      // });
    },
    /** 点击列表的单条数据**/
    handleListItem(index) {
      this.getDetail(index);
    },
    /** 获取扣除次数 */
    getDeductNum() {
      commonConfigKey({
        paramName: "lc_grab_case_deduct_num",
      }).then(res => {
        this.deductNum = res;
      });
      commonConfigKey({
        paramName: "lc_recommend_case_source_show_day",
      }).then(res => {
        // 0  = 当天 1 =一天   ,2 两天
        this.dayDes = !Number(res) ? "当天" : res + "天";
      });
    },
    /** 确定选中的支付方式 1 线索包 3 赠送 2 充值  **/
    submitPop(item){
      this.currentParams.accountType = ~~item;
      this.submitGrabOrder();
    },

    submitGrabOrder(){
      // 如果是独享
      // if (this.currentParams.way === 1) {
      //   // this.isShowDeductNum = true;
      //   this.grabOrder();
      //   return;
      // }

      // caseGrabCheck({ id: this.currentParams.id }).then(res => {
      //   if (res.code === 1) {
      //     this.isShowGrab = true;
      //     this.showGrabMessage = res.message;
      //   } else {
      //     this.grabOrder();
      //   }
      // });
      this.grabOrder();
    },
    /** 抢单 */
    grabOrder() {

      caseGrabV3(this.currentParams).then((res) => {
        console.log(res, 999);
        if(res.code === 1){
          // 弹出已经被抢单的弹框
          this.isShowGrab = true;
          this.currentParams.confirmId = res.confirmId;
          this.showGrabMessage = res.message;
          return;
        }else{
          this.$message.success("抢单成功，快去跟进吧~");
          this.getRemainingGrabTimes();
          this.getList();
          this.isShowDeductNum = false;
          this.isShowGrab = false;
        }
      });
    },
    /** 查询可抢剩余次数 */
    getRemainingGrabTimes() {
      caseRemainTimes().then(res => {
        this.remainingGrabTimes = res;
      });
    },
    handleSizeChange(val) {
      console.log(val, 9);
    },
    handleCurrentChange(val) {
      console.log(val, 8);
      this.currentPage = val;
      this.getList();
    },
  }
};
</script>

<style lang="scss" scoped>
.case-wrap {
  background: #FFFFFF;
  padding: 0 24px 24px;
}

.case-wrap-c {
  height: calc(100vh - 260px);
  overflow-y: auto;
}

</style>
