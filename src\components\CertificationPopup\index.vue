<template>
	<app-popup title="实名认证" width="560px" hide-footer :visible="show" @close="close">
		<div class="px-[24px] mt-[16px]">
			<el-form
				label-width="80px"
				label-position="right"
				ref="form"
				class="form"
				:rules="rules"
				:model="formData"
				:disabled="disabled"
				inline
			>
				<el-form-item label="手机号" prop="certPhone">
					<el-input
						class="w-full"
						maxlength="11"
						disabled
						placeholder="请输入手机号"
						v-model.trim="formData.certPhone"
					/>
				</el-form-item>
				<div class="flex justify-between">
					<el-form-item prop="certIdCardImgHead" label="身份证照">
						<certification-upload-image v-model="formData.certIdCardImgHead" :limit="1">
							<div class="text-[14px] text-[#CCCCCC] leading-none">
								<img
									class="block w-[75px] h-[40px] my-[12px] mx-auto"
									alt=""
									src="@/components/CertificationPopup/img/tnbi3s.png"
								/>
								<div>上传身份证正面照</div>
								<div class="mt-[8px]">
									（国徽面）
								</div>
							</div>
						</certification-upload-image>
					</el-form-item>
					<el-form-item prop="certIdCardImgBadge">
						<certification-upload-image v-model="formData.certIdCardImgBadge" :limit="1">
							<img
								class="block w-[75px] h-[40px] my-[12px] mx-auto"
								alt=""
								src="@/components/CertificationPopup/img/yptqun.png"
							/>
							<div class="text-[14px] text-[#CCCCCC] leading-none">
								<div>上传身份证正面照</div>
								<div class="mt-[8px]">
									（人像面）
								</div>
							</div>
						</certification-upload-image>
					</el-form-item>
				</div>
				<div class="text-[12px] text-[#EB4738] pl-[80px] mb-[24px]">
					温馨提示：1、保持四角完整显示；2、请勿裁剪或遮挡；3、避免逆光和强光反射，导致模糊不清
				</div>
				<el-form-item label="身份证号" prop="certIdCardNumber">
					<el-input
						placeholder="请输入身份证号（需与身份证信息保持一致）"
						v-model.trim="formData.certIdCardNumber"
					/>
				</el-form-item>
				<el-form-item label="真实姓名" prop="certName">
					<el-input
						placeholder="请输入真实姓名（需与身份证信息保持一致）"
						v-model.trim="formData.certName"
					/>
				</el-form-item>
				<el-form-item prop="certPhone" v-if="this.certStatus === 3" label="失败原因">
					<div class="text-[14px] text-[#333333]">{{ detail.checkRemark }}</div>
				</el-form-item>
			</el-form>
		</div>
		<div
			v-if="certStatus !== 2"
			class="flex items-center justify-end pt-[8px] [box-shadow:inset_0px_1px_0px_0px_#EEEEEE] pb-[24px] px-[24px]"
		>
			<div v-if="isAudit" class="text-[14px] text-[#3887F5]">审核中，请等待认证结果</div>
			<div v-else-if="isPass" class="text-[14px] text-[#3887F5]">审核通过</div>
			<app-button @click="handleSubmit" v-else>
				{{ buttonText }}
			</app-button>
		</div>
	</app-popup>
</template>

<script>
import AppPopup from "components/appPopup/index.vue";
import { regular } from "utils/validate";
import AppButton from "components/appButton/index.vue";
import CertificationUploadImage from "components/uploadImage/CertificationUploadImage.vue";
import { mapGetters } from "vuex";
import { getCertInfoByStaffId, uploadCertInfo } from "@/api/common";
import { lawyerCertStatus } from "utils/config";

/**
 * https://lanhuapp.com/web/#/item/project/product?pid=167f6e04-0cf0-4786-9d6a-cbca654ac27d&image_id=ae46bb6e-f788-445d-a12e-6471097b0ad7&tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&versionId=1989d35e-ff2c-423a-8c9d-6927ab6b0c55&docId=ae46bb6e-f788-445d-a12e-6471097b0ad7&docType=axure&pageId=749858ee607f4099ae7435fd1cd35ea9&parentId=739941cd-e6d3-4d27-8910-fbd99049d91f
 */
export default {
  name: "CertificationPopup",
  components: {
    CertificationUploadImage,
    AppButton,
    AppPopup,
  },
  data() {
    return {
      rules: {
        certPhone: [{ required: true, message: "请输入手机号", trigger: "blur" }],
        certIdCardNumber: [
          { required: true, message: "请输入身份证号", trigger: "blur" },
          { validator: regular("ID"), trigger: "blur" },
        ],
        certIdCardImgHead: [{ required: true, message: "上传身份证正面照", trigger: "change" }],
        certIdCardImgBadge: [{ required: true, message: "上传身份证正面照", trigger: "change" }],
        certName: [
          { required: true, message: "请输入真实姓名", trigger: "blur" },
          { validator: regular("realName"), trigger: "blur" },
        ],
      },
      formData: {
        certIdCardImgHead: "",
        certIdCardImgBadge: "",
        certIdCardNumber: "",
        certName: "",
        certPhone: "",
      },
      detail: {},
      certStatus: 0,
    };
  },
  computed: {
    ...mapGetters({
      // 有值，则说明是认证别人
      certificationData: "getCertificationData",
    }),
    show: {
      get() {
        return this.$store.getters.getCertificationPopup;
      },
      set(val) {
        this.$store.commit("SET_CERTIFICATION_POPUP", val);
      },
    },
    buttonText() {
      switch (this.certStatus) {
      case 3:
        return "重新提交";
      default:
        return "提交认证";
      }
    },
    /** 是否是审核中 */
    isAudit() {
      return this.certStatus === 1;
    },
    /** 是否是审核通过 */
    isPass() {
      return lawyerCertStatus(this.certStatus);
    },
    disabled() {
      return this.isAudit || this.isPass;
    },
    staffId() {
      return this.certificationData ? this.certificationData.id : this.$store.getters.userInfo.id;
    },
  },
  watch: {
    show: {
      handler(val) {
        if (val) {
          getCertInfoByStaffId(this.staffId).then(data => {
            this.detail = data;

            if (data) {
              this.certStatus = data.checkStatus;

              for (const key in this.formData) {
                this.formData[key] = data[key];
              }
            } else {
              this.certStatus = 0;
              // 赋值当前用户的手机号
              this.formData.certPhone = this.certificationData
                ? this.certificationData.userPhone
                : this.$store.getters.userInfo.userPhone;
            }
          });
        } else {
					// 在弹窗关闭时，重置表单
					// 注意在 form 不一定存在
					this.$refs.form?.resetFields?.();
					this.detail = {};
        }
      },
      immediate: true,
    },
  },
  methods: {
    handleSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const formData = {
            ...this.formData,
            staffId: this.staffId,
          };

          delete formData.certPhone;

          uploadCertInfo(formData).then(() => {
            this.$message.success("提交成功");
            this.$store.commit("CALL_CERTIFICATION_SUBMIT_CALL_BACK");
            this.close();
          });
        }
      });
    },
    close() {
      this.show = false;
    },
  },
};
</script>

<style lang="scss" scoped>
::v-deep .el-form-item__content {
	.el-input {
		width: 432px;
	}
}
</style>
