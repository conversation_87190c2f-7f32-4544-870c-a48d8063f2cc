import VersionPromptPopup from "./index.vue";
import Vue from "vue";
import { commonConfigKey } from "@/api/common";
import basics from "utils/basicsMethods";

let instance = "";
export const showVersionPromptPopup = () => {
  /* 创建组件*/
  if(instance) return instance;
  const VersionPromptPopupConstructor = Vue.extend(VersionPromptPopup);
  instance = new VersionPromptPopupConstructor();
  const el = instance.$mount();
  document.body.appendChild(el.$el);
  return instance;
};


/* 对比传入的版本号和本地版本号是否有差异  有就直接弹窗版本更新提示弹窗 版本一致返回true  不一致烦返回false*/
export const compareVersions = (version) => {
  const localVersion = process.env.VUE_APP_VERSION;
  console.log("远程版本号====", version);
  console.log("本地版本号====", localVersion);
  /* 这里怕打包出问题 因为版本号是通过打包注入进来的 判空一下 没有就直接返回一致*/
  if(basics.isNull(localVersion)) return true;
  /* 版本是否一致*/
  const versionAndLocalVersion = localVersion === version;
  if(!versionAndLocalVersion){
    showVersionPromptPopup();
  }
  return versionAndLocalVersion;
};



/* http请求 系统参数的版本号*/
export const getHttpSystemVersion = () => {
  let timer;
  const stop = () => {
    clearTimeout(timer);
  };
  const start = () => {
    /* 避免多次开启*/
    stop();
    timer = setTimeout(() => {
      commonConfigKey({
        paramName: "pc_version",
      }).then(data => {
        if(data){
          /* 如果弹出过版本更新弹出 就停止定时器了*/
          if (!compareVersions(data)){
            stop();
          }else{
            /* 没有就继续获取*/
            start();
          }
        }else{
          stop();
        }
      });
    }, 5000);
  };
  return{
    start,
    stop
  };
};
