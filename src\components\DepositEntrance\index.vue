<template>
  <div v-if="isRecharge">
    <slot />
  </div>
</template>

<script>
import { goodsListCatch } from "@/api/pay";

export default {
  name: "DepositEntrance",
  data() {
    return {
      product: []
    };
  },
  computed: {
    /* 判断是不是超级管理员*/
    isAdministrator() {
      return this.$store.getters.isAdministrator;
    },
    // 主账号有充值，子账号没有
    isRecharge() {
      // 如果没有商品的话，隐藏入口
      return this.isAdministrator && this.product.length > 0;
    }
  },
  mounted() {

    // 查看商品是否存在
    goodsListCatch().then(r => {
      this.product = r;
    });
  }
};
</script>

<style scoped lang="scss">

</style>
