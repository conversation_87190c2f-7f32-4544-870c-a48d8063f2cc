import { uplodImages } from "@/api/common";

export default {
  props: {
    value: {
      type: String,
      default: "",
      desc: "获取值",
    },
    width: {
      type: String,
      default: "100px",
      desc: "宽度",
    },
    height: {
      type: String,
      default: "100px",
      desc: "高度",
    },
    multiple: {
      type: Boolean,
      default: false,
      desc: "是否多选",
    },
    accept: {
      type: String,
      default: "image/*",
      desc: "文件类型",
    },
    limit: {
      type: Number,
      default: 1,
      desc: "限制传输数量",
    },
    formMaxSizeM: {
      default: 10,
      type: Number,
      desc: "限制图片大小(单位M)",
    },
    formMaxSizekb: {
      default: 100,
      type: Number,
      desc: "限制图片大小(单位kb)",
    },
    isSizeKb: {
      default: false,
      type: Boolean,
      desc: "限制图片大小(单位kb)",
    },
    showList: {
      default: true,
      type: Boolean,
    },
  },
  data() {
    return {
      uplodImages,
      previewList: [],
      showUpload: true,
      loading: "",
    };
  },
  computed: {
    //  当传来的值变化时，跟着变化
    fileList() {
      let _data = [];
      if (this.value) _data = this.value.split(",");
      return _data;
    },
    imageSize() {
      if (!this.showList) {
        return {};
      }
      return { width: this.width, height: this.height };
    },
  },
  methods: {
    //   更改值
    emitInput(val) {
      this.$emit("input", val);
    },
    // 文件上传成功
    handleSuccess(response, file) {
      let _dataStr = "";
      if (!response.code) {
        if (this.fileList.length > 0) {
          this.fileList.push(response.data);
          _dataStr = this.fileList.toString();
        } else {
          _dataStr = response.data;
        }
        this.emitInput(_dataStr);

        setTimeout(rel => {
          this.loading.close();
          this.$message.success("上传文件成功！");
        }, 1500);
      } else {
        this.loading.close();
        this.$message.error(response.message);
      }
      this.$refs["upload"].clearFiles();
    },
    // 文件上传失败
    handleError(err, file, fileList) {
      this.loading.close();
      this.$message.error({
        type: "info",
        message: err.message || "上传文件失败",
      });
    },
    // 文件移除
    handleRemove(file, index) {
      this.fileList.splice(index, 1);
      const _dataStr = this.fileList.toString();
      this.emitInput(_dataStr);
    },
    // 验证文件的大小
    verification(file) {
      if (!this.isSizeKb && file.size / 1024 / 1024 > this.formMaxSizeM) {
        this.$message({
          message: `上传文件大小不能超过${this.formMaxSizeM}M!`,
          type: "warning",
        });
        this.loading.close();
        return false;
      } else if (this.isSizeKb && file.size / 1024 > this.formMaxSizekb) {
        this.$message({
          message: `上传文件大小不能超过${this.formMaxSizekb}Kb!`,
          type: "warning",
        });
        this.loading.close();
        return false;
      } else if (/\s/.test(file.name)) {
        this.$message({
          message: "文件不能有空格！",
          type: "error",
        });
        this.loading.close();
        return false;
      }

      return true;
    },
    // 压缩图片,大于2M进行压缩
    compress(file) {
      // 注意这里需要用Promise函数来阻止图片没有压缩好,就直接将文件上传
      // return new Promise(async(resolve, reject) => {
      //   const isJPG = file.type === 'image/jpeg'
      //   const isPng = file.type === 'image/png'
      //   // 每个限制返回错误信息   都需要用到reject()
      //   if (!isJPG && !isPng) {
      //     this.$message.warning('上传图片只能是 JPG 或者 PNG 格式!')
      //     return reject(false) // 注意这里需要用reject来返回错误的信息,防止图片自动上传
      //   }
      //   const compress = 2048 // 假设图片限制不能大于2M
      //   const sizeOver = file.size / 1024 > compress // 文件大小 是否大于指定大小
      //   if (sizeOver && compress) { // 大于2M进行压缩
      //     const res = await compressAccurately(file, {
      //       size: compress, // 需要压缩的大小
      //       accuracy: 0.9, // 精度 0.8-0.99之间 默认值0.95
      //       type: 'image/jpeg',
      //       width: 105,
      //       height: 105,
      //       orientation: 2,
      //       scale: 0.5
      //     })
      //     file = res // 把得到的新的图片文件赋值给原文件,然后进行上传
      //   }
      //   resolve(true) // 通过resolve将Promise函数返回成功回调,进行后面操作
      // })
    },

    // 上传前的钩子
    beforeUpload(file) {
      console.log(111111111111111);
      this.loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      // 压缩图片

      return this.verification(file);

      // return new Promise((resolve, reject) => {
      //      resolve(true)
      //      reject(false)
      // })
    },
    // 预览图片
    handlePreview(file, index) {
      this.$refs.previewImage[index].showViewer = true;
    },
  },
};
