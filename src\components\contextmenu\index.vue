<template>
  <div :style="style" v-show="menuShowState" class="contextmenu">
    <drop-down-menu @contextmenu.prevent.native @menuClick="menuClick" :menu="menu" />
  </div>
</template>

<script>
import DropDownMenu from "components/dropDownMenu/index.vue";

export default {
  name: "Contextmenu",
  components: { DropDownMenu },
  data() {
    return {
      /* 菜单列表*/
      menu: [],
      /* 菜单位置*/
      style: {
        top: 0,
        left: 0
      },
      /* 菜单显示状态*/
      menuShowState: false,
      /* 菜单点击*/
      onMenuClick: () => {},
      /* 菜单额外参数*/
      params: {}
    };
  },
  watch: {
    menuShowState: {
      immediate: true,
      handler() {
        if (this.menuShowState) {
          document.body.addEventListener("click", this.closeMenu);
          document.body.addEventListener("contextmenu", this.closeMenu);
        } else {
          document.body.removeEventListener("click", this.closeMenu);
          document.body.addEventListener("contextmenu", this.closeMenu);
        }
      }
    }
  },
  methods: {
    /* 判断是不是函数*/
    isFunction(fn) {
      return Object.prototype.toString.call(fn) === "[object Function]";
    },
    openMenu({ x, y, menu, onMenuClick, params }) {
      this.params = params || {};
      if (this.isFunction(menu)) this.menu = menu(this.params);
      else this.menu = menu;
      this.onMenuClick = onMenuClick;
      this.style = {
        top: y + 15 + "px",
        left: x + "px"
      };
      this.menuShowState = true;
    },
    closeMenu() {
      this.menuShowState = false;
    },
    menuClick(data) {
      this.onMenuClick(data, this.params);
    }
  }
};
</script>

<style scoped lang="scss">
.contextmenu{
  position: fixed;
  z-index: 999;
  top: 0;
  left: 0;
}
</style>
