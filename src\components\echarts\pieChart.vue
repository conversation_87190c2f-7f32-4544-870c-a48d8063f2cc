<template>
  <div :id="id" :class="className" :style="{height:height,width:width}" />
</template>

<script>
import echarts from "echarts";
import resize from "@/minixs/resize";
export default {
  name: "<PERSON><PERSON><PERSON>",
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "pieChart"
    },
    id: {
      type: String,
      default: "pieChart"
    },
    name: {
      type: String
    },
    width: {
      type: String,
      default: "100%"
    },
    height: {
      type: String,
      default: "300px"
    },
    radius: {
      type: [String, Array],
      default: () => ["30%", "50%"]
    },
    chartData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null,
      legendData: []
    };
  },
  watch: {
    chartData: {
      handler(val) {
        this.initChart();
      },
      deep: true
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart();
    });
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.chart = echarts.init(document.getElementById(this.id));
      const _this = this;
      const option = {
        legend: {
          bottom: 20
        },
        tooltip: {
          trigger: "item",
          align: "left",
          // formatter: '{a} <br/>{b} : {c} ({d}%)'
          formatter: function(params) {
            const seriesName = params.seriesName;
            const name = params.name;

            let str = "";
            params.data.othersShow && params.data.othersShow.map(res => {
              str += res.name + "：" + res.value + "<br/>";
            });

            return `${seriesName}` + "<br/>" + `${name}：` + params.value + " %" + "<br/>" + str;
          }
        },
        series: [
          {
            name: this.name,
            type: "pie",
            // top: '-10%',
            radius: this.radius,
            width: 400,
            left: "center",
            itemStyle: {
              borderColor: "#fff",
              borderWidth: 1
            },
            label: {
              formatter: "{name|{b}}\n{time|{c} %}",
              minMargin: 5,
              lineHeight: 15,
              rich: {
                time: {
                  fontSize: 10,
                  color: "#999"
                }
              }
            },
            labelLine: {
              length: 60,
              length2: 10
            },
            labelLayout: function(params) {
              var isLeft = params.labelRect.x < _this.chart.getWidth() / 2;
              var points = params.labelLinePoints;
              // Update the end point.
              points[2][0] = isLeft
                ? params.labelRect.x
                : params.labelRect.x + params.labelRect.width;

              return {
                labelLinePoints: points
              };
            },
            data: this.chartData
          }
        ],
        color: ["#58A3F7", "#FFC542", "#FF7474", "#50B5FF", "#8167F5", "#6e7074", "#546570", "#c4ccd3"]
      };

      this.chart.setOption(option);
    }
  }
};
</script>

<style scoped>

</style>
