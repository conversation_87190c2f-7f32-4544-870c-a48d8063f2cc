<template>
	<div :class="[tagClassName]" :style="tagStyle">
		<div ref="select" v-show="type === 'plus'" class="relative items-center">
			<div
				ref="popup"
				v-if="showSelect"
				class="z-50 bg-[#FFFFFF] w-[568px] fixed box-border [box-shadow:0px_2px_8px_0px_rgba(0,0,0,0.12)] rounded-tl-[2px] rounded-br-[2px] rounded-tr-[2px] rounded-bl-[2px] opacity-100"
			>

				<div class="flex   px-[12px] pb-[12px]" v-for="(i,index) in tagsList" :key="index">
          <p class="pr-[16px] text-[14px] text-[#666666]  pt-[12px]">{{i.label}}</p>
          <div class="grid grid-cols-4 flex-1 ">
            <div v-for="item in i.list" :key="item.value" class="pt-[12px]">
              <!-- 将值绑定上 -->
              <app-checkbox
                delay-click
                :value="selectTag.some(select => select.value === item.value)"
                @change="handleSelect(item)"
                :label="item.label"
              />
            </div>
          </div>
        </div>
			</div>
			<div @click="handleClick" class="flex cursor-pointer items-center">
				<i class="cursor-pointer iconfont icon-xianxingtubiao-16 text-[16px] block" />
			</div>
		</div>
		<div v-if="type === 'info'" class="flex items-center">
			<div class="leading-[23px] text-[#FFFFFF]">{{ data.label }}</div>
      <img
        v-if="closeIcon"
        alt=""
        class="absolute w-[16px] h-[16px] -right-[5px] -top-[5px] ml-[4px] cursor-pointer iconfont icon-shanchu text-[12px] block"
        src="@/assets/images/123.png"
        v-delay-click-directive:click="handleDelete"
      />
		</div>
	</div>
</template>

<script>
import AppCheckbox from "components/appCheckbox/index.vue";
import { delayClickDirective } from "utils/delayClick";

const baseStyle =
  "rounded-[4px] opacity-100 text-[12px] relative";

export default {
  name: "ClueDetailTextareaTagItem",
  components: { AppCheckbox },
  directives: { ...delayClickDirective() },
  props: {
    type: {
      type: String,
      default: "info",
      validator: value => {
        return ["plus", "info"].includes(value);
      },
    },
    tagsList: {
      type: Array,
      default: () => [],
    },
    data: {
      type: Object,
      default: () => ({}),
    },
    selectTag: {
      type: Array,
      default: () => [],
    },
    /** 样式顺序 */
    styleOrder: {
      type: Number,
      default: 0,
    },
    closeIcon: {
      type: Boolean,
      default: true
    },
    /* 不可编辑*/
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      /** 弹出选择弹窗 */
      showSelect: false,
    };
  },
  computed: {
    tagClassName() {
      if (this.type === "plus") return baseStyle + "  text-[#666666] w-[24px] h-[24px] flex items-center justify-center";

      const notPlus = baseStyle + " py-[4px] px-[10px]";


      switch (this.data.value){
      case "yjwx":
        return notPlus + " bg-[#E6F5EC] text-[#666666]";
      case "gthwyx":
        return notPlus + " bg-[#FCE0D7] text-[#666666]";
      case "gthyxg":
        return notPlus + " bg-[#FAEDDC] text-[#666666]";
      }

      return notPlus + " bg-[#E6F5EC] text-[#666666]";
    },
    tagStyle(){
      const supplementDesc = this.data.supplementDesc || {};
      if(this.basics.isObj(supplementDesc)){
        return supplementDesc;
      }
      return  {};
    }
  },
  mounted() {
    // 点击select之外的地方，关闭select
    document.addEventListener("click", e => {
      if (this.showSelect && !this.$refs.select?.contains(e.target)) {
        this.showSelect = false;
      }
    });
  },
  methods: {
    /** 当为添加时，点击按钮 */
    handleClick() {
      this.showSelect = !this.showSelect;
      this.$nextTick(() => {
        if(!this.$refs.popup) return;
        // 获取 select 的位置
        const { top, left } = this.$refs.select.getBoundingClientRect();
        let setTop = top + 35 + "px";
        let setLeft = left + "px";
        /* 判断有没有到窗口边界值*/
        const { innerHeight, innerWidth } = window;

        if (innerHeight - top < 200) {
          setTop = top - 200 + "px";
        }
        if(innerWidth - left < 568){
          setLeft = left - 568 + "px";
        }
        // 设置弹窗位置
        this.$refs.popup.style.top = setTop;
        this.$refs.popup.style.left = setLeft;
      });
    },
    /** 当为添加时，选择内容 */
    handleSelect(value) {
      if (this.type === "plus") {
        this.$emit("select", value);
      }
    },
    /** 点击删除按钮 */
    handleDelete() {
      this.$emit("select", this.data);
    },
  },
};
</script>

<style scoped lang="scss"></style>
