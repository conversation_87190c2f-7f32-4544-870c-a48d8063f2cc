<template>
  <div v-if="init" ref="echarts"  />
</template>

<script>
import echarts from "echarts";
export default {
  name: "AppEcharts",
  data() {
    return {
      chart: {},
      init:false
    };
  },
  mounted() {
    window.addEventListener("resize", this.addResize);
  },
  destroyed() {
    window.removeEventListener("resize", this.addResize);
  },
  methods: {
    addResize(){
      this.chart && this.chart.resize();
    },
    initEcharts(options){
      this.init = false
      this.$nextTick(() => {
        this.init = true
        this.$nextTick(()=>{
          const data = this.basics.isFunction(options) ? options(echarts) : options;
          this.chart = echarts.init(this.$refs.echarts);
          this.chart.setOption(data);
        })
      });
    },
  }
};
</script>

<style scoped lang="scss">

</style>
