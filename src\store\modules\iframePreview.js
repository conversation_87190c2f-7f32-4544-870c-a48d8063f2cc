

export default {
  state: {
    iframePreviewInfo: {},
    iframePreviewVisible: false
  },
  getters: {
    getIframePreviewInfo: state => state.iframePreviewInfo,
    getIframePreviewVisible: state => state.iframePreviewVisible
  },
  mutations: {
    SET_IFRAME_PREVIEW_INFO(state, data) {
      state.iframePreviewInfo = data;
    },
    SET_IFRAME_PREVIEW_VISIBLE(state, data) {
      state.iframePreviewVisible = data;
    }


  },
  actions: {
    setIframePreviewVisible({ commit }, data = {}) {
      commit("SET_IFRAME_PREVIEW_INFO", data);
      setTimeout(() => {
        commit("SET_IFRAME_PREVIEW_VISIBLE", true);
      }, 0);
    }
  }
};
