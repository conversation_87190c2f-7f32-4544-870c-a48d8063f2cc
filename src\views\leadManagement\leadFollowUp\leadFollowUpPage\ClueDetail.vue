<template>
	<app-drawer size="720px" :visible.sync="drawer" title="线索详情">
		<div class="mt-[27px] px-[24px]">
			<clue-detail-title title="基本信息" />
			<div class="mt-[24px]">
				<clue-detail-descriptions :data="detailData" />
			</div>
			<div class="text-[14px] text-[#666666]">
				<p>描述内容:</p>
				<div
					class="bg-[#F5F5F7] rounded-tl-[4px] rounded-br-[4px] rounded-tr-[4px] rounded-bl-[4px] p-[12px] mt-[8px]"
				>
					<p class="text-[#666666] leading-[16px]">
						{{ detailData.clueInfo }}
					</p>
				</div>
			</div>
			<div>
        <div class="mt-[20px] lead_labels">
          <p class="text-[14px] text-[#666666] mb-[8px]">跟进标签：</p>
          <clue-detail-textarea-tag
            disabled
            :tags-list="tagsList"
            :flat-list="flatTagsList"
            :label-values="detailData.lcLawClueLabel"
          />
        </div>
				<div class="flex justify-between mt-[24px]">
					<clue-detail-title title="跟进情况" />
					<app-button
						v-if="detailData.followStatus === 1"
						@click="handleEndFollowUp"
						style-type="border"
						type="danger"
						size="medium"
						>结束跟进
					</app-button>
				</div>
				<div class="mt-[16px]">
					<clue-detail-textarea :input.sync="submitContent.followInfo" :tags-list="tagsList" />
				</div>
			</div>
			<div class="flex justify-end items-center mt-[10px]">
				<div class="flex">
					<app-button @click="handleBack" type="info">返回</app-button>
					<div class="ml-[16px]">
						<app-button delay-click @click="handleSubmit">提交</app-button>
					</div>
				</div>
			</div>
			<div class="mt-[48px] flex items-center justify-between">
				<clue-detail-title title="跟进记录" />
			</div>
			<div class="mt-[12px]">
				<ul>
					<li
						v-for="item in followUpRecord"
						:key="item.id"
						class="flex justify-between [&:not(:first-child)]:mt-[24px]"
					>
						<p v-if="Number(item.operationType) !== 3" class="text-[14px] text-[#666666]">
							【{{ item.userName }}】<span v-if="item.info">更新跟进记录内容 “{{ item.info }}”</span
							><span v-if="item.clueTypeLabel"
								>{{ Number(item.operationType) === 1 ? '新增' : '删除' }}标签
								{{ item.clueTypeLabel }}</span
							>
						</p>
            <p v-else class="text-[14px] text-[#666666]">{{ item.info }}</p>
						<p class="text-[12px] text-[#999999] flex-shrink-0">{{ item.followTime }}</p>
					</li>
				</ul>
				<div class="pagination">
					<xyPagination
						:current.sync="query.currentPage"
						:page-size.sync="query.pageSize"
						:total="total"
						@handleCurrentChange="handleCurrentChange"
						@handleSizeChange="handleSizeChange"
					/>
				</div>
			</div>
		</div>
	</app-drawer>
</template>

<script>
import ClueDetailTitle from "views/leadManagement/leadFollowUp/leadFollowUpPage/ClueDetailTitle.vue";
import AppButton from "components/appButton/index.vue";
import ClueDetailTextarea from "views/leadManagement/leadFollowUp/leadFollowUpPage/ClueDetailTextarea.vue";
import ClueDetailDescriptions from "views/leadManagement/leadFollowUp/leadFollowUpPage/ClueDetailDescriptions.vue";
import AppDrawer from "components/AppDrawer/index.vue";
import { getClueFollowDetailList, saveClueFollow } from "@/api/clues";
import { dataDetailLabelList, dataDetailList } from "@/api/common";
import xyPagination from "components/xy-pagination/index.vue";
import { randomWord } from "utils/basicsMethods";
import ClueDetailTextareaTag from "views/leadManagement/leadFollowUp/leadFollowUpPage/ClueDetailTextareaTag.vue";

export default {
  name: "ClueDetail",
  components: {
    ClueDetailTextareaTag,
    xyPagination,
    AppDrawer,
    ClueDetailDescriptions,
    ClueDetailTextarea,
    AppButton,
    ClueDetailTitle,
  },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    /** 详情 */
    detailData: {
      type: Object,
      required: true,
    },
  },
  data() {
    return {
      /** 提交的内容 */
      submitContent: {
        /** 跟进内容 */
        followInfo: "",
      },
      /** 跟进记录 */
      followUpRecord: [],
      tagsList: [],
      flatTagsList: [],
      query: {
        currentPage: 1,
        pageSize: 10,
      },
      total: 0,
    };
  },
  computed: {
    drawer: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  watch: {
    drawer: {
      handler(val) {
        if (this.detailData.serverId && val) {
          this.submitContent.followInfo = this.detailData.lastFollowInfo;
          this.handleTextareaChange();
          this.getTagsList();
        } else {
          // 关闭的时候清空数据
          this.submitContent = this.$options.data().submitContent;
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    /** 点击提交 */
    handleSubmit() {
      /* 判空*/
      if (!this.submitContent.followInfo) {
        this.$message.warning("请输入跟进内容");
        return;
      }
      saveClueFollow({
        ...this.submitContent,
        serverId: this.detailData.serverId,
      }).then(() => {
        this.$message.success("提交成功");
        this.drawer = false;
        this.$store.dispatch("setIsTableRefresh");
      });
    },
    /** 跟进记录 */
    handleTextareaChange() {
      getClueFollowDetailList({
        ...this.query,
        serverId: this.detailData.serverId,
      }).then(res => {
        this.followUpRecord = res.records.map(item => {
          return {
            ...item,
            id: randomWord(),
          };
        });
        this.total = res.total;
      });
    },
    /** 点击结束跟进 */
    handleEndFollowUp() {
      this.$emit("handleEndFollowUp", this.detailData);
    },
    /** 后端返回的标签数据转换 */
    handleTagsData(data) {
      if (!data || data.length === 0) return "";

      const labelValues = data.split(",");

      const values = labelValues.map(item => {
        return this.flatTagsList.find(tag => tag.value === item);
      });

      // 过滤空值，合并字符串
      return values
        .filter(item => item)
        .map(item => item.label)
        .join("，");
    },
    /** 点击返回 */
    handleBack() {
      this.drawer = false;
    },
    /** 获取标签的值 */
    getTagsList() {
      dataDetailLabelList({
        groupCode: "LC_LAW_CLUE_LABLE",
      }).then(({ data = [], flatData = [] }) => {
        this.tagsList = data;
        this.flatTagsList = flatData;
      });
    },
    /** 分页 */
    handleCurrentChange(val) {
      this.query.currentPage = val;
      this.handleTextareaChange();
    },
    handleSizeChange(val) {
      this.query.pageSize = val;
      this.handleTextareaChange();
    },
  },
};
</script>

<style scoped lang="scss">
.lead_labels{
  ::v-deep{
    .clue-detail-textarea-tag-item-c{
      padding-top: 8px;
      flex-wrap: wrap;
    }
    .clue-detail-textarea-tag-item{
      margin-bottom: 8px;
    }
  }
}
</style>
