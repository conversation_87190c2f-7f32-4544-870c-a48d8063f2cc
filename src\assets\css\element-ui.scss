.el-header {
  padding: 0;
}

.el-button + .el-button {
  margin-left: 16px;
}

.el-popover {
  border-radius: 8px;
  box-shadow: 0px 2px 12px 0px rgba(0, 0, 0, 0.12);

  &.dark-popover {
    color: #fff;
    background: #4C4C4C;
    border-color: #4C4C4C;

    &.el-popper[x-placement^="top"] .popper__arrow::after {
      border-top-color: #4C4C4C;
      bottom: 0px;
    }

    &.el-popper[x-placement^="bottom"] .popper__arrow::after {
      border-bottom-color: #4C4C4C;
    }

    &.el-popper[x-placement^="left"] .popper__arrow::after {
      border-left-color: #4C4C4C;
    }

    &.el-popper[x-placement^="right"] .popper__arrow::after {
      border-right-color: #4C4C4C;
    }
  }

  &.user-info-popover {
    font-size: $font-size-mini;

    .head {
      padding-bottom: $distance-small;
      @extend .flex;
      border-bottom: 1px solid $weak-color;
    }

    .content {
      padding: $distance-large $distance-large 0;

      li {
        margin-bottom: $distance-normal;
        @extend .flex-center;

        p {
          color: $text-color-main;
          margin-left: $distance-small;
        }
      }
    }
  }

  &.menu-popover {
    font-size: $font-size-mini;

    .head {
      padding-bottom: $distance-small;
      @extend .flex;
      border-bottom: 1px solid $weak-color;
    }

    .content {
      padding: $distance-large $distance-normal 0;
      @extend .flex;
      flex-wrap: wrap;

      li {
        margin-bottom: $distance-large;
        width: calc(100% / 3);
        text-align: center;
        cursor: pointer;

        &:hover {
          color: $primary-color;
        }
      }
    }
  }
}

.el-button {
  &.el-button--mini {
    padding: 7px;
    border-radius: 2px;
  }
}

.el-form {
  // 表格搜索表单设置
  &.table-search-form {
    display: flex;
    flex-wrap: wrap;

    .el-button--mini {
      height: 32px;
      //line-height: 32px;
      padding: 8px 16px;
    }

    .btn-box {
      button {
        margin-left: $distance-normal;
      }
    }
  }

  .el-form-item__content {
    font-size: 0;
  }

  &.el-form--inline .el-form-item {
    margin-right: 0;
  }

  &.dialog-form {
    padding: $distance-large 30px;

    .el-select,
    .el-cascader,
    .el-textarea,
    .el-input-number {
      width: 100%;
    }
  }
}

.el-pagination {
  display: flex;
  justify-content: flex-end;
}

.el-input {
  .el-input__inner {
    border-radius: 2px;
  }
}

.el-textarea {
  .el-textarea__inner {
    font-family: Microsoft YaHei;
  }
}

// .el-table__body-wrapper{
// 	max-height: 700px;
// 	overflow-y: auto;
// }
.el-table {
  .el-table__header {
    tr th {
      color: $text-color-main;
      font-weight: 500;
    }

    th > .cell {
      padding-left: 24px;
    }
  }

  // 解决表格错位
  th.gutter {
    display: table-cell !important;

    .cell span {
      position: relative; // 用于表头图标定位使用
    }
  }

  .el-table__row {
    td {
      box-sizing: border-box;
      padding: 12px 0;

      .cell {
        line-height: 23px;
        padding-left: 24px;

        .el-button + span {
          margin-left: 15px;
        }

        .danger-btn {
          background: transparent;
          border: none;
          color: $error-color;
          padding: 4px 0 !important;
        }

        .warning-btn {
          color: $warning-color;
        }

        .success-btn {
          color: $success-color;
        }

        .primary-btn {
          color: $primary-color;
        }

        .infos-btn {
          color: $infos-color;
        }

        .gray-btn {
          color: #909399;
        }

        .el-button.el-button--text.el-button--small {
          padding: 4px 0 !important;
        }
      }
    }
  }
}

.el-menu {
  border: none;
  height: auto;
  width: 225px;
  transition: all 0.28s;

  &.el-menu--collapse {
    width: auto;
  }

  .el-submenu {
    .el-menu-item,
    .el-submenu__title {
      height: 50px;
      line-height: 50px;

      &:hover {
        background-color: #EDF3F7 !important;
      }
    }


    .el-menu-item {
      // background-color: #383d49 !important;
      transition: all 0.3s ease-in-out;

      &:hover {
        background-color: #EDF3F7 !important;
      }

      &:not(.is-active) {
        color: #333333 !important;
      }

    }

    .el-menu {

      .el-submenu .el-submenu__title {
        // background-color: #383d49!important;

        &:hover {
          background-color: #EDF3F7 !important;
        }

      }
    }

    &.is-active {
      .el-menu-item, .el-submenu__title {
        &:hover {
          background-color: #EDF3F7 !important;
        }
      }
    }
  }


  .el-menu-item {
    height: 50px;
    line-height: 50px;

    &:hover {
      background-color: #EDF3F7 !important;
    }
  }



  .el-menu-item.is-active {
    background-color: #EDF3F7 !important;
  }
}

.el-menu--popup {
  border-radius: 6px !important;
}

.el-dialog {
  //position: absolute;
  //top: 50%;
  //left: 50%;
  //transform: translate(-50%, -50%);
  z-index: 9999;

  .el-dialog__header {
    padding: $distance-normal;
    background-color: $menu-group-bg-color;
    border-bottom: 1px solid $weak-color;

    .el-dialog__title {
      font-size: $font-size-normal;
    }
  }

  .el-dialog__body {
    padding: $distance-normal $distance-large;
    min-height: 300px;
  }
}

.el-tree {
  .el-tree-node__content {
    height: 36px;
  }
}

.el-input-number {
  &.is-controls-right .el-input__inner {
    text-align: left !important;
  }
}

.el-avatar {
  background: transparent;

  & > img {
    margin: 0 auto;
  }
}

.el-image-viewer__wrapper {
  .el-icon-circle-close {
    color: #fff;
  }
}

.el-tabs--border-card {
  height: 100%;
  //.el-tabs__item.is-top.is-active {
  //  background: $primary-color;
  //  color: #fff;
  //  &:hover{
  //    color: #fff;
  //  }
  //}
}

.el-popover {
  &.table-head-popover.el-popover--plain {
    padding: 10px 15px;
    font-size: $font-size-mini;
  }
}

.form-wrap {

  .el-select,
  .el-input,
  .el-cascader,
  .el-textarea,
  .el-input-number {
    width: 90%;
  }

  .el-cascader /deep/ .el-input {
    width: 90%;
  }

  .el-textarea /deep/ textarea {
    min-height: 150px;
    resize: none;
  }


}

.el-page-header {
  .el-page-header__title {
    font-size: 13px;
  }

  .el-page-header__content {
    font-size: 16px;
    color: #42b983;
  }
}


/* 设置滚动条的样式 */
html {
  * {
    //scrollbar-width: none;
  }
}

.block-btns {
  button {
    display: block;
  }
}

// 修改message的样式
.el-message {
  &--success {
    background: #E6F5EC;
    border-color: #B7E7D5 !important;

    .el-message__content {
      color: #333333 !important;
    }
  }

  &--error {
    background: #FCF1ED;
    border-color: #FBCFC3 !important;

    .el-message__content {
      color: #333333 !important;
    }
  }
}


// 设置主题色
.el-button--success {
  background-color: $success-color;
  border-color: $success-color;
}

.el-button--warning {
  background-color: $warning-color;
  border-color: $warning-color;
}

.el-button--danger {
  background-color: $error-color;
  border-color: $error-color;
}

.el-image-viewer__next, .el-image-viewer__prev {
  background-color: #ffffff;
  color: #000000;
}

.h32 {
  height: 32px !important;
  line-height: 32px !important;
}

.el-pagination {

  .el-input__inner {
    border-color: #eee;

    &:focus, &:hover {
      border-color: $primary-color;
    }
  }

  .el-pagination__sizes, .el-pagination__total {
    @extend .h32;
    margin-right: 20px;
  }

  .el-pagination__jump {
    @extend .h32;
  }

  .el-select .el-input .el-input__inner, .el-pager li, .btn-prev, .btn-next {
    @extend .h32;
  }

  .btn-prev, .btn-next {
    padding: 0;
  }

  .btn-next {
    margin-left: 8px;
  }

  .el-pager li, .btn-prev, .btn-next {
    border: 1px solid #EEEEEE !important;
    box-sizing: border-box;
    min-width: 32px;
    border-radius: 2px;
    font-weight: normal;

    &.active, &:hover {
      border-color: $primary-color !important;
    }
  }

  &.el-pagination button:disabled:hover {
    border-color: #eeeeee !important;
  }

  .el-pager li {
    margin-left: 8px;
  }

}

.el-tabs__nav-wrap::after {
  height: 1px;
}

.el-table {
  font-size: 14px !important;
}

.el-select-dropdown__item.selected {
  font-weight: normal;
}

.el-select-dropdown__item.hover {
  background: #EDF3F7;
}

.el-textarea__inner {
  padding: 12px;
}

.el-tabs__item.is-active {
  font-weight: 500;
}

.el-tabs__header {
  margin: 0;
}

.el-tabs__nav-scroll {
  margin-left: 24px;
}

.log-out-popover {
  padding: 0;
}

.el-switch {
  .el-switch__core {
    border-color: #E5E5E5 !important;
    background-color: #FDFDFD !important;

    &:after {
      box-shadow: -2px 4px 4px 0px rgba(0, 0, 0, 0.1), 0px 4px 23px 0px rgba(0, 0, 0, 0.08), 0px 0px 4px 0px rgba(0, 0, 0, 0.2);
    }
  }

  &.is-checked .el-switch__core {
    border-color: $primary-color !important;
    background-color: $primary-color !important;

    &:after {
      box-shadow: none;
    }
  }
}

.saas-server-notification {
  display: block;
  padding: 16px;
  width: 236px;
  border: none;

  &.right {
    right: 24px;
  }

  .el-notification__group, .el-notification__content {
    margin: 0;
  }

}

.el-radio__input.is-checked .el-radio__inner, .el-radio__input.is-focus .el-radio__inner {
  background: #fff;
}

.el-radio__inner::after {
  width: 8px;
  height: 8px;
  border-radius: 100%;
  background-color: #3887f5;
}

.fade-enter-active, .fade-leave-active {
  transition: opacity .5s;
}

.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */
{
  opacity: 0;
}


.el-tooltip__popper.is-dark{
  max-width: 30%;
  font-size: 14px;
  line-height:1.4;
}
//caseSourcePush 文件
.case-switch-tip{
  background: rgba(0,0,0,0.7) !important;
  padding: 16px;
}
