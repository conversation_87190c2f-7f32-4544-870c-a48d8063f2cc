<template>
  <div class="px-[24px] pt-[24px] flex justify-between">
    <div class=" flex items-center">
      <p class="w-[4px] h-[16px] bg-[#3887F5]" />
      <p :class="{
        'font-bold text-[16px] text-[#333333]':type === 'large',
        'font-medium text-[14px] text-[#333333]':type === 'medium',
        'font-normal text-[12px] text-[#333333]':type === 'small'}" class="pl-[8px]"><slot /></p>
    </div>
    <div>
      <slot name="append" />
    </div>
  </div>
</template>

<script>
export default {
  name: "WorkbenchCardTitle",
  props: {
    type: {
      type: String,
      default: "large",
      validator: (value) => {
        /* 标题类型 大标题 副标题 */
        /* large 大标题 medium 副标题 small 小标题 */
        return ["large", "medium", "small"].includes(value);
      }
    }
  }
};
</script>

<style scoped lang="scss">

</style>
