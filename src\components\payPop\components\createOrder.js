import { goodsListCreateOrder, goodsListPay } from "@/api/pay";

export const createOrderFun =  (data = {}) => {

  return  new Promise(resolve => {
    // 创建订单
    goodsListCreateOrder(data).then(r => {
      // 支付
      goodsListPay({
        orderId: r.orderId,
        payType: data.payType
      }).then(s => {
        console.log(s, "下单返回的数据");
        return resolve(s);
      });
    });

  });
};
