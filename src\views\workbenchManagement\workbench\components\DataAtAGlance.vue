<template>
  <div class="bg-[#FFFFFF] rounded-[2px] mt-[16px]">
    <workbench-card-title>
      线索统计
      <template #append>
        <p class="text-[12px] text-[#999999]">仅展示律客云平台今日的实时数据</p>
      </template>
    </workbench-card-title>
    <div class="pt-[16px] px-[24px] pb-[16px]">
      <div class="flex h-[103px] bg-[linear-gradient(_180deg,_#EBF2FC_0%,_rgba(235,242,252,0)_100%)] rounded-[4px]">
        <div v-for="i in list" :key="i.value" class="flex-1 glance-item text-center flex flex-column items-center justify-center">
          <p class="font-bold text-[24px] text-[#333333]">{{data[i.value]||'0'}}<span
            class="text-[12px] text-[#999999] font-normal">条</span></p>
          <p class="text-[14px] text-[#666666] !pt-[4px]">{{i.label}}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import WorkbenchCardTitle from "views/workbenchManagement/workbench/components/CardTitle.vue";
import { lcLawFirmsInstitutionGetCaseData } from "@/api/workbenchManagement.js";

export default {
  name: "DataAtAGlance",
  components: { WorkbenchCardTitle },
  data() {
    return {
      list: [{
        label: "当前跟进中",
        value: "gjzCount"
      }, {
        label: "今日已抢案源",
        value: "dayCaseCount"
      }, {
        label: "今日已抢咨询",
        value: "dayQaMessegeCount"
      }, {
        label: "总计已抢案源",
        value: "caseCount"
      }, {
        label: "总计已抢咨询",
        value: "qaMessegeCount"
      }],
      data: {}
    };
  },
  mounted() {
    lcLawFirmsInstitutionGetCaseData().then(res => {
      this.data = res;
    });
  }
};
</script>

<style scoped lang="scss">
.glance-item {
  position: relative;

  &:after {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    content: " ";
    border-right: 1px dashed #DDDDDD;
    height: 60px;
  }

  &:last-of-type {
    &:after {
      display: none;
    }
  }
}
</style>
