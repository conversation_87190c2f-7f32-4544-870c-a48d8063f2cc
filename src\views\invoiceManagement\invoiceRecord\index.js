import global from "@/utils/constant";
import { datePickerOptionsExpand } from "utils/datePickerOptions.js";
import { dataDetailList } from "@/api/common";
import { priceNumber } from "@/utils/toolMethods";
import { institutionInvoiceDetailByInvoiceId } from "@/api/invoice";
import axios from "axios";

/** 从URL获取文件名 */
function getFileNameFromUrl(url) {
  if (!url) return "";
  const urlParts = url.split("/");
  let fileName = urlParts[urlParts.length - 1];

  // 处理可能存在的查询参数
  if (fileName.includes("?")) {
    fileName = fileName.split("?")[0];
  }

  return decodeURIComponent(fileName);
}

export function handleDownloadInvoice(id) {
  institutionInvoiceDetailByInvoiceId({ id }).then(data => {
    const invoiceImage = data.invoiceImage;

    if(invoiceImage) {
      // 从URL获取文件名
      const fileName = getFileNameFromUrl(invoiceImage);

      // 使用axios直接下载
      axios({
        url: invoiceImage,
        method: "get",
        responseType: "blob"
      }).then(response => {
        // 创建blob链接并下载
        const blob = new Blob([response.data]);
        const link = document.createElement("a");
        link.href = window.URL.createObjectURL(blob);
        link.download = fileName || `发票_${id}.pdf`;
        document.body.appendChild(link);
        link.click();
        window.URL.revokeObjectURL(link.href);
        document.body.removeChild(link);
      }).catch(error => {
        this.$message.error("下载发票失败：" + error.message);
      });
    } else {
      this.$message.error("发票图片不存在");
    }
  });
}

// 搜索表单配置
const searchForm = [
  {
    prop: "dateRange",
    propKey: ["createTimeStart", "createTimeEnd"],
    label: "申请时间",
    search: true,
    tableHidden: true,
    type: global.formItemType.datetimerange,
    valueFormat: "yyyy-MM-dd HH:mm:ss",
    pickerDate: datePickerOptionsExpand(90),
  },
  {
    prop: "valueAddedTaxInvoiceType",
    label: "增值税发票类型",
    tableHidden: true,
    search: true,
    type: global.formItemType.select,
    syncData: {
      url: dataDetailList,
      value: "value",
      label: "label",
      params: { groupCode: "VALUE_ADDED_TAX_INVOICE_TYPE" },
    },
  },
  {
    prop: "invoiceType",
    label: "发票类型",
    tableHidden: true,
    search: true,
    type: global.formItemType.select,
    syncData: {
      url: dataDetailList,
      value: "value",
      label: "label",
      params: { groupCode: "INSTITUTION_INVOICE_TYPE" },
    },
  },
  {
    prop: "invoiceHeader",
    label: "抬头类型",
    tableHidden: true,
    search: true,
    type: global.formItemType.select,
    syncData: {
      url: dataDetailList,
      value: "value",
      label: "label",
      params: { groupCode: "INSTITUTION_INVOICE_HEADER" },
    },
  },
  {
    prop: "checkStatus",
    label: "开票状态",
    tableHidden: true,
    search: true,
    type: global.formItemType.select,
    syncData: {
      url: dataDetailList,
      value: "value",
      label: "label",
      params: { groupCode: "INSTITUTION_INVOICE_CHECK_STATUS" },
    },
  },
];

export const tableColumn = [
  ...searchForm,
  {
    label: "申请时间",
    prop: "createTime",
    minWidth: "192",
  },
  {
    label: "抬头类型",
    prop: "invoiceHeader",
    minWidth: "153",
    filterUrl: true,
    syncData: {
      url: dataDetailList,
      value: "value",
      label: "label",
      params: { groupCode: "INSTITUTION_INVOICE_HEADER" },
    },
  },
  {
    label: "发票金额",
    prop: "amount",
    minWidth: "104",
    type: "money",
  },
  {
    label: "发票类型",
    prop: "invoiceType",
    minWidth: "146",
    filterUrl: true,
    syncData: {
      url: dataDetailList,
      value: "value",
      label: "label",
      params: { groupCode: "INSTITUTION_INVOICE_TYPE" },
    },
  },
  {
    label: "增值税发票类型",
    prop: "valueAddedTaxInvoiceType",
    minWidth: "146",
    filterUrl: true,
    syncData: {
      url: dataDetailList,
      value: "value",
      label: "label",
      params: { groupCode: "VALUE_ADDED_TAX_INVOICE_TYPE" },
    },
  },
  {
    label: "公司名称",
    prop: "companyName",
    minWidth: "244",
  },
  {
    label: "公司税号",
    prop: "companyTaxId",
    minWidth: "186",
  },
  {
    label: "审核备注",
    prop: "checkRemark",
    minWidth: "131",
  },
  {
    label: "开票状态",
    prop: "checkStatus",
    minWidth: "131",
    render: (h, { row }) => {
      switch (row.checkStatus) {
      case 1:
        return (
          <div class="flex items-center">
            <div class="px-[8px] rounded-[2px] border-[1px] border-solid border-[#B9F2D3] text-[14px] text-[#22BF7E] h-[24px] box-border flex items-center justify-center">
							申请中
            </div>
          </div>
        );
      case 2:
        return (
          <div class="flex items-center">
            <div class="px-[8px] rounded-[2px] border-[1px] border-solid border-[#A0CFFB] text-[14px] text-[#3887F5] h-[24px] box-border flex items-center justify-center">
							已开票
            </div>
          </div>
        );
      case 3:
        return (
          <div class="flex items-center">
            <div class="px-[8px] rounded-[2px] border-[1px] border-solid border-[#FBCFC3] text-[14px] text-[#EB4738] h-[24px] box-border flex items-center justify-center">
							开票失败
            </div>
          </div>
        );
      default:
        return <span>-</span>;
      }
    },
  },
];

/** 线索包开票明细 */
export const invoiceItemsColumn1 = [
  {
    label: "线索名称",
    prop: "title",
    minWidth: "104",
  },
  {
    label: "支付时间",
    prop: "time",
    width: "183",
  },
  {
    label: "实际支付",
    type: "money",
    prop: "amount",
  },
];

/** 法临币开票明细 */
export const invoiceItemsColumn2 = [
  {
    label: "名称",
    prop: "label",
    minWidth: "104",
  },
  {
    label: "开票类型",
    prop: "title",
    width: "104",
  },
  {
    label: "案件信息",
    prop: "info",
    width: "183",
  },
  {
    label: "抢单时间",
    prop: "time",
    width: "183",
  },
  {
    label: "单条支付",
    type: "money",
    prop: "amount",
    width: "104",
  },
];


export const invoiceInfoProps = [
  {
    label: "发票总额",
    value: "amount",
    render: (data) => {
      return `￥${priceNumber(data.amount)}`;
    },
  },
  { label: "发票数量", value: "invoiceCount" },
  { label: "发票内容", value: "invoiceContent" },
  { label: "开票方", value: "invoiceParty" },
  { label: "公司名称", value: "companyName" },
  { label: "公司税号", value: "companyTaxId" },
  { label: "申请时间", value: "createTime" },
  {
    label: "审核状态",
    value: "checkStatus",
    render: (data, h) => {
      switch (data.checkStatus) {
      case 1:
        return (
          <div class="flex items-center">
            <div class="px-[8px] rounded-[2px] border-[1px] border-solid border-[#B9F2D3] text-[14px] text-[#22BF7E] h-[24px] box-border flex items-center justify-center">
                申请中
            </div>
          </div>
        );
      case 2:
        return (
          <div class="flex items-center">
            <div class="px-[8px] rounded-[2px] border-[1px] border-solid border-[#A0CFFB] text-[14px] text-[#3887F5] h-[24px] box-border flex items-center justify-center">
                已开票
            </div>
            <div class="text-[14px] text-[#3887F5] ml-[16px] cursor-pointer" onClick={() => handleDownloadInvoice(data.id)}>
              下载电子发票
            </div>
          </div>
        );
      case 3:
        return (
          <div class="flex items-center">
            <div class="px-[8px] rounded-[2px] border-[1px] border-solid border-[#FBCFC3] text-[14px] text-[#EB4738] h-[24px] box-border flex items-center justify-center">
                开票失败
            </div>
          </div>
        );
      default:
        return <span>-</span>;
      }
    },
  },
  {
    label: "审核备注",
    value: "checkRemark",
    render: (data, h) => {
      if (data.checkRemark) {
        return (
          <div class="text-[14px] text-[#EB4738] p-[12px] bg-[#F5F5F7] rounded-[4px]">
            {data.checkRemark || "-"}
          </div>
        );
      }

      return <span>-</span>;
    },
  },
];


