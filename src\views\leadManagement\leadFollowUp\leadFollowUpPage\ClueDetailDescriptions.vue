<template>
	<el-descriptions :column="2" size="medium">
		<el-descriptions-item label="当事人姓名">{{data.userName}}</el-descriptions-item>
		<el-descriptions-item label="联系方式">{{data.userPhone}}</el-descriptions-item>
		<el-descriptions-item label="当事人所在地">{{data.provinceName}}{{data.regionName}}</el-descriptions-item>
		<el-descriptions-item label="发布时间">
			{{data.publishTime}}
		</el-descriptions-item>
		<el-descriptions-item label="跟进人">{{data.qdInstitutionStaffName}}</el-descriptions-item>
		<el-descriptions-item label="抢单时间">{{data.createTime}}</el-descriptions-item>
	</el-descriptions>
</template>

<script>
export default {
  name: "ClueDetailDescriptions",
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  }
};
</script>

<style scoped lang="scss">
::v-deep .el-descriptions-item__content {
	color: #333333;
}
</style>
