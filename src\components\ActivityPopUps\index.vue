<template>
  <div>
    <!-- 活动浮窗-->
    <div class="fixed z-[20] right-[12px] bottom-[27px]" v-if="floatingActivityList.length > 0">
      <el-carousel :interval="2000" class="w-[100px] floating-bottom" height="100px" direction="horizontal" :autoplay="true" indicator-position="none">
        <el-carousel-item v-for="i in floatingActivityList" :key="i.id">
          <img @click="handleClick(i)" class="block w-full h-full cursor-pointer" :src="i.rightBottomFloatWindowUrl" alt="" />
        </el-carousel-item>
      </el-carousel>
    </div>

    <!-- 活动弹窗   -->
    <div v-if="isShowActivityPopUps" class="fixed z-[9999] left-0 right-0 bg-[rgba(0,0,0,0.7)] bottom-0 top-0 flex items-center justify-center">
        <div class=" flex flex-col items-center justify-center">
          <div class="flex w-full justify-end pb-[16px]">
            <img class="w-[40px] cursor-pointer" @click.stop="close" src="@/components/ActivityPopUps/<EMAIL>" alt="" />
          </div>
          <el-carousel :interval="2000" :indicator-position="activityPopUpsList.length===1?'none':''" class="w-[580px] floating-bottom"  height="480px" :autoplay="true">
            <el-carousel-item v-for="i in activityPopUpsList" :key="i.id">
              <img @click="handleFloatingClick(i)" class="block h-[460px] w-full cursor-pointer" :src="i.indexPopUpUrl" alt="" />
            </el-carousel-item>
          </el-carousel>
        </div>
    </div>
  </div>
</template>

<script>
import { caseSourceActivityV2List } from "@/api";
import { isDiffDay } from "utils/toolMethods";
import { setTriggerTime } from "utils/storage";
import moment from "moment/moment";

export default {
  name: "ActivityPopUps",
  data() {
    return {
      /* 活动弹窗列表*/
      activityPopUpsList: [],
      /* 浮窗活动列表*/
      floatingActivityList: [],
      /* 活动弹窗是否展示*/
      isShowActivityPopUps: false
    };
  },
  mounted() {
    caseSourceActivityV2List().then((res = []) => {
      if(this.basics.isArrNull(res)){
        return;
      }
      this.activityPopUpsList = res.filter(item => !this.basics.isNull(item.indexPopUpUrl));
      this.floatingActivityList = res.filter(item => !this.basics.isNull(item.rightBottomFloatWindowUrl));
      this.isShowActivityPopUps = !isDiffDay("caseSourceActivityV2List") && !this.basics.isArrNull(this.activityPopUpsList);
    });
  },
  methods: {
    close(){
      setTriggerTime("caseSourceActivityV2List", moment().format("YYYY-MM-DD"));
      this.isShowActivityPopUps = false;
    },
    handleFloatingClick(data){
      this.close();
      this.handleClick(data);
    },
    handleClick(data = {}){
      this.$store.dispatch("setIframePreviewVisible", {
        title: data.name,
        previewUrl: data.h5Url + "?id=" + data.id,
        nestApp: true
      });
    }
  }
};
</script>

<style scoped lang="scss">
.floating-bottom{
  ::v-deep .el-carousel__arrow{
    display: none !important;
  }
  ::v-deep .el-carousel__indicator--horizontal{
    bottom: 0;
    padding: 0 0 0 6px;
    &.is-active{
      .el-carousel__button{
        background: rgba(255,255,255,1);
      }
    }
    &:first-child{
      padding: 0;
    }

    .el-carousel__button{
      width: 10px;
      height: 10px;
      border-radius: 50%;
      background: rgba(255,255,255,0.5);
    }
  }
}
</style>
