import { dataDetailCache } from "@/api/common";
import basics from "utils/basicsMethods";
import store from "@/store";
import { parseJSON } from "utils/toolMethods";
/* 字典查询对应得值*/
export const  getDictValue = ({ dictApi = dataDetailCache, groupCode, key = "value", value } = {}) => {
  return dictApi({ groupCode: groupCode }).then(data => {
    if(basics.isNull(data)) return Promise.reject("no Dict data");
    const result = data.find(item => item[key] === value);
    if(result) return result;
    return Promise.reject("no Dict result");
  });
};


/* 获取机构对应字典单独配置*/
export const getDictValueByOrg = () => {
  if(basics.isNull(store.getters["userInfo"].institutionId)) return Promise.reject();
  const value = String(store.getters["userInfo"].institutionId );
  return  getDictValue({
    groupCode: "LKY_NICK_NAME",
    value
  }).then(data => {
    return parseJSON(data);
  });
};

/* 获取字典描述里面对应得key*/
export const getDictValueByOrgDescKey = ({ key }) => {
  return getDictValueByOrg().then(data => {
    if(data.supplementDesc && !basics.isNull(data.supplementDesc[key])) return data.supplementDesc[key];
    return Promise.reject();
  });
};
