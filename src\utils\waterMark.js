CanvasRenderingContext2D.prototype.wrapText = function(text, x, y, lineHeight = 20) {
  if (typeof text !== "string" || typeof x !== "number" || typeof y !== "number") {
    return;
  }

  const context = this;
  const canvas = context.canvas;

  if (typeof lineHeight === "undefined") {
    lineHeight = (canvas && parseInt(window.getComputedStyle(canvas).lineHeight)) || parseInt(window.getComputedStyle(document.body).lineHeight);
  }

  const arrText = text.split(",");
  context.fillText(arrText[0], x, y);
  y += lineHeight;
  context.fillText(arrText[1], x, y);
};

const watermark = {};

let maskDiv = null;

const setWatermark = (str, dom) => {
  const id = "bydWaterMark";

  if (document.getElementById(id) !== null) {
    removeMaskDiv(dom);
    return;
  }

  const can = document.createElement("canvas");
  can.width = 400;
  can.height = 180;

  const cans = can.getContext("2d");
  cans.rotate(-20 * Math.PI / 180);
  cans.font = "18px Vedana";
  cans.fillStyle = "rgba(200, 200, 200, 0.20)";
  cans.textAlign = "left";
  cans.textBaseline = "Middle";
  cans.wrapText(str, 0, can.height / 1.1);

  maskDiv = document.createElement("div");
  maskDiv.id = id;
  maskDiv.style.pointerEvents = "none";
  maskDiv.style.top = "70px";
  maskDiv.style.left = "180px";
  maskDiv.style.position = "fixed";
  maskDiv.style.zIndex = "100000";
  maskDiv.style.width = document.getElementById(dom).clientWidth - 20 + "px";
  maskDiv.style.height = document.getElementById(dom).clientHeight + "px";
  maskDiv.style.background = "url(" + can.toDataURL("image/png") + ") left top repeat";
  document.getElementById(dom).appendChild(maskDiv);
  return id;
};

const Monitor = (str, dom) => {
  const body = document.getElementById("app-main");
  const options = {
    childList: true
  };
  const callback = (mutations, observer) => {
    if (mutations[0].removedNodes[0] && mutations[0].removedNodes[0].id === "bydWaterMark") {
      setWatermark(str, dom);
    }
  };
  const observer = new MutationObserver(callback);
  observer.observe(body, options);
};

const removeMaskDiv = (dom) => {
  if (document.getElementById(dom).contains(maskDiv)) {
    document.getElementById(dom).removeChild(maskDiv);
  }
};

watermark.set = (str, dom) => {
  setWatermark(str, dom);
  Monitor(str, dom);
  const timer = null;
  window.onresize = () => {
    if (timer) {
      clearTimeout(timer);
    } else {
      setTimeout(() => {
        setWatermark(str, dom);
      }, 1000);
    }
  };
};

export default watermark;
