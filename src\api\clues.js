import { request } from "utils/axios";

/** 案源线索 */
export const caseSourceClues = (params) => request.post("/law-cloud/lc/caseSourceClues", params);
/** 近七日案源线索 */
export const caseSourceCluesWeek = (params) => request.post("/law-cloud/lcCluePlanV2/page", params);
/** 律客云近七日案源推荐列表总数 */
export const caseSourceCluesPushNum = (params) => request.post("/law-cloud/lcCluePlanV2/recommendCount", params);
/** 律客云自动推荐案源详情 */
export const caseSourceCluesPushDetail = (params) => request.post("/law-cloud/lcCluePlanV2/detail", params);
/** 律所员工修改案源线索包推荐计划 */
export const caseSourceCluesPushDetailUpdate = (params) => request.post("/law-cloud/lcCluePlan/update", params);
/** 律所员工修改案源线索包推荐计划 */
export const caseSourceCluesPushDetailSwitchLcClue = (params) => request.post("/law-cloud/lcCluePlan/switchLcClue", params);

/** 律所员工修改案源线索包推荐计划 */
export const caseSourceCluesPushDetailAdd = (params) => request.post("/law-cloud/lcCluePlan/insert", params);

/** 律客云-线索广场-即时咨询分页 */
export const qaMessagePage = (params) => request.post("/law-cloud/lc/qaMessage/page", params);

/** 剩余可抢单次数 */
export const caseRemainTimes = (params) => request.post("/law-cloud/lc/caseRemainTimes", params);
/** 后置独享抢单 */
export const postYkjGrab = (params) => request.post("/law-cloud/lc/postYkjGrab", params);

/** 案源线索抢单 */
export const caseGrab = (params) => request.post("/law-cloud/lc/caseGrab", params);
export const caseGrabV2 = (params) => request.post("law-cloud/lc/v2/caseGrab", params);
export const caseGrabV3 = (params) => request.post("law-cloud/lc/v3/caseGrab", params);

/** 线索抢单校验是否同律所抢了案源 */
export const caseGrabCheck = (params) => request.post("/law-cloud/lc/caseGrabCheck", params);

/** 查询当前机构的工作城市 */
export const institutionWorkCity = (params) => request.post("/law-cloud/lc/institutionWorkCity", params);

/** 案源线索抢单 */
export const caseWdGrab = (params) => request.post("/law-cloud/lc/caseWdGrab", params);
export const caseWdGrabV2 = (params) => request.post("/law-cloud/lc/v2/caseWdGrab", params);

/** 线索跟进列表 */
export const clueFollowList = (params) => request.post("/law-cloud/lc/clueFollowList", params);

/** 导出线索跟进列表 */
export const exportClueFollowList = (params) => request.post("/law-cloud/lc/exportClueFollowList", params);

/** 保存线索跟进 */
export const saveClueFollow = (params) => request.post("/law-cloud/lc/saveClueFollow", params);

/** 线索跟进详情列表 */
export const getClueFollowDetailList = (params) => request.post("/law-cloud/lc/getClueFollowDetailList", params);

/** 线索统计分页 */
export const clueStatistics = (params) => request.post("/law-cloud/lc/clueStatistics", params);

/** 抢线索总分布 */
export const grabClueStatistics = (params) => request.post("/law-cloud/lc/grabClueStatistics", params);

/** 跟进状态统计 */
export const followDataStatistics = (params) => request.post("/law-cloud/lc/followDataStatistics", params);

/** 导出线索统计分页 */
export const exportClueStatistics = (params) => request.post("/law-cloud/lc/export/clueStatistics", params);

/** 获取跟进中数量 */
export const getFollowCount = (params = {}) => request.post("/law-cloud/lc/getFollowCount", params);

/** 修改线索跟进用户名 */
export const updateClueFollowUser = (params) => request.post("/law-cloud/lc/updateClueFollowUser", params);

/** 修改线索跟进用户名 */
export const closeClueFollow = (serverId) => request.post(`/law-cloud/lc/closeClueFollow/${serverId}` );

/** 修改线索跟进标签 */
export const updateClueFollowLabel = (params) => request.post("/law-cloud/lc/updateClueFollowLabel", params );

/** 新增线索跟进标签 */
export const saveClueFollowLabel = (params) => request.post("/law-cloud/lc/saveClueFollowLabel", params );

/** 删除线索跟进标签 */
export const deleteClueFollowLabel = (params) => request.post("/law-cloud/lc/deleteClueFollowLabel", params );

/* 案源推送红点*/
export const lcCluePlanV2PushRedDot = (params) => request.post("/law-cloud/lcCluePlanV2/pushRedDot", params );

/* 推荐案源记录已读处理*/
export const lcCluePlanV2RecordRead = (params) => request.post("/law-cloud/lcCluePlanV2/recordRead", params );

/* 线索转移*/
export const lcTransferClue = (params) => request.post("/law-cloud/lc/transferClue", params );

/* 律客云消耗权益明细*/
export const statisticsEquity = (params) => request.post("/law-cloud/lc/statistics/equity/page", params );

/* 律客云消耗权益明细导出*/
export const statisticsEquityExport = (params) => request.post("/law-cloud/lc/statistics/equity/export", params );
/* 律客云机构客户跟进统计-跟进客户所在地省份占比*/
export const lawFirmLocation = (params) => request.post("/law-cloud/2lc/customerFollowAnalysis/lawFirmLocation", params );
/* 律客云机构客户跟进统计-跟进状态分布 */
export const followUpStatusDistribution = (params) => request.post("/law-cloud/2lc/customerFollowAnalysis/followUpStatusDistribution", params );
/* 律客云机构客户跟进统计-跟进标签分布 */
export const followUpLabelDistribution = (params) => request.post("/law-cloud/2lc/customerFollowAnalysis/followUpLabelDistribution", params );
/* 律客云抢单分析法临币消耗明细分页查询 维度：员工 */
export const statisticsFalinCoinConsumePage = (params) => request.post("/law-cloud/lc/statistics/falinCoin/consume/page", params );
/* 律客云抢单分析法临币消耗明细导出 维度：员工 */
export const statisticsFalinCoinConsumeExport = (params) => request.post("/law-cloud/lc/statistics/falinCoin/consume/export", params );
/* 律客云退单申请记录 */
export const feedBackCaseSourceLists = (params) => request.post("/law-cloud/feedBackCaseSource/page", params );
/* 退款申请-查看线索详情 */
export const feedBackCaseSourceDetail = (params) => request.post("/law-cloud/feedBackCaseSource/detail", params );
/* 律客云-退款申请 */
export const feedBackCaseSourceSubmit = (params) => request.post("/law-cloud/feedBackCaseSource/tkApply", params );
/* 律客云退单申请记录-导出 */
export const feedBackCaseSourceExport = (params) => request.post("/law-cloud/feedBackCaseSource/export", params );
/* 默认支付方式 */
export const lcInstitutionStaffGetPayWay = (params) => request.post("/law-cloud/lcInstitutionStaff/getPayWay", params );
/* 修改默认支付方式*/
export const lcInstitutionStaffUpdatePayWay = (params) => request.post("/law-cloud/lcInstitutionStaff/updatePayWay", params );

/* 查询最先一笔将过期的线索包*/
export const lcLawFirmsInstitutionOrderTimeOutOrder = (params) => request.post("/law-cloud/lcLawFirmsInstitutionOrder/timeOutOrder", params );

/** 
 * 新增抢单计划
 * https://showdoc.imlaw.cn/web/#/5/3944
 */
export const autoGrabPlanInsert = (params) => request.post("/law-cloud/lc/autoGrabPlan/insert", params );

/** 
 * 抢单计划列表
 * https://showdoc.imlaw.cn/web/#/5/3945
 */
export const autoGrabPlanList = (params) => request.post("/law-cloud/lc/autoGrabPlan/list", params );

/** 
 * 修改抢单计划
 * https://showdoc.imlaw.cn/web/#/5/3946
 */
export const autoGrabPlanUpdate = (params) => request.post("/law-cloud/lc/autoGrabPlan/update", params );

/** 
 * 修改计划启用状态
 * https://showdoc.imlaw.cn/web/#/5/3947
 */
export const autoGrabPlanUpdateStatus = (params) => request.post("/law-cloud/lc/autoGrabPlan/updateStatus", params );

/** 
 * 删除抢单计划
 * https://showdoc.imlaw.cn/web/#/5/3948
 */
export const autoGrabPlanDelete = (params) => request.post("/law-cloud/lc/autoGrabPlan/delete", params );

/** 
 * 获取单个计划详情信息
 * https://showdoc.imlaw.cn/web/#/5/3949
 */
export const autoGrabPlanDetail = (params) => request.post("/law-cloud/lc/autoGrabPlan/detail", params );

/** 
 * 每日自动抢案源信息汇总
 * https://showdoc.imlaw.cn/web/#/5/3950
 */
export const autoGrabPlanSummary = (params) => request.post("/law-cloud/lc/autoGrabPlan/summary", params );

/** 
 * 每日自动抢案源信息汇总
 * https://showdoc.imlaw.cn/web/#/5/3957
 */
export const lcInstitutionStaffAgreeAutoGrabAgreement = (params) => request.post("/law-cloud/lcInstitutionStaff/agreeAutoGrabAgreement", params );