import Vue from "vue";
import router from "router";

const EventBus = new Vue();
let ons = [];
let routeName = "";

/* 这里 做一次代理 每次注册事件都会把重复的事件给销毁保持事件的唯一性
* 一个页面可以存在多个 相同事件 切换页面的时候会销毁 上一个页面
* */
function $onRewrite(event, callback) {
  const currentRouter = router.currentRoute.name;
  /* 判断当前路由切换没有 删除已经调用过得事件*/
  if (routeName !== currentRouter) {
    console.log("上一页im监听的回调", ons.join(","));
    /* 这里不能摧毁全部监听 只能摧毁当前页面的监听*/
    $offRewrite();
  }
  routeName = currentRouter;
  ons.push({
    event,
    callback
  });
  return EventBus.$on(event, callback);
}

/* 清除当前注册的事件*/
function $offRewrite() {
  console.log("清除当前注册的事件", ons);
  ons.forEach((item) => EventBus.$off(item.event, item.callback));
  ons = [];
}

EventBus.$offRewrite = $offRewrite;
EventBus.$onRewrite = $onRewrite;
const emit = EventBus.$emit;
EventBus.$emit = function(event, ...args) {
  return emit.call(EventBus, event, ...args);
};

export default EventBus;
