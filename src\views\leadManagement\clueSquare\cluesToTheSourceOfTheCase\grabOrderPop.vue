<template>
  <app-popup
    :visible="show"
    @close="cancel"
    :show-close="false"
    :show-footer="false"
    show-cancel
    :width="'560px'"
    @cancel="cancel"
    @confirm="onSubmit('form')"
    title="案源支付"
  >
    <ul>
      <li v-if="numObj.num !== 0 && isLeadPackageDisplay()" class="flex flex-align-center flex-space-between">
          <div>
            <img class="icon-img" src="@/assets/images/icon1.png" />
            <div class="texts">
              <p class="fs14 text-[#333333]">线索包剩余次数</p>
              <p class="fs12 text-[#666666]">{{numObj.num}}条</p>
            </div>
          </div>
        <div>
          <label  class="text-[#333333]">抢单扣除权益：{{ detail.way === 1 ?  deductNum : 1}}次</label>
          <el-radio :disabled="isDisableCount" v-model="radioVal" label="1"  />
        </div>
      </li>
      <li class="flex flex-align-center flex-space-between">
          <div>
            <img class="icon-img" src="@/assets/images/icon2.png" />
            <div class="texts">
              <p class="fs14 text-[#333333]">赠送余额</p>
              <p class="fs12 text-[#666666]"> {{priceYuan(remainingGrabTimes.giftRemainAmount)}} </p>
            </div>
          </div>
        <div>
          <label class="text-[#333333]"> {{detail.isCaseSource ? '案源线索' : '即时咨询'}}{{detail.way === 1 ?  '独享' : ''}}价格：{{priceYuan(detail.amount)}}</label>
          <el-radio v-model="radioVal" :disabled="remainingGrabTimes.giftRemainAmount < detail.amount" label="3" />
        </div>
      </li>
      <li class="flex flex-align-center flex-space-between">
          <div>
            <img class="icon-img" src="@/assets/images/icon3.png" />
            <div class="texts">
              <p class="fs14 text-[#333333]">充值余额</p>
              <p class="fs12 text-[#666666]">{{priceYuan(remainingGrabTimes.remainAmount)}}</p>
            </div>
          </div>
        <div>
          <label  class="text-[#333333]">{{detail.isCaseSource ? '案源线索' : '即时咨询'}}{{detail.way === 1 ?  '独享' : ''}}价格：{{priceYuan(detail.amount)}}</label>
          <el-radio v-model="radioVal"  :disabled="remainingGrabTimes.remainAmount <  detail.amount" label="2" />
        </div>
      </li>
    </ul>
  </app-popup>
</template>

<script>
import AppPopup from "components/appPopup/index.vue";
import { caseRemainTimes } from "@/api/clues";
import { priceYuan } from "utils/toolMethods";
import { commonConfigKey } from "@/api/common";
import { isLeadPackageDisplay } from "components/leadPackageDisplay";

export default {
  name: "GrabOrderPop",
  components: { AppPopup },
  props: {
    show: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: () => {
        return {};
      }
    },
    remainingGrabTimes: {
      type: Object,
      default: () => {
        return {
          remainAmount: 0,
          giftRemainAmount: 0,
        };
      },
      desc: "查询可抢剩余次数，金额 "
    }
  },
  data(){
    return {
      radioVal: "1",
      deductNum: 1
    };
  },
  computed: {
    /** 案源次数、问答次数, 默认支付方式 **/
    numObj(){
      const _num = this.detail?.isCaseSource ? this.remainingGrabTimes.remainCaseCount : this.remainingGrabTimes.remainQaMessageCount;
      let val = "1";
      const count = this.detail.way === 1 ? this.deductNum : 1;
      if(~~_num < ~~count || !isLeadPackageDisplay()) {
        if ( ~~this.remainingGrabTimes.giftRemainAmount < ~~this.detail.amount) {
          val = "2";
        } else {
          val = "3";
        }
      }
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.radioVal = val;
      return  {
        num: _num || 0,
        radioVal: val
      };
    },
    isDisableCount(){
      if(this.detail.way === 1){
      //   独享
        return  this.numObj.num < this.deductNum;

      }else {
      //  抢单
        return  this.numObj.num < 1;
      }
    }
  },
  created() {
    this.getDeductNum();
  },
  methods: {
    isLeadPackageDisplay,
    cancel(){
      this.$emit("update:show", false);
    },
    onSubmit(){
      this.$emit("confirmPop", this.radioVal);
      this.cancel();
    },
    priceYuan,
    /** 获取扣除次数 */
    getDeductNum() {
      commonConfigKey({
        paramName: "lc_grab_case_deduct_num",
      }).then(res => {
        this.deductNum = res;
      });
    },
  }
};
</script>

<style lang="scss" scoped>
ul{
  padding: 24px;
     .icon-img{
       width: 40px;
       height: 40px;
       vertical-align: top;
       margin-right: 8px;
     }
     .texts{
       display: inline-block;
       line-height: 22px;
     }
  label{
    margin-right: 8px;
  }
  /deep/ .el-radio__label{
    display: none;
  }
  li{
    margin-bottom: 24px;

    &:last-child{
      margin-bottom: 0;
    }
  }
}
</style>
