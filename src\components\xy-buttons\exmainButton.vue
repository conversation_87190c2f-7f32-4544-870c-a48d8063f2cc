
<template>
  <el-button :type="typeName" size="small"   @click="edit">
    <slot>审核</slot>
  </el-button>
</template>

<script>

export default {
  name: "SearchButton",
  props: {
    typeName: {
      type: String,
      default: "text"
    }
  },
  data() {
    return {
      filed: ''
    };
  },
  methods: {
    edit() {
      this.$emit("click");
    }
  }
};
</script>

<style scoped>

</style>
