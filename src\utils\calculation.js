import basics from "utils/basicsMethods";

const isNull = basics.isNull;
// 给一个当前金额，利率  利率百分数
// 考虑精度问题最后是数字类型
export function getAmountRate({ currentAmount, rate }) {
  // 解决精度问题
  if (isNull(currentAmount) || isNull(rate)) return 0; // 使用toFixed保留两位小数，然后转换为数字类型
  const result = (currentAmount * (rate / 100)).toFixed(2);
  return parseFloat(result);
}

// 根据金额和规则计算律师费
// rules规则是一个数组，每个元素是一个对象，对象的属性是金额和利率 还有金额区间
// 金额是数字类型，利率是数字类型
// 金额区间是当前金额需要匹配的规制 最大区间没有就标识最大是无限
// 返回和getMinMaxRate一样的结果
export function calculateLawyerFee({ amount, rules, id }) {
// 如果传入了id 就直接匹配id 对应的规制数据 返回当前数据的最大金额 最小金额
  if(!isNull(id)) {
    const rule = rules.find(rule => rule.id === Number(id));
    if(!rule) return [0];
    return [rule.minAmount, rule.maxAmount];
  }
  if(isNull(amount)) return [0];
  const amountToNum = Number(amount);
  const rule = rules.find(rule => {
    const { minAmount, maxAmount } = rule;
    if(!isNull(minAmount) && !isNull(maxAmount)) {
      return amountToNum >= minAmount && amountToNum <= maxAmount;
    }
    if(!isNull(minAmount)) {
      return amountToNum >= minAmount;
    }
    if(!isNull(maxAmount)) {
      return amountToNum <= maxAmount;
    }
  });
  if(!rule) return [0];
  const { minRate, maxRate } = rule;
  const min = getAmountRate({ currentAmount: amountToNum, rate: minRate });
  if(min < rule.minLawyerFee) return [rule.minLawyerFee];
  const max = getAmountRate({ currentAmount: amountToNum, rate: maxRate });
  if(minRate === maxRate) return [min];
  else return [
    min,
    max
  ];
  // 根据金额和规则计算律师费
}
