<template>
  <div>
    <p class="font-[700] text-[16px] py-[24px] text-center text-[#333333]">扫码登录</p>
    <div class="flex justify-center">
      <div class="relative">
        <div id="qrcode" class=" w-[160px] h-[160px] block" />
        <div  v-if="qrcodeState==='EXPIRED'||qrcodeState==='CONFIRMED'" class="absolute z-[1]  left-0 top-0 w-full h-full bg-[rgba(255,255,255,0.9)]">
          <div v-if="qrcodeState==='EXPIRED'" @click="getQRCode" class="cursor-pointer w-full h-full flex flex-col items-center justify-center">
            <img class="w-[24px] h-[24px]" src="@/views/login/imgs/<EMAIL>" alt="" />
            <p class="pt-[12px] text-[14px] text-[#666666]">二维码已过期 <br />
              点击重新加载</p>
          </div>
          <div class="w-full h-full flex flex-col items-center justify-center" v-if="qrcodeState==='CONFIRMED'">
            <p class="text-[14px] text-[#666666]">授权成功，等待跳转</p>
          </div>
        </div>
      </div>
      <img class="w-[196px] h-[160px] ml-[24px]" src="@/views/login/imgs/<EMAIL>" alt="" />
    </div>
    <p class="text-center text-[14px] text-[#666666] pt-[24px] pb-[24px]">打开<span class="text-[#3887F5]">律客云APP</span>扫码登录</p>
  </div>
</template>

<script>
import { scanQRCodeCheckStatus, scanQRCodeCreateQRCode } from "@/api/login";
import QRcode from "qrcodejs2";
import { setToken } from "utils/storage";
import { createWs } from "utils/creat-ws";

export default {
  name: "ScanTheCodeLogin",
  data() {
    return {
      codeFun: null,
      qrcodeData: {},
      qrcodeState: "",
      timer: null,
      //   点击状态 防止多次点击
      clickState: false
    };
  },
  mounted() {
    this.getQRCode();
  },
  methods: {
    /* 获取二维码*/
    getQRCode() {
      if(this.clickState) return;
      this.clickState = true;
      scanQRCodeCreateQRCode({ genQrcodeType: "lawCloudPcQrCodeLogin" }).then(data => {
        this.qrcodeData = data;
        this.qrcodeState = "";
        this.generateCode(JSON.stringify(data));
        this.checkQRCodeStatus();
      }).finally(() => {
        setTimeout(() => {
          this.clickState = false;
        }, 1000);
      });
    },

    /* 生成二维码*/
    generateCode(text) {
      this.$nextTick(() => {
        if(this.codeFun){
          this.codeFun.makeCode(text);
          return;
        }
        this.codeFun = new QRcode("qrcode", {
          width: "160", // 生成二维码宽度
          height: "160", // 生成二维码高度
          text: text, // 二维码地址或文本，如果是网址扫码后会跳转网址
          colorDark: "#000",
          colorLight: "#fff"
        });
      });
    },
    /* 查询二维码状态*/
    checkQRCodeStatus() {
      /* data	QRCodeLoginStatusCheckResponse	N
—status	String	Y	二维码状态WAITING:待扫码；SCANNED:已扫码；CONFIRMED:已确认登录；EXPIRED:二维码已过期
—token	String	N	pc端token status=CONFIRMED && genQrcodeType=lawCloudPcQrCodeLogin时有值*/
      clearInterval(this.timer);
      const set = () => scanQRCodeCheckStatus(this.qrcodeData).then(data => {
        // 已确认登录或者二维码已过期 停止定时器
        if(data.status === "CONFIRMED" ||  data.status === "EXPIRED"){
          clearInterval(this.timer);
        }
        if (data.status === "CONFIRMED" && data.token){
          this.$store.commit("SET_TOKEN", data.token);
          setToken(data.token);
          createWs();
          this.$emit("loginSuccess", data);
        }
        this.qrcodeState = data.status;
      });
      this.timer = setInterval(() => set(), 1000);
      this.$on("hook:destroyed", () => {
        clearInterval(this.timer);
      });
    }
  }
};
</script>

<style scoped lang="scss">

</style>
