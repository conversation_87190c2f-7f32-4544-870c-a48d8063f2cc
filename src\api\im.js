
import { request } from "utils/axios.js";

/* 获取会员一对一付费服务列表*/
export const getVipServiceList = (params) => request.post("/paralegal/serviceManege/getVipServiceList", params);
/* 快捷回复查询*/
export const quickReplyGet = (params) => request.post("/info/quickReply/get", params);
/* 快捷回复新增*/
export const quickReplyInsert = (params) => request.post("/info/quickReply/insert", params);
/* 快捷回复修改*/
export const quickReplyUpdate = (params) => request.post("/info/quickReply/update", params);
/* 律师是否同意暴露电话*/
export const lawyerOfferPhone = (params) => request.post(`/paralegal/caseSourceServerV2/lawyerOfferPhone/${params.caseSourceServerV2Id}/${params.type}`);
/* 新获取小号或真实号码*/
export const getNewServerCall = (params) => request.post(`/paralegal/caseSourceServerV2/getNewServerCall/${params.caseSourceServerV2Id}`);
/* 律师完成服务是否显示提醒评价push*/
export const lawyerCaseSourceDiscussPush = (params) => request.post(`/paralegal/lawyerCaseSource/discussPush/${params.caseSourceServerV2Id}`);
/* 发送提醒用户进行评价的push*/
export const lawyerCaseSourceSendDiscussPush = (params) => request.post(`/paralegal/lawyerCaseSource/sendDiscussPush/${params.caseSourceServerV2Id}`);
/* 开始锁定*/
export const caseSourceServerV2StartLock = (params) => request.post(`/paralegal/caseSourceServerV2/startLock/${params.caseSourceServerV2Id}`);
/* 律师点击结束完成服务（我的案源结束跟进，im聊天中点击完成服务案源）*/
export const caseSourceServerV2CompleteCaseServer = (params) => request.post(`/paralegal/caseSourceServerV2/completeCaseServer/${params.caseSourceServerV2Id}`);
/* 获取法律意见书*/
export const lawWrittenOpinionGetLawWrittenOpinion = (params) => request.post("/paralegal/lawWrittenOpinion/getLawWrittenOpinion", params);
/* 发送法律意见书*/
export const lawWrittenOpinionSend = (params) => request.post("/paralegal/lawWrittenOpinion/send", params);
/* 保存Im中的服务备注*/
export const modifyServerV2Remarks = (params) => request.post("/law-cloud/caseSourceServerV2/modifyServerV2Remarks", params);
/* 获取Im中的服务备注*/
export const getServerV2Remarks = (params) => request.post(`/law-cloud/caseSourceServerV2/getServerV2Remarks/${params.caseSourceServerV2Id}`, params);
