<template>
	<div class="bg-[#FFFFFF]">
		<div class="px-[24px] pt-[24px]">
			<app-select
				:select-list="goodAtType"
				@change="handleSelect"
				:default-value="query.typeValue"
			/>
		</div>
		<div class="px-[24px] pt-[16px] pb-[24px]">
			<xy-page
				:is-margin="false"
				ref="page"
				:column="tableColumn"
				:request="request"
				:query="query"
         :default-loading-page="false"
			>
				<template #pageMiddleContainer>
					<div class="flex">
            <lead-package-display>
              <div class="text-[14px] text-[#333333] mb-[16px]  pr-[40px]">

                律所剩余可抢即时咨询：<span class="text-[#3887F5]">{{
                  remainingGrabTimes.remainQaMessageCount
                }}</span
              >条
              </div>
            </lead-package-display>
            <div class="text-[14px] text-[#333333] mb-[16px]">
              剩余可使用法临币：<span class="text-[#3887F5]">{{
                priceNumber(~~remainingGrabTimes.remainAmount + ~~remainingGrabTimes.giftRemainAmount)
              }}</span
            >个
            </div>
          </div>
				</template>
				<template #handleButton>
					<el-table-column fixed="right" width="90px" label="操作">
						<template #default="{ row }">
							<app-button
								v-if="row.status !== 3"
								style-type="text"
								type="primary"
								size="medium"
								@click="handleGrabOrder({...row,amount: row.serverAmount, isCaseSource: false })"
								>抢单
							</app-button>
						</template>
					</el-table-column>
				</template>
			</xy-page>
		</div>
    <!--  1.0.9抢单流程  -->
    <grab-order-pop :remaining-grab-times="remainingGrabTimes" :show.sync="dialogVisible" :detail="currentParams" @confirmPop="submitPop" />
	</div>
</template>

<script>
import XyPage from "components/xy-page/index.vue";
import { tableColumn } from "./index.js";
import AppSelect from "components/AppSelect/index.vue";
import AppButton from "components/appButton/index.vue";
import { caseRemainTimes,  caseWdGrabV2, qaMessagePage } from "@/api/clues";
import { dataDetailList, lcNewQaMessageCount } from "@/api/common";
import refreshTheDatatipPeriodically from "@/minixs/refreshTheDatatipPeriodically.js";
import LeadPackageDisplay from "components/leadPackageDisplay/index.vue";
import { priceNumber } from "../../../../utils/toolMethods";
import GrabOrderPop from "views/leadManagement/clueSquare/cluesToTheSourceOfTheCase/grabOrderPop.vue";
import { isLeadPackageDisplay } from "components/leadPackageDisplay";
import { judgeBalance } from "views/leadManagement/clueSquare/cluesToTheSourceOfTheCase";
import handleGrabOrder from "@/minixs/handleGrabOrder";

export default {
  name: "InstantConsultation",
  components: { GrabOrderPop, LeadPackageDisplay, AppButton, AppSelect, XyPage },
  mixins: [refreshTheDatatipPeriodically({ updateDataApi: lcNewQaMessageCount, msg: "线索广场更新num条新数据，请立即刷新查看", key: "newQaMessageCount" }), handleGrabOrder],
  data() {
    return {
      request: {
        getListUrl: data => this._updateData(qaMessagePage, data),
      },
      tableColumn,
      query: {
        typeValue: "",
      },
      /** 剩余可抢单次数 */
      remainingGrabTimes: {},
      /** 擅长类型 */
      goodAtType: [],
      dialogVisible: false,
      currentParams: {}
    };
  },
  created() {
    this.getGoodAtType();
    this.getCacheData();
  },
  activated() {
    this.getRemainingGrabTimes();
  },
  methods: {
    priceNumber,
    submitPop(item){
      caseWdGrabV2({ id: this.currentParams.qaMessageId, accountType: ~~item }).then((res) => {
        // if(res.showGrabMessage) this.$message.success(res.showGrabMessage);
        this.$message.success("抢单成功，快去跟进吧~");
        this.getRemainingGrabTimes();
        this.$store.dispatch("setIsTableRefresh");
      });
    },
    /** 查询可抢剩余次数 */
    getRemainingGrabTimes() {
      caseRemainTimes().then(res => {
        this.remainingGrabTimes = { ...res, remainCaseCount: res.remainQaMessageCount };
      });
    },
    /** 获取擅长类型 */
    getGoodAtType() {
      dataDetailList({
        groupCode: "LAWYER_SPECIALITY",
      }).then(res => {
        this.goodAtType = res;

        this.goodAtType.unshift({
          label: "全部",
          value: "",
        });
      });
    },
    /** 选择了擅长类型 */
    handleSelect(item) {
      this.query.typeValue = item.value;
      this.$store.dispatch("setIsTableRefresh");
    },
    /** 获取缓存的数据 */
    getCacheData() {
      const cacheKey = `xy-table-search:${this.$route.fullPath}`;
      const cacheData = localStorage.getItem(cacheKey);
      if (cacheData) {
        this.query.typeValue = JSON.parse(cacheData).typeValue || "";
      }
      this.$nextTick(() => {
        this.$store.dispatch("setIsTableRefresh");
      });
    },
  },
};
</script>

<style scoped lang="scss"></style>
