
import { removeStorage } from "utils/storage";
import { goodsList } from "@/api/pay";

export default {
  state: {
    tableId: "", // 表格id
    isRouterRefresh: true, // 是否刷新页面
    isTableRefresh: "", // 是否刷新表格
    requestLoading: true, // 请求是否需要loading
    breadcrumb: [], // 面包屑名称
    showBackButton: false, // 是否显示面包屑上的返回按钮
    showDetailPage: false, // 是否显示详情页
    cachedViews: [], // 需要缓存的页面,值为vue页面中的name值，必须一 一对应否则无效
    tableExpends: "", // 表格需要展开的数据
    isHandlerRest: true, // 表格是否重置
    isHandlerExamine: false, // 是否更新审核人状态 2.2.5
    isPayPc: false, // 是否线上支付
    /* 计算器弹窗配置*/
    calculator: {
      isShow: false,
      showIndex: 0,
    },
    // table配置
    tableConfig: {
      /* 每页条数*/
      // pageSize: 10,
      /* 当前页*/
      currentPage: 1
    }

  },
  getters: {
    isRouterRefresh: (state) => state.isRouterRefresh,
    isTableRefresh: (state) => state.isTableRefresh,
    requestLoading: state => state.requestLoading,
    breadcrumb: state => state.breadcrumb,
    showBackButton: state => state.showBackButton,
    showDetailPage: state => state.showDetailPage,
    cachedViews: state => state.cachedViews,
    tableExpends: state => state.tableExpends,
    isHandlerRest: state => state.isHandlerRest,
    isHandlerExamine: state => state.isHandlerExamine,
    tableId: state => state.tableId,
    isPayPc: state => state.isPayPc,
    calculator: state => state.calculator,
    /* table配置*/
    getTableConfig: state => state.tableConfig,
  },
  mutations: {
    SET_TABLE_ID(state, data) {
      state.tableId = data;
    },
    SET_IS_ROUTER_REFRESH(state, data) {
      state.isRouterRefresh = false;
      setTimeout(() => {
        state.isRouterRefresh = true;
      }, 0);
    },
    SET_IS_TABLE_REFRESH(state, data) {
      state.isTableRefresh = data || state.tableId;
      setTimeout(() => {
        state.isTableRefresh = "";
      }, 0);
    },
    // 设置当前请求是否需要请求
    SET_REQUEST_LOADING: (state, status) => {
      state.requestLoading = status;
    },
    // 设置当前请求是否需要请求
    SET_BREADCRUMB: (state, data) => {
      state.breadcrumb = data;
    },
    // 设置是否显示返回按钮
    SET_SHOW_BACK_BUTTON: (state, data) => {
      state.showBackButton = data;
    },
    // 设置是否显示返回按钮
    SET_SHOW_DETAIL_PAGE: (state, data) => {
      state.showDetailPage = data;
    },
    // 设置展示的表格的数组，针对的是表格tree
    SET_SHOW_TABLE_EXPENDS: (state, data) => {
      state.tableExpends = data;
    },
    // 设置表格重置
    SET_IS_TABLE_REST(state, data) {
    //   state.isHandlerRest = false
      state.isHandlerRest = data || false;
      setTimeout(() => {
        state.isHandlerRest = true;
      }, 0);
    },
    SET_IS_HEADER_EXAMINE: (state, data) => {
      state.isHandlerExamine = data;
    },
    SET_IS_PAY_PC: (state, data) => {
      state.isPayPc = data;
    },
    SET_CALCULATOR: (state, data) => {
      state.calculator = {
        ...state.calculator,
        ...data
      };
    },
    /* table配置 控制当前table的配置*/
    SET_TABLE_CONFIG(state, config = {}){
      state.tableConfig = {
        ...state.tableConfig,
        ...config,
        key: config.key || state.tableId,
      };
    },
  },
  actions: {
    setIsRouterRefresh({ commit, state, getters }, data) {
      commit("SET_IS_ROUTER_REFRESH", data);
    },
    setIsTableRefresh({ commit, state, getters }, data) {
      commit("SET_IS_TABLE_REFRESH", data);
    },
    setRequestLoading: ({ commit }, status) => {
      commit("SET_REQUEST_LOADING", status);
    },
    setBreadcrumb: ({ commit }, data) => {
      commit("SET_BREADCRUMB", data);
    },
    setShowBackButton: ({ commit }, data) => {
      commit("SET_SHOW_BACK_BUTTON", data);
    },
    setShowDetailPage: ({ commit }, data) => {
      commit("SET_SHOW_DETAIL_PAGE", data);
      if (!data) {
        // 关闭详情页同时删除详情id
        commit("SET_SHOW_BACK_BUTTON", false);
        removeStorage("detailId");
      }
    },
    setShowTableExpends: ({ commit }, data) => {
      commit("SET_SHOW_TABLE_EXPENDS", data);
    },
    setIsTableRest: ({ commit }, data) => {
      commit("SET_IS_TABLE_REST", data);
    },
    setIsExamine: async ({ commit }, data) => {
      commit("SET_IS_HEADER_EXAMINE",  data);
    },
  }
};
