<template>
	<div class="bg-[#FFFFFF] p-[24px]">
		<!-- 标签页 -->
		<div class="mb-[16px] flex justify-between">
			<app-select
				:select-list="tabOptions"
				:default-value="invoiceType"
				@change="handleTabChange"
			/>
      <app-button icon="icon-record" @click="goToInvoiceRecord" style-type="text">开票记录</app-button>
		</div>

		<!-- 筛选区域 -->
		<div class="mb-[16px] flex items-center">
			<el-checkbox @change="handleSelectAll" v-model="selectAll">全选</el-checkbox>
			<el-checkbox @change="getInvoiceList" v-model="onlyDisplayBillableItems"
				>只看可开票</el-checkbox
			>
		</div>

		<!-- 列表内容 -->
		<div class="space-y-[16px]">
			<invoice-list-item
				v-for="item in invoiceList"
				:key="item.bizId"
				:data="item"
				:invoice-type="invoiceType"
				v-model="selectedInvoiceIds"
				title="线索包"
				amount-label="受付金额"
				@check-change="handleItemCheckChange"
			/>
		</div>

		<!-- 分页 -->
		<div
			class="flex justify-end items-center mt-[24px] border-0 border-t-[1px] border-[#EEEEEE] border-solid"
		>
			<xy-pagination
				background
				@handleCurrentChange="handleCurrentChange"
				@handleSizeChange="handleSizeChange"
				:current.sync="currentPage"
				:page-size.sync="pageSize"
				:total="total"
			/>
		</div>

		<!-- 底部按钮区域 -->
		<div class="flex items-center justify-end mt-[24px] pt-[16px]">
			<div class="text-[14px] text-[#333333] mr-[16px]">
				已选择 <span class="text-[#EB4738]">{{ selectedCount }}</span> 个，开票总金额：<span
					class="text-[#EB4738]"
					>¥{{ priceNumber(selectedTotalAmount) }}</span
				>
			</div>
			<app-button :disabled="selectedCount === 0" @click="handleNextStep">下一步</app-button>
		</div>
		<!-- 使用提取的发票弹窗组件 -->
		<invoice-popup
			:visible.sync="invoiceDialogVisible"
			:total-amount="selectedTotalAmount"
			:biz-ids="selectedInvoiceIds"
			ref="invoicePopup"
			@success="handleSubmitInvoice"
			:invoice-type="invoiceType"
		/>
	</div>
</template>

<script>
import { institutionInvoicePage } from "@/api/invoice";
import AppSelect from "@/components/AppSelect/index.vue";
import AppButton from "@/components/appButton/index.vue";
import XyPagination from "@/components/xy-pagination/index.vue";
import { priceNumber } from "@/utils/toolMethods";
import InvoiceListItem from "@/views/invoiceManagement/invoice/components/InvoiceListItem.vue";
import InvoicePopup from "@/views/invoiceManagement/invoice/components/InvoicePopup.vue";
import { turnToInvoiceRecord } from "@/utils/turnPage";

export default {
  name: "InvoicePage",
  components: {
    AppSelect,
    AppButton,
    XyPagination,
    InvoicePopup,
    InvoiceListItem,
  },
  data() {
    return {
      invoiceType: "1",
      selectAll: false,
      onlyDisplayBillableItems: false,
      selectedInvoiceIds: [],
      currentPage: 1,
      pageSize: 10,
      total: 999,
      tabOptions: [
        { label: "线索包充值", value: "1" },
        { label: "法临币抢单", value: "2" },
      ],
      invoiceList: [],
      // 发票弹窗相关
      invoiceDialogVisible: false,
    };
  },
  computed: {
    selectedCount() {
      return this.selectedInvoiceIds.length;
    },
    selectedTotalAmount() {
      if (this.selectedInvoiceIds.length === 0) return 0;

      return this.selectedInvoiceIds.reduce((total, id) => {
        const selectedItem = this.invoiceList.find(item => item.bizId === id);
        return total + (selectedItem ? selectedItem.amount : 0);
      }, 0);
    },
    // 可开票的项目列表
    billableItems() {
      return this.invoiceList.filter(item => item.invoiceStatus === 0);
    },
  },
  created() {
    this.getInvoiceList();
  },
  methods: {
    priceNumber,
    getInvoiceList() {
      const params = {
        selectAll: this.selectAll ? 1 : 0,
        onlyDisplayBillableItems: this.onlyDisplayBillableItems ? 1 : 0,
      };

      institutionInvoicePage({
        currentPage: this.currentPage,
        pageSize: this.pageSize,
        invoiceType: this.invoiceType,
        ...params,
      }).then(data => {
        this.invoiceList = data.records;
        this.total = data.total;

        // 如果是全选状态，则选中所有可开票的项目
        if (this.selectAll) {
          this.selectAllBillableItems();
        } else {
          // 保留已选中且在当前页面中的项目
          this.selectedInvoiceIds = this.selectedInvoiceIds.filter(id =>
            this.invoiceList.some(item => item.bizId === id),
          );
        }
      });
    },
    // 处理全选操作
    handleSelectAll(val) {
      if (val) {
        this.selectAllBillableItems();
      } else {
        this.selectedInvoiceIds = [];
      }
    },
    // 选择所有可开票的项目
    selectAllBillableItems() {
      // 获取所有可开票项目的ID
      const billableIds = this.invoiceList
        .filter(item => item.invoiceStatus === 0)
        .map(item => item.bizId);

      // 更新已选择的ID列表，避免重复
      this.selectedInvoiceIds = Array.from(new Set([...this.selectedInvoiceIds, ...billableIds]));
    },
    // 处理单个项目的选中状态变化
    handleItemCheckChange() {
      // 如果所有可开票项目都被选中，则更新全选状态
      if (this.billableItems.length > 0) {
        const allBillableSelected = this.billableItems.every(item =>
          this.selectedInvoiceIds.includes(item.bizId),
        );
        this.selectAll = allBillableSelected;
      } else {
        this.selectAll = false;
      }
    },
    /** 清除全选 */
    clearSelectAll() {
      this.selectAll = false;
      this.selectedInvoiceIds = [];
    },
    handleTabChange(item) {
      this.invoiceType = item.value;
      this.currentPage = 1;
      this.clearSelectAll();
      this.getInvoiceList();
    },
    handleCurrentChange(val) {
      this.currentPage = val;
      this.clearSelectAll();
      this.getInvoiceList();
    },
    handleSizeChange(val) {
      this.pageSize = val;
      this.clearSelectAll();
      this.getInvoiceList();
    },
    handleNextStep() {
      this.invoiceDialogVisible = true;
    },
    // 处理发票提交
    handleSubmitInvoice(invoiceForm) {
      console.log("提交发票申请：", invoiceForm);
      this.clearSelectAll();
      this.getInvoiceList();
    },
    /* 前往发票记录 */
    goToInvoiceRecord(){
      turnToInvoiceRecord();
    }
  },
};
</script>
