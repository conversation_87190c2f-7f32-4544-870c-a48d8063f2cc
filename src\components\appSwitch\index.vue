<template>
  <el-switch
    v-model="getValue"
    :inactive-text="showInactiveText"
    :active-text="showActiveText"
    @change="handleChange"
    v-bind="$attrs"
    class="app-switch" />
</template>

<script>
export default {
  name: "AppSwitch",
  props: {
    // 双向绑定的值
    value: {
      type: Boolean,
      default: false
    },
    // 关闭时的文字描述
    inactiveText: {
      type: String,
      default: "关"
    },
    // 打开时的文字描述
    activeText: {
      type: String,
      default: "开"
    },
    // switch 打开时的值
    activeValue: {
      type: [Boolean, Number, String],
      default: true
    },
    // switch 关闭时的值
    inactiveValue: {
      type: [Boolean, Number, String],
      default: false
    },
  },
  computed: {
    getValue: {
      get(){
        return this.value;
      },
      set(val){
        this.$emit("input", val);
      }
    },
    showInactiveText(){
      return this.value === this.inactiveValue ? this.inactiveText : " ";
    },
    showActiveText(){
      return this.value === this.activeValue ? this.activeText : " ";
    }
  },
  methods: {
    // 处理开关状态变化
    handleChange(val) {
      this.$emit("change", val);
    }
  }
};
</script>

<style lang="scss" scoped>
// 自定义Switch组件样式
.app-switch{
    &.is-checked {
      ::v-deep .el-switch__core{
        background-color: #3887F5 !important;
      }
    }
  ::v-deep{
    .el-switch__core{
      background-color: #CCCCCC !important;
    }
    .el-switch__core:after {
      z-index: 999 !important;
    }
    .el-switch__label--right {
      position: absolute;
      left: -4px;
      color: #FFFFFF;
      z-index: 1 !important;
      font-size: 11px;
    }
    .el-switch__label--left {
      position: absolute;
      left: 21px;
      color: #FFFFFF;
      z-index: 1;
      font-size: 11px;
    }
    .el-switch__label * {
      font-size: 12px;
    }
  }
}
</style>
