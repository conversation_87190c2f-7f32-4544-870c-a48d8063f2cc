import Vue from "vue";
import ElementUI from "element-ui";
import "./assets/css/iconfont/iconfont.css";
import "element-ui/lib/theme-chalk/index.css";
import "element-ui/lib/theme-chalk/display.css";
import "./assets/css/element-variables.scss";
import "./styles/index.scss";
import App from "./App.vue";
import router from "router/routerPermission";
import store from "./store";
// import '@/mock/index'
// 图片预览
import preview from "vue-photo-preview";
import "vue-photo-preview/dist/skin.css";
import * as filters from "./filters"; // global filters
import basics from "utils/basicsMethods";
import global from "utils/constant";
import VueClipBoard from "vue-clipboard2";
import "@/directive/root-btns/index";
import BaiduMap from "vue-baidu-map";
import "@/directive/click-await/index.js";
// import imEventBus from "utils/imEventBus.js";
import contextmenu from "components/contextmenu/index.js";
import "@/assets/tailwindout.css";

// import imConfig from './utils/imConfig'
// imEventBus();
const options = {
  fullscreenEl: false, // 控制是否显示右上角全屏按钮
  closeEl: false, // 控制是否显示右上角关闭按钮
  topToClose: true, // 点击滑动区域应关闭图库
  captionEl: false,
  zoomEl: true, // 控制是否显示放大缩小按钮
  shareEl: false, // 控制是否显示分享按钮
  counterEl: false, // 控制是否显示左上角图片数量按钮
  arrowEl: true,
  preloaderEl: true,
  showHideOpacity: false, // 背景opacity和图像scale将被动画化（图像不透明度始终为1）
  timeToIdle: 100,
  showAnimationDuration: 0,
  bgOpacity: "0.5",
  barsSize: { top: 100, bottom: 100 }
};
Vue.use(contextmenu);
Vue.use(preview, options);
Vue.use(ElementUI, { size: "small", zIndex: 3000 });
Vue.config.productionTip = false;
Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key]);
});
Vue.use(basics);
Vue.prototype.$global = global;
Vue.use(VueClipBoard);
Vue.use(BaiduMap, {
  ak: "6KgGQfMPO3tSpnXk8QgCvZNDkoUIBG4O"
});
// Vue.use(imConfig)
if(!String.prototype.replaceAll){
  try {
    const  replaceAll =  require("string-replace-all-ponyfill");
    String.prototype.replaceAll = function () {
      return replaceAll.default(this, ...arguments);
    };
  }catch (e) {
    console.log(e);
  }
}

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount("#app");
