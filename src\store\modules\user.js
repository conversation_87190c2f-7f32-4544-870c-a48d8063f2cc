import { getLoginUserInfo, loginOut, smsLogin } from "@/api/login";
import { getToken, removeImToken, removeToken, setToken } from "utils/storage";
import router, { createRoutes } from "@/router/index";
import { createWs, wsClose } from "utils/creat-ws.js";
import { lawyerCertStatus } from "utils/config";
import basics from "utils/basicsMethods";

// import store from 'store/index'

const user = {
  state: {
    isCollapse: false, // 是否收缩菜单
    /* 菜单 */
    menusList: [],
    permissionMenu: [], // 登录用户的权限菜单
    hasLogin: false, // 判断是否登录，不能token判断的原因是，某些接口可能无须带token
    token: getToken(),
    imToken: "",
    visitorToken: "",
    userInfo: {},
    adminUserId: 1, // 超级管理员角色id
    adminRoleId: 1, // 默认系统用户id
    userFirstLoginGuide: false,
    btnRoots: {}, // 权限按钮
    pdStatus: {
      // 0.停止派单 1.派单中
      assignStatus: 0
    },
    // 派单状态
    casePushNum: 10,
    /** 认证弹窗 */
    certificationPopup: false,
    certificationData: null,
    certificationSubmitCallBack: null,
  },
  getters: {
    getPermissionMenu: (state) => state.permissionMenu,
    getIsCollapsed: (state) => state.isCollapse,
    token: state => state.token,
    hasLogin: state => state.hasLogin,
    userInfo: state => state.userInfo,
    /**
		 * 获取认证状态
     *0.未认证 1.待审核,2.已通过,3.未通过
		 * @param state
		 * @return {number}
		 */
    getCertStatus: (state) => basics.isNull(state.userInfo.certStatus) ? 0 : Number(state.userInfo.certStatus),
    /**
     * 是否认证
     * @param state
     * @param getters
     * @return {boolean} true 已认证 false 未认证
     */
    lawyerCertStatus: (state, getters) => {
      return lawyerCertStatus(getters.getCertStatus);
    },
    getMenusList: state => state.menusList,
    pdStatus: state => state.pdStatus,
    getCasePushNum: state => state.casePushNum,
    /* 是不是线索包用户*/
    isItALeadPackageUser: state => state.userInfo.settleInType === 1,
    isAdministrator: state => state.userInfo.isAdmin === 1 /* 是否是管理员 */,
    getCertificationPopup(state){
      return state.certificationPopup;
    },
    getCertificationData(state){
      return state.certificationData;
    },
    /* 是不是特殊的客服 要隐藏logo客服之类的*/
    /**
     * 显示类型(0:律客云 1:不显示律客云)
     */
    getIsSpecialCustomerService(state){
      return state.userInfo.showType === 1;
    }
  },
  mutations: {
    SET_MENUS_LIST(state, list) {
      console.log(list);
      state.menusList = list;
    },
    SET_IS_COLLAPSE(state, data) {
      state.isCollapse = data;
    },
    SET_TOKEN: (state, token) => {
      state.token = token;
    },
    SET_IM_TOKEN: (state, token) => {
      state.imToken = token;
    },
    SET_VISITOR_TOKEN: (state, token) => {
      state.visitorToken = token;
    },
    SET_LOGIN: (state, hasLogin) => {
      state.hasLogin = hasLogin;
    },
    SET_USER_INFO: (state, info = {}) => {
      state.userInfo = info;
    },
    SET_PD_STATUS: (state, info = {}) => {
      state.pdStatus = info;
    },
    SET_CASE_PUSH_NUMBER: (state, data) => {
      state.casePushNum = data;
    },
    SET_CERTIFICATION_POPUP(state, data) {
      state.certificationPopup = data;
    },
    SET_CERTIFICATION_DATA(state, data) {
      state.certificationData = data;
    },
    SET_CERTIFICATION_SUBMIT_CALL_BACK(state, data) {
      state.certificationSubmitCallBack = data;
    },
    CALL_CERTIFICATION_SUBMIT_CALL_BACK(state){
			state.certificationSubmitCallBack?.();
    }
  },

  actions: {
    // 登录
    login({ commit }, formData) {
      return new Promise((resolve, reject) => {
        smsLogin(formData).then(result => {
          commit("SET_TOKEN", result.token);
          setToken(result.token);
          /* 判断是不是新用户弹窗 是否新人首次登录（0.否，1.是）*/
          if(result.newUserFirstLogin === 1){
            commit("SET_FIRST_LOGIN_GUIDE", true);
          }
          createWs();
          // setImToken(result.imToken);
          // imLogin();
          resolve(result);
        }).catch(error => {
          reject(error);
        });
      });
    },
    // 登出
    loginOut({ commit }) {
      loginOut().then(() => {
      }).finally(() => {
        try {
          window.WebIM.conn.close();
        } catch (e) {
          console.log(e);
        }
        // reject(error)
        commit("SET_TOKEN", "");
        commit("SET_IM_TOKEN", "");
        commit("SET_USER_INFO", {});
        commit("SET_LOGIN", false);
        // 清除菜单历史访问记录
        commit("DEL_ALL_VISITED_VIEWS");
        commit("DEL_ALL_CACHED_VIEWS");
        removeToken();
        removeImToken();
        // clearStorage()
        wsClose();
        // 清空菜单
        commit("SET_MENUS_LIST", []);
        router.push("/login");
        setTimeout(() => {
          /* 刷新*/
          location.reload();
        }, 500);

      });
    },
    setIsCollapse({
      commit,
      state,
      getters
    }, data) {
      commit("SET_IS_COLLAPSE", data);
    },
    // 获取用户信息
    getUserInfo({ commit }) {
      // 如果没有token则不执行
      if (!getToken()) return;

      return new Promise((resolve, reject) => {
        getLoginUserInfo().then(res => {
          console.log(res, "res");
          commit("SET_USER_INFO", res);
          commit("SET_LOGIN", true);

          // 通过后端数据创建路由
          const r = createRoutes(res.list);

          // 动态添加路由
          router.addRoutes(r);

          // 保存菜单
          commit("SET_MENUS_LIST", r[0].children);

          // 将路由抛进去
          resolve(r[0].children);
        }).catch(error => {
          reject(error);
        });
      });
    },
    // 前端 登出
    FedLogOut({ commit }) {
      return new Promise(resolve => {
        try {
          window.WebIM.conn.close();
        } catch (e) {
          console.log(e);
        }
        commit("SET_TOKEN", "");
        // commit('SET_USER_ID', '')
        commit("SET_USER_INFO", {});
        commit("SET_LOGIN", false);
        removeImToken();
        removeToken();
        // 清空菜单
        commit("SET_MENUS_LIST", []);

        // removeCacheTable()
        resolve();
      });
    }
  }
};

export default user;
