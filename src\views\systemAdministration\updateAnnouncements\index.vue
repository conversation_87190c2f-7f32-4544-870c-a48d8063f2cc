<template>
  <div class="wrap-box">
    <!--  筛选    -->
    <div class="list-search">
      <label>发布时间</label>
      <el-date-picker
        class="!w-[360px]"
        v-model="time"
        type="datetimerange"
        value-format="yyyy-MM-dd HH:mm:ss"
        start-placeholder="开始日期"
        end-placeholder="结束日期" />

      <label class="list-search-status">查看状态</label>
      <el-select class="w-[160px]" v-model="statusRead" clearable placeholder="请选择">
        <el-option
          v-for="item in readStatus"
          :key="item.value"
          :label="item.label"
          :value="item.value" />
      </el-select>
      <app-button @click="handleSearch" class="ml-[40px]">查询</app-button>
      <app-button class="ml-[8px]" type="info" @click="handleReset">重置</app-button>
    </div>
    <div class="list-box flex">
      <div class="list-left translate-x-0">
        <div class="list-wap-c pr">
         <div v-if="list.length <1" class="none-data tc">
           <img src="@/assets/images/empty.png" />
           <p class="fs14 text-[#666666]">当前条件下暂无数据～</p>
         </div>
         <div v-else>
           <ul>
             <li v-for="(item,i) in list" :key="i"
                 :class="['flex flex-align-center', Number(item.id) === Number(detailId) && 'checked']"
                 @click="handleListItem(i)">
               <div class="list-content">
                 <div class="flex flex-space-between text-[#999999] ">
                   <div class="flex items-center">
                     <label class="font-16 text-[#333333] max-w-[250px] text-ellipsis pr list-content-tit ">{{ item.title }} </label>
                     <i v-if="!item.read"  class="w-[8px] h-[8px] ml-[4px] bg-[#EB4738] rounded-[8px] border-[1px] border-solid border-[#FFFFFF]" />
                   </div>
                   <label class="fs12 text-[#999999] ">{{ item.pubTime }}</label>
                 </div>
                 <p class="line1 fs14 text-[#666666] list-content-desc">{{ item.description }}</p>
               </div>
             </li>
           </ul>
           <!--    分页      -->
           <div  class="fixed bg-[#ffffff] border-0 border-[#EEEEEE] border-solid  border-t-[1px] bottom-0 left-0 right-0 px-[24px] pt-[12px] pb-[24px] ">
             <el-pagination
               @size-change="handleSizeChange"
               @current-change="handleCurrentChange"
               :current-page.sync="currentPage"
               :page-size="pageSize"
               :pager-count="5"
               layout="prev, pager, next, jumper"
               :total="total" />
           </div>
         </div>
       </div>
     </div>
      <div class="list-right list-wap-c" v-if="!basics.isObjNull(detail)">
        <div class="list-right-tit">
          <h5>{{ detail.title }}</h5>
          <p>{{ detail.pubTime }}</p>
        </div>
        <div class="list-right-content">
          <div class="list-right-item">
            <label class="fs14 text-[#333333]">{{ detail.description }}</label>
          </div>
          <div id="updateAnnouncementsContainer" v-html="detail.content" />
          <preview-the-image container="#updateAnnouncementsContainer" />
        </div>
      </div>
    </div>
  </div>
</template>

<script>


import { lcNoticeDetailById, lcNoticePage } from "@/api/workbenchManagement";
import AppButton from "components/appButton/index.vue";
import { readStatus } from "@/enum/common.js";
import PreviewTheImage from "components/PreviewTheImage/index.vue";

export default {
  name: "UpdateAnnouncements",
  components: { PreviewTheImage, AppButton },
  data() {
    return {
      time: [],
      statusRead: "",
      readStatus,
      currentPage: 1,
      pageSize: 10,
      total: 0,
      list: [],

      detailId: null,
      detail: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    test(event){
      console.dir(event.target);
    },
    /** 获取列表**/
    getList() {
      const data = {
        currentPage: this.currentPage,
        pageSize: this.pageSize,
        readStatus: this.statusRead
      };
      if (this.time) {
        data.pubTimeStart = this.time[0];
        data.pubTimeEnd = this.time[1];
      }

      lcNoticePage(data).then((r) => {
        let findIndex = 0;
        const { records = [] } = r;
        this.list = records;
        this.total = r.total;
        if (this.basics.isArrNull(records)) {
          findIndex = -1;
        }else if (this.$route.query.id) {
          findIndex = records.findIndex((s) => Number(s.id) === Number(this.$route.query.id));
        }
        this.getDetail(findIndex);
      });
    },
    /** 获取详情 **/
    getDetail(index) {
      if(index < 0) {
        this.detailId = null;
        this.detail = {};
        return;
      }
      const item = this.list[index];
      this.detailId = item.id;
      /* 更新全局的通知数量*/
      if(item.read === 0){
        this.$store.commit("REDUCE_NOTICE_COUNTS", "noticeUnreadNum");
      }
      this.$set(item, "read", 1);
      lcNoticeDetailById({ id: this.detailId }).then(r => {
        console.log(r, "详情");
        if (r.pic) r.imgs = r.pic.split(",");
        this.detail = r;
      });
    },
    /* 搜索*/
    handleSearch(){
      this.currentPage = 1;
      this.getList();
    },
    handleSizeChange(val) {
      console.log(val, 9);
    },
    /* 重置 */
    handleReset(){
      this.time = [];
      this.statusRead = "";
      this.currentPage = 1;
      this.getList();
    },
    handleCurrentChange(val) {
      console.log(val, 8);
      this.currentPage = val;
      this.getList();
    },
    /** 点击列表的单条数据**/
    handleListItem(index) {
      this.getDetail(index);
    }
  }
};
</script>

<style scoped lang="scss">
.wrap-box {
  background: #FFFFFF;
  padding: 24px;

  .list-search {
    label {
      margin-right: 8px;
    }

    .list-search-status {
      margin-left: 40px;
    }
  }

  .list-wap-c{
    height: calc(100vh - 237px);
    overflow-y: auto;
  }
  .list-box {
    border: 1px solid #EEEEEE;
    border-radius: 5px;
    margin-top: 24px;

    .list-left {
      width: 462px;
      border-right: 1px solid #EEEEEE;
      box-sizing: border-box;
      .list-wap-c{
        box-sizing: border-box;
        padding-bottom: 74px;
      }

      li {
        padding: 16px 24px;
        cursor: pointer;

        &.checked {
          background: #F5F5F7;
        }

        &:hover {
          background: #F5F5F7;
        }

        .list-content {
          width: 100%;

          &-tit {
            font-weight: 600;
          }

          &-desc {
            margin-top: 6px;
          }
        }

        img {
          width: 48px;
          height: 48px;
          margin-right: 12px;
          background: #FFFFFF;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #EEEEEE;
        }
      }

      .pagination {
        padding: 12px 24px 24px;
        border-top: 1px solid #EEEEEE;
        bottom: 0;
        left: 0;
        right: 0;
      }
    }

    .list-right {
      width: calc(100% - 462px);
      padding: 24px;
      box-sizing: border-box;

      &-content {
        ::v-deep {
          img {
            max-width: 100%;
          }
        }
      }

      &-tit {
        margin-bottom: 36px;
        padding-bottom: 24px;
        border-bottom: 1px dotted #DDDDDD;

        h5 {
          font-weight: 600;
          font-size: 23px;
          color: #333333;
        }

        p {
          font-weight: 400;
          font-size: 12px;
          color: #999999;
          margin-top: 8px;
        }
      }

      &-item {
        padding-bottom: 24px;

        .desc {
          font-size: 12px;
          color: #F78C3E;
          margin: 12px 0 16px;
        }
      }

      .img-box {
        max-width: 100%;
      }

      .bottom-img {
        width: 100%;
      }
    }
  }
}

.title {
  font-weight: 500;
  font-size: 16px;
  color: #111111;
  padding-left: 9px;
  position: relative;
  margin-top: 12px;
  margin-bottom: 10px;

  &:after {
    position: absolute;
    content: "";
    width: 3px;
    height: 18px;
    background: #EB4738;
    left: 0;
    top: 2px;
  }
}

.text-red {
  span {
    color: #EB4738 !important;
  }
}

.none-data {
  padding-top: 72px;

  img {
    width: 160px;
    height: 120px;
  }
}

</style>
