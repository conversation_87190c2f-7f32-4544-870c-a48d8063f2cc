<template>
  <div>
    <permissions-header>
      菜单
      <template #after>
        <app-button :disabled="basics.isNull(personnelId)||disabled" @click="confirm">保存</app-button>
      </template>
    </permissions-header>
    <div class="list-container px-[10px]">
      <el-tree
        :data="menuList"
        show-checkbox
        default-expand-all
        node-key="id"
        ref="tree"
        highlight-current
        :props="treeProps" />
    </div>
  </div>
</template>

<script>
import PermissionsHeader from "views/systemAdministration/permissionsManagement/components/PermissionsHeader.vue";
import AppButton from "components/appButton/index.vue";
import { lcInstitutionStaffPermissionConfig } from "@/api/system";

export default {
  name: "MenuList",
  components: { AppButton, PermissionsHeader },
  inject: ["getPersonnelId"],
  props: {
    /* 菜单*/
    menuList: {
      type: Array,
      default: () => []
    },
    /* tree的参数*/
    treeProps: {
      type: Object,
      default: () => ({})
    },
    disabled: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    /* 勾选的菜单id*/
    selectIds: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    /* 人员id*/
    personnelId() {
      return this.getPersonnelId();
    }
  },
  watch: {
    selectIds: {
      handler: "handleSelectIdsChange",
      immediate: true,
      deep: true
    }
  },
  methods: {
    handleSelectIdsChange(){
      this.$nextTick(() => {
        this.$refs.tree.setCheckedKeys(this.selectIds);
      });
    },
    confirm(){
      if(this.basics.isNull(this.personnelId)) return;
      lcInstitutionStaffPermissionConfig({
        id: this.personnelId,
        moduleIdList: [...this.$refs.tree.getHalfCheckedKeys(), ...this.$refs.tree.getCheckedKeys()]
      }).then(() => {
        this.$message.success("保存成功");
        this.$emit("menuConfirm");
      });
    }
  }
};
</script>

<style scoped lang="scss">
.list-container{
  overflow-y: auto;
  height: calc(100vh - 191px);
}
</style>
