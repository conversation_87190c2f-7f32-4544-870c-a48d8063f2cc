<template>
	<app-popup
		title="客服"
		width="560px"
		:show-title="false"
		:hide-footer="true"
		:visible.sync="visible"
    class="kf"
	>
		<div
			class="relative rounded-[8px] z-10 w-[560px] h-[390px] bg-[length:100%_100%]"
			:style="{ backgroundImage: `url(${require('@/assets/images/bg.png')})` }"
		>
			<img
				src="@/assets/images/close.png"
				class="w-[40px] h-[40px] absolute right-0 -top-[56px] cursor-pointer"
				alt=""
				@click="visible = false"
			/>
			<img
				class="mx-auto absolute left-[58px] top-[192px] w-[120px] h-[120px] rounded-[5px] block"
				:src="serviceQR.lkyKfQrCode"
				alt=""
			/>
		</div>
	</app-popup>
</template>

<script>
import AppPopup from "components/appPopup/index.vue";
import { commonConfigKey } from "@/api/common";

export default {
  name: "FunctionMenuServiceQR",
  components: { AppPopup },
  props: {
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      /** 客服二维码 */
      serviceQR: "",
    };
  },
  computed: {
    visible: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
  created() {
    this.getServiceQR();
  },
  methods: {
    /** 获取客服二维码 */
    getServiceQR() {
      commonConfigKey({
        paramName: "platform_gf_qr_code",
      }).then(res => {
        this.serviceQR = JSON.parse(res || "{}");
        console.log(this.serviceQR);
      });
    },
  },
};
</script>

<style scoped lang="scss">
/deep/ .el-dialog{
  border-radius: 16px;
}
</style>
