
<template>
  <div class="pagination-container">
    <el-pagination
      :page-sizes=" pageSizes"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :page-size.sync="getPageSize"
      :current-page.sync="getCurrent"
      layout="total, sizes, prev, pager, next, jumper"
      :total="total" />
  </div>
</template>

<script>
// import { scrollTop } from '@/utils/toolMethods'
export default {
  name: "XyPagination",
  props: {
    total: {
      type: Number,
      default: 0
    },
    pageSize: {
      type: Number,
      default: 10
    },
    current: {
      type: Number,
      default: 1
    },
    pageSizes: {
      type: [Array,Number],
      default: () => {
        return [10, 20, 50, 100];
      }
    }
  },
  data() {
    return {
    };
  },
  computed: {
    getPageSize: {
      get() {
        return this.pageSize;
      },
      set(val) {
        this.$emit("update:pageSize", val);
      }
    },
    getCurrent: {
      get() {
        return this.current;
      },
      set(newVal) {
        this.$emit("update:current", newVal);
      }
    }
  },
  methods: {
    handleSizeChange(val) { /* 条数切换触发 */
      this.$emit("handleSizeChange", val);
      // scrollTop(0, 800)
    },
    handleCurrentChange(val) { /* 页数切换触发 */
      this.$emit("handleCurrentChange", val);
      // scrollTop(0, 800)
    }
  }
};
</script>

<style lang="scss" scoped>
.pagination-container{
  margin-top: $distance-normal;
}
</style>
