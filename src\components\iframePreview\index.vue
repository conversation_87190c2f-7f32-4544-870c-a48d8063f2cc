<template>
  <app-drawer append-to-body :size="size" :title="title" :visible.sync="getShow"
              @open="openFun">
    <div class="flex flex-column FilePreview">
      <div v-if="hasTabBar&&selectDefaultValue&&getShow" class="pt-[24px] pl-[24px] pb-[12px]">
        <app-select :default-value="selectDefaultValue" @change="selectChange" :select-list="tabBarList" />
      </div>
      <div class="flex-1">
        <div class="w-full h-full"  v-loading.lock="loadingIframe" v-show="loadingIframe" />
        <iframe v-if="iframeState"  class="border-0" @load="load" ref="iframe" width="100%" height="100%" :src="iframeUrl" />
      </div>
    </div>
  </app-drawer>
</template>

<script>
import AppDrawer from "components/AppDrawer/index.vue";
import * as AppFn from "./index.js";
import { getToken } from "utils/storage";
import { axiosBaseHeadersConfig } from "utils/config";
import  qs from "qs";
import AppSelect from "components/AppSelect/index.vue";


export default {
  name: "IframePreview",
  components: { AppSelect, AppDrawer },
  props: {
    url: {
      type: String,
      default: ""
    },
    size: {
      type: String,
      default: "720px"
    },
    title: {
      type: String,
      default: "文件预览"
    },
    /* 是否携带token信息*/
    nestApp: {
      type: Boolean,
      default: false
    },
    /* 是不是文件预览*/
    isFilePreview: {
      type: Boolean,
      default: false
    },
    /* tabbarlist*/
    tabBarList: {
      type: Array,
      default: () => []
    },
    activeTabBarIndex: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      loadingIframe: false,
      iframeState: false,
      timer: null,
      selectData: {}
    };
  },
  computed: {
    getShow: {
      get(){
        return this.$store.getters["getIframePreviewVisible"];
      },
      set(val){
        this.$store.commit("SET_IFRAME_PREVIEW_VISIBLE", val);
      }
    },
    /* 查看有没有tabbar*/
    hasTabBar(){
      return this.basics.isArray(this.tabBarList) && !this.basics.isArrNull(this.tabBarList);
    },
    selectDefaultValue(){
      if(this.hasTabBar){
        console.log(this.activeTabBarIndex, "this.activeTabBarIndex");
        return this.tabBarList[this.activeTabBarIndex].value;
      }
      return "";
    },
    /* 获取当前的url  当传入tabbar的时候 url根据tabbar来*/
    getUrl(){
      if(!this.basics.isObjNull(this.selectData)){
        return this.selectData.url;
      }
      if(this.hasTabBar){
        return this.tabBarList[this.activeTabBarIndex].url;
      }
      return this.url || "";
    },
    /* 处理一下url*/
    iframeUrl() {
      if(this.isFilePreview){
        const { Base64 } = require("js-base64");
        return  process.env.VUE_APP_FILE_PREVIEW_PATH + "/onlinePreview?url=" + encodeURIComponent(Base64.encode(this.getUrl));
      }
      if(!this.nestApp) return this.getUrl;
      else {
        const [url, urlParams] = this.getUrl.split("?");
        /* 要带到嵌入页面的参数*/
        const params = {
          token: getToken(),
          osVersion: axiosBaseHeadersConfig.osVersion,
          ...qs.parse(urlParams)
        };
        return `${url}?${qs.stringify(params)}`;
      }
    }
  },
  watch: {
    getShow(){
      if(!this.getShow){
        this.selectData = {};
        /* 关闭弹窗 清除延时器*/
        clearTimeout(this.timer);
        this.removeEventListenerMessage();
      }
    }
  },
  methods: {
    selectChange(data){
      this.selectData = data;
      this.openFun();
    },
    load(){
      this.loadingIframe = false;
      if(this.nestApp) {
        this.addEventListenerMessage();
      }
    },
    addEventListenerMessage(){
      this.removeEventListenerMessage();
      window.addEventListener("message", this.messageCallback, false);
    },
    removeEventListenerMessage(){
      window.removeEventListener("message", this.messageCallback);
    },
    messageCallback(message){
      const  data = message.data;
      /* 判断子级页面通信域名 是不是本地写的域名*/
      if(message.origin !== process.env.VUE_APP_CHILD_URL) return;
      if(!data) return;
      if(!this.basics.isObj(data)) return;
      if(!data.appName) return;
      if(data.appName !== "lky-activity") return;
      if(!AppFn[data.appFnName]) return;
      AppFn[data.appFnName](data.data);
      this.getShow = false;
    },
    openFun() {
      this.openIframePage();
      this.loadingIframe = true;
      /* 文件预览 超时补偿*/
      this.timer = setTimeout(() => {
        this.loadingIframe = false;
      }, 10000);
    },
    openIframePage(){
      clearTimeout(this.timer);
      this.iframeState = false;
      this.$nextTick(() => {
        this.iframeState = true;
      });
    }
  },
};
</script>

<style scoped lang="scss">
.FilePreview{
  /*高度减去title高度*/
  height: calc(100% - 5px);
}
.button{
  border-top: 0.5px solid #EEEEEE;
}
</style>
