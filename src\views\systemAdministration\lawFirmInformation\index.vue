<template>
  <div class="pl-[24px] pr-[24px] pb-[24px] bg-[#FFFFFF] min-h-full">
    <p class="title">机构信息</p>
    <basics-info />
    <account-information>
      <p class="title">账户信息</p>
    </account-information>
    <p class="title">充值与消耗记录</p>
    <recharge-and-consumption-records />
  </div>
</template>

<script>
import BasicsInfo from "views/systemAdministration/lawFirmInformation/components/basicsInfo.vue";
import AccountInformation from "views/systemAdministration/lawFirmInformation/components/accountInformation.vue";
import RechargeAndConsumptionRecords
  from "views/systemAdministration/lawFirmInformation/components/rechargeAndConsumptionRecords.vue";

export default {
  name: "LawFirmInformation",
  components: {  RechargeAndConsumptionRecords, AccountInformation, BasicsInfo }
};
</script>

<style scoped lang="scss">
.title{
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  padding-left: 12px;
  position: relative;
  padding-top: 25px;
  padding-bottom: 16px;
  &:after{
    position: absolute;
    content: "";
    width: 4px;
    height: 16px;
    background: #3887F5;
    left: 0;
    top: 28px;
  }
}
</style>
