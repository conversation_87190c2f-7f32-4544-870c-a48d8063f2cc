
<template>

  <el-button  v-root-btn="showField"  @click="handleClick" type="text" size="small"> <slot /> </el-button>

</template>

<script>
export default {
  name: "Buttons",
  props: {
    showField: {
      type: String,
      default: "",
      desc: "权限展示按钮"
    }
  },
  data() {
    return {

    };
  },
  methods: {
    handleClick() {
      this.$emit("click");
    }
  }
};

</script>

<style lang="scss" scoped>
  .buttons{

  }
</style>
