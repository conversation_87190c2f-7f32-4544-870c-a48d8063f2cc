<template>
	<div>
		<el-popover placement="right" width="400" trigger="click" v-model="visible">
      <template  v-if="filesList.length">
			<p v-for="(item,idx) in filesList" :key='item+idx'>
        <el-link type="primary" @click="operateFile(item)">{{
				item.url
			}}</el-link>
      </p>
      </template>
      <p v-else>暂无附件</p>
			<el-button slot="reference" type="primary">查看附件</el-button>
		</el-popover>
		<el-image-viewer v-if="showViewer" :on-close="closeViewer" :url-list="previewList" />
	</div>
</template>

<script>
import ElImageViewer from "element-ui/packages/image/src/image-viewer";
export default {
  name: "FilesShow",
  components: {
    ElImageViewer
  },
  props: {
    itemList: {
      type: Object,
      default: () => {},
      desc: "表格定义的字段"
    },
    rowList: {
      type: Object,
      default: () => {},
      desc: "表格返回的字段"
    }
  },
  data() {
    return {
      visible: false,
      filesList: [],
      showViewer: false,
      previewList: []
    };
  },
  watch: {
    rowList: {
      handler(val) {
        this.init();
      },
      deep: true
    }
  },
  mounted() {
    this.init();
  },
  methods: {
    // 参数处理
    init() {
      if (this.basics.isObjNull(this.rowList)) return;
      this.filesList = [];
      const listStr = this.rowList[this.itemList.prop];
      // const listStr = 'https://oss.imlaw.cn/test/image/2021/10/13/8797da1e0e26428db695365fb007dc3e.jpg,https://oss.imlaw.cn/test/excel/temp/成交数据-2021-10-20_16:15:20.xlsx'
      if (!listStr) return;
      const list = listStr.split(",");
      list.forEach(url => {
        const type = this.isImage(url);
        this.filesList.push({ url, type });
      });
      //
    },
    /**
     * 文件区别 暂时区别只做图片 文件区别
     * type 1 图片预览 2文件下载
     */
    isImage(str) {
      if (!str) return;
      const imgExt = [".png", ".jpg", ".jpeg", ".bmp", ".gif"];
      const name = str.toLowerCase();
      const i = name.lastIndexOf(".");
      if (i > -1) {
        const ext = name.substring(i);
        return imgExt.includes(ext) ? 1 : 2;
      } else {
        return 3;
      }
    },
    /**
     * 文件操作
     * type 1 图片预览 2文件下载
     */
    operateFile(item) {
      const { url, type } = item;
      if (type === 1) {
        this.previewImg(url);
      } else if (type === 2) {
        this.downloadFile(url);
      } else {
        this.$message({
          showClose: true,
          message: "暂不支持操作的文件格式",
          type: "warning"
        });
      }
    },
    // 文件下载
    downloadFile(url) {
      if (!url) return;
      this.visible = false;
      window.location.href = url;
    },
    // 图片预览
    previewImg(url) {
      if (!url) return;
      this.visible = false;
      this.showViewer = true;
      this.previewList = [url];
    },
    // 关闭图片预览
    closeViewer() {
      this.showViewer = false;
      this.previewList = [];
    }
  }
};
</script>
