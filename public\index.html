<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge" >
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
<!-- saas图标库 要改 改这个   -->
<!--    <link rel="stylesheet" type="text/css" href="//at.alicdn.com/t/c/font_4337340_fzhrselvnzj.css">-->
    <link rel="icon" href="<%= BASE_URL %>logo.png">
    <title>律客云管理平台</title>
    <!-- 引入样式 -->
<!--    <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">-->
<!--    <script src="https://cdn.bootcss.com/vue/2.5.2/vue.min.js"></script>-->
<!--    <script>window.Vue || document.write('<script src="https://cdn.bootcdn.net/ajax/libs/vue/2.5.2/vue.min.js"><\/script>')</script>-->

<!--    <script src="https://cdn.bootcss.com/vue-router/3.0.2/vue-router.min.js"></script>-->
<!--    <script>window.VueRouter || document.write('<script src="https://cdn.bootcdn.net/ajax/libs/vue-router/3.0.2/vue-router.min.js"><\/script>')</script>-->

<!--    <script src="https://cdn.bootcss.com/vuex/3.1.0/vuex.min.js"></script>-->
<!--    <script>window.Vuex || document.write('<script src="https://cdn.bootcdn.net/ajax/libs/vuex/3.1.0/vuex.min.js"><\/script>')</script>-->

<!--    <script src="https://cdn.bootcss.com/axios/0.18.0/axios.min.js"></script>-->
<!--    <script>window.axios || document.write('<script src="https://cdn.bootcdn.net/ajax/libs/axios/0.18.0/axios.min.js"><\/script>')</script>-->
<!--    &lt;!&ndash; 引入组件库 &ndash;&gt;-->
<!--    <script src="https://unpkg.com/element-ui/lib/index.js"></script>-->

  </head>
  <body>
    <noscript>
      <strong>We're sorry but <%= htmlWebpackPlugin.options.title %> doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app"></div>
    <!-- built files will be auto injected -->
  </body>
</html>
