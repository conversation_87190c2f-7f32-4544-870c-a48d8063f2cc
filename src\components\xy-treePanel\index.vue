<template>
  <div class="tree-panel">
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <div class="head-title" v-if="title">
        <slot>
          {{title}}
        </slot>
        <span class="operate" @click="handleTitleClick" v-if="showTitleBtn">
          <el-popover
            placement="right"
            width="100"
            trigger="hover"
            :content="`添加一级${title}`">
              <i class="el-icon-plus" slot="reference" />
          </el-popover>
<!--          <el-tooltip :content="`添加一级${title}`" placement="right" effect="light">-->
<!--          </el-tooltip>-->
      </span>
      </div>
     <div :class="showCheckbox?'':'tree'">
       <el-tree
         :ref="refName"
         :show-checkbox="showCheckbox"
         :data="treeData"
         :props="defaultProps"
         :expand-on-click-node="expandNode"
         :check-on-click-node="true"
         :default-checked-keys="defaultChecked"
         :default-expanded-keys="defaultExpanded"
         :default-expand-all="false"
         :indent="30"
         node-key="id"
         highlight-current
         @check="handleCheck"
         @check-change="handleCheckChange"
         @node-click="handleNodeClick">
         <div class="flex custom-tree-node" slot-scope="{ node, data }">
            <span>{{node.label}}</span>
            <slot name="rightContent" :datas=' data' />
         </div>
         </el-tree>
     </div>
    </el-scrollbar>
  </div>
</template>

<script>
export default {
  name: "TreePanel",
  props: {
    defaultChecked: {
      type: Array,
      default: () => []
    },
    showCheckbox: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: "菜单"
    },
    treeData: {
      type: Array,
      required: true
    },
    currentNode: {
      type: Object,
      required: true
    },
    currentChecked: {
      type: Array,
      default: () => []
    },
    defaultExpanded: {
      type: Array,
      required: false
    },
    showTitleBtn: {
      type: Boolean,
      required: false,
      default: true
    },
    defaultProps: {
      type: Object,
      default: function() {
        return {
          children: "list",
          label: "label"
        };
      }
    },
    expandNode: {
      type: Boolean,
      default: false
    },
    refName: {
      type: String,
      default: "tree"
    }
  },
  watch: {
    currentChecked: function(val) {
      this.$refs.tree.setCheckedKeys(val);
    },
    currentNode: function(val) {
      this.$nextTick(() => this.$refs[this.refName].setCurrentKey(val.id));
    }
  },
  mounted() {
    this.$emit("treeNode", this.$refs.tree);
  },
  methods: {
    handleNodeClick(nodeData, nodeSelfParams, nodeComponent) {
      this.$emit("nodeClick", { nodeData, nodeSelfParams, nodeComponent });
    },
    handleCheck(checkedNodes, checkedKeys) {
      this.$emit("handleCheck", checkedKeys);
    },
    handleTitleClick() {
      this.$emit("titleClick");
    },
    handleCheckChange(checkedNodes, checkedState) {
      this.$emit("handleCheckChange", { checkedNodes, checkedState });
    }
  }
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  .tree-panel {
    height: 100%;
    .operate {
      float: right;
      cursor: pointer;
      i{
        font-size: $font-size-normal;
        color: $primary-color;
        font-weight: 600;
      }
    }
    & /deep/ .el-scrollbar {
      height: 100%;
      /*width: 198px;*/
      .el-scrollbar__wrap {
        overflow-x: hidden;
      }
    }
   .tree{
     /*& /deep/ .el-tree--highlight-current {*/
     /*  background-color: transparent;*/
     /*  .el-tree-node.is-current > .el-tree-node__content {*/
     /*    background-color: #42b983;*/
     /*    color: #FFFFFF;*/
     /*  }*/
     /*  .el-tree-node__content:hover {*/
     /*    background-color: #42b983;*/
     /*    color: #FFFFFF;*/
     /*  }*/
     /*}*/
   }
  }
  .custom-tree-node{
    flex: 1;
  }
</style>
