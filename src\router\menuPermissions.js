
/* 当前菜单的权限*/
import basics from "utils/basicsMethods";

let menuPermissions = {

};

/* 修改全部菜单*/
export const updateAllMenuPermissions = (data = {}) => {
  menuPermissions = data;
};

/* 修改部分菜单*/
export const updateMenuPermissions = (id, data = {}) => {
  if(menuPermissions[id]){
    menuPermissions[id] = {
      ...menuPermissions[id],
      ...data
    };
    return;
  }
  menuPermissions[id] = {
    ...data
  };
};

/* 获取全部菜单权限*/
export const getAllMenuPermissions = () => {
  return menuPermissions;
};

/* 获取菜单权限*/
export const getMenuPermissions = (id) => {
  return getAllMenuPermissions()[id];
};

/* 判断有无权限菜单 true就是有*/
export const hasMenuPermissions = (id) => {
  return !basics.isNull(getMenuPermissions(id));
};

/* 判断有无权限，执行方法*/
export const hasMenuPermissionsExecute = (id, fn) => {
  if(hasMenuPermissions(id)){
    fn && fn();
  }
};
