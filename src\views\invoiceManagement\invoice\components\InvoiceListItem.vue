<template>
  <div class="relative">
    <img v-if="statusIcon" :src="statusIcon" alt="" class="w-[64px] h-[46px] block absolute top-0 right-[44px]" />
    <div class="flex items-center justify-between p-[16px] border-[1px] border-[#EEEEEE] border-solid rounded-[8px]"
      :class="{ 'opacity-50': !canInvoice }">
      <div class="flex items-center">
        <el-checkbox v-model="checkboxValue" class="mr-[16px]" @change="handleCheckboxChange"
          :disabled="!canInvoice" />
        <div class="ml-[8px]">
          <div class="flex items-center">
            <div v-if="data.title" class="text-[14px] font-medium text-[#333333]">{{ data.title }}</div>
            <div
              v-if="data.label"
              class="ml-[4px] w-[56px] h-[16px] rounded-[21px] border-[1px] border-solid border-[#A0CFFB] box-border flex items-center justify-center">
              <div class="text-[12px] text-[#3887F5]">{{ data.label }}</div>
            </div>
          </div>
          <div v-if="data.info" class="text-[12px] text-[#666666] mt-[4px]">{{ data.info }}</div>
          <div v-if="data.time" class="text-[12px] text-[#999999] mt-[8px]">{{ String(invoiceType) === "1" ? '支付' : '抢单' }}时间：{{ data.time }}</div>
        </div>
      </div>

      <div class="flex flex-col items-end ml-[12px] shrink-0">
        <div class="text-[16px] font-medium text-[#333333]">{{ priceYuan(data.amount) }}</div>
        <div class="text-[12px] text-[#999999] mt-[4px]">{{ String(invoiceType) === "1" ? '实付金额' : '单条支付' }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { priceYuan } from "@/utils/toolMethods";

export default {
  name: "InvoiceListItem",
  props: {
    data: {
      type: Object,
      required: true
    },
    invoiceType: {
      type: [Number, String],
      default: "1"
    },
    value: {
      type: Array,
      default: () => []
    }
  },
  computed: {
    checkboxValue: {
      get() {
        return this.value.includes(this.data.bizId);
      },
      set(val) {
        let newSelected = [...this.value];
        if (val) {
          // 添加到选中数组
          if (!newSelected.includes(this.data.bizId)) {
            newSelected.push(this.data.bizId);
          }
        } else {
          // 从选中数组中移除
          newSelected = newSelected.filter(id => id !== this.data.bizId);
        }
        this.$emit("input", newSelected);
      }
    },
    /** 是否可以开票 */
    canInvoice() {
      return this.data.invoiceStatus === 0;
    },
    statusIcon() {
      const icons = [
        require("@/assets/images/Frame1321315436.png"),
        require("@/assets/images/Frame2727.png"),
      ];

      if (this.data.invoiceStatus === 1) {
        return icons[0];
      } else if (this.data.invoiceStatus === 2) {
        return icons[1];
      }

      return "";
    }
  },
  methods: {
    priceYuan,
    handleCheckboxChange(val) {
      // 勾选状态变化时的事件处理
      this.$emit("check-change", {
        id: this.data.bizId,
        checked: val,
        item: this.data
      });
    }
  }
};
</script>
