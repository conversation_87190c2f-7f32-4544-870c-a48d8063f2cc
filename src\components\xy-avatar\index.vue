<template>
  <el-image
    :style="`width: ${size}px; height: ${size}px;border-radius: ${shape==='circle'? '100%':'no-set'}`"
    :src="picturePath(url, type, sync)"
    :preview-src-list="preview?[picturePath(url, type, sync)]:[]"
    fit="cover"
    lazy>
    <div slot="error" class="image-slot" style="width: 100%; height: 100%; display: flex;justify-content: center;align-items: center; background: #f2f2f2;">
      <i class="el-icon-picture" style="font-size: 18px;color: #a5a5a5" />
    </div>
  </el-image>
</template>

<script>
import { picturePath } from "utils/toolMethods";

export default {
  name: "Avatar",
  props: {
    size: {
      type: Number,
      default: 40
    },
    url: {
      type: String
    },
    shape: {
      type: String,
      default: "circle",
      desc: "设置头像的形状,circle / square"
    },
    type: {
      type: Number,
      default: 1,
      desc: "1: 用户头像,2: 图片，其默认值不同"
    },
    sync: {
      type: Boolean,
      default: true,
      desc: "是否是异步请求图片或本地图片，true：异步;false:本地"
    },
    preview: {
      type: Boolean,
      default: true,
      desc: "是否可以预览"
    }
  },
  methods: {
    picturePath
  }
};
</script>

<style scoped lang="scss">
</style>
