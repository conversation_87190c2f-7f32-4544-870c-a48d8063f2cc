<template>
  <div>

    <el-carousel class="w-[192px]" height="30px" direction="vertical" :autoplay="true" indicator-position="none">
      <el-carousel-item v-for="(item,i) in promotionalInfoLists" :key="i">
        <el-image @click.stop="handleImage(item)" v-if="!basics.isNull(item.cover)"  class="w-[192px] block cursor-pointer" :src="item.cover" :preview-src-list="item.previewImg||[]" />
      </el-carousel-item>
    </el-carousel>
    <!--  下载APP  -->
    <down-app-pop v-for="(i,index) in pageOpenPreviewImg" :key="index" :data="i" />
  </div>
</template>

<script>
import DownAppPop from "components/xy-header/downAppPop.vue";
import { commonConfigKey } from "@/api/common";

export default {
  name: "HeaderSwipe",
  components: { DownAppPop },
  data() {
    return {
      /* 活动图的集合*/
      promotionalInfoLists: [],
    };
  },
  computed: {
    /* 进页面就打开的预览图*/

    pageOpenPreviewImg() {
      return this.promotionalInfoLists.filter(item => {
        return item.previewImg && item.starting;
      });
    }
  },
  mounted() {
    this.getPromotionalInfo();
  },
  methods: {

    /* 获取促销信息*/
    getPromotionalInfo(){
      commonConfigKey({
        paramName: "LEXICON_CLOUD_SETTLED_IN_THE_POP_UP_WINDOW_TWO",
      }).then(res => {
        if(!res) return;
        try {
          this.promotionalInfoLists = JSON.parse(res);
        }catch (e) {
          console.log(e);
        }
      });
    },
    handleImage(data){
      if(data.previewUrl){
        this.$store.dispatch("setIframePreviewVisible", data);
      }
      if(data.jumpUrl){
        this.$router.push(data.jumpUrl);
      }
    }
  }
};
</script>

<style scoped lang="scss">

</style>
