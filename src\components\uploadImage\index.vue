<template>
	<div class="uloadImage">
		<!-- 图片列表展示 -->
		<div v-if="fileList.length > 0" class="img-list">
			<div v-for="(item, index) in fileList" :key="index" :style="imageSize" class="list">
				<el-image
					v-if="showList"
					ref="previewImage"
					:preview-src-list="[item]"
					:src="item"
					:style="imageSize"
					fit="fill"
				/>
				<slot />
				<!-- 操作 -->
				<div class="tools">
					<span>
						<!-- 预览 -->
						<i v-if="showList" class="el-icon-zoom-in" @click="handlePreview(item, index)" />
						<!-- 下载 -->
						<!-- <i class="el-icon-download"></i> -->
						<!-- 删除 -->
						<i class="el-icon-delete" @click="handleRemove(item, index)" />
					</span>
				</div>
			</div>
		</div>
		<el-upload
			v-if="limit > fileList.length"
			ref="upload"
			:accept="accept"
			:action="uplodImages"
			:before-upload="beforeUpload"
			:limit="limit"
			:multiple="multiple"
			:on-error="handleError"
			:on-success="handleSuccess"
			:show-file-list="false"
			class="editor-slide-upload"
		>
			<div :style="`width:${width};height:${height}`" class="uploadBtn">
				<i slot="default" class="el-icon-plus" />
			</div>
			<!-- <el-button size="small" type="primary">
          点击上传
        </el-button> -->
		</el-upload>

		<!-- 用法： <UploadTest v-model="postForm.image_uri"    /> -->
	</div>
</template>
<script>
import mixin from "./minxin";
// import { compressAccurately } from 'image-conversion'
export default {
  name: "UploadImage",
  mixins: [mixin],
};
</script>

<style lang="scss" scoped>
.uloadImage {
	display: flex;
	margin-bottom: 15px;

	.img-list {
		display: flex;
		flex-wrap: wrap;

		.list {
			font-size: 0;
			position: relative;
			border: 1px solid #eee;
			border-radius: 2px;
			overflow: hidden;
			margin-right: 10px;
			margin-bottom: 10px;
			position: relative;
			&:hover {
				.tools {
					opacity: 1;
				}
			}

			/deep/ .el-image-viewer__wrapper {
				z-index: 9999999 !important;
			}

			.tools {
				position: absolute;
				background: rgba(0, 0, 0, 0.5);
				top: 0;
				right: 0;
				left: 0;
				bottom: 0;
				cursor: pointer;
				color: #fff;
				display: flex;
				opacity: 0;

				span {
					display: flex;
					align-items: center;
					justify-content: center;
					width: 100%;
					height: 100%;
				}

				i {
					font-size: 20px;
					margin: 0 5px;
				}
			}

			img {
				width: 100%;
				display: inline-block;
				cursor: pointer;
			}
			.del {
				position: absolute;
				right: 0;
				top: 0;
				// font-size: $font-size-large;
				color: #fff;
				z-index: 10;
				cursor: pointer;
				transition: all 0.2s ease-in-out;
				border-radius: 100%;
				font-weight: 600;
				&:hover {
					// color: $error-color;
					background: #fff;
				}
			}
		}
	}
}
.uploadBtn {
	background: #fbfdff;
	line-height: 100px;
	font-size: 20px;
	border: 1px dashed #c0ccda;
	color: #8c939d;
}
</style>
