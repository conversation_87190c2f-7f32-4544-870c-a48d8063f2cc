import Vue from "vue";

Vue.directive("click-await", {
  bind: function(el, binding) {

    el["onCLickAwait"] = () => {
      if (el._disabled) {
        return;
      }
      el._disabled = true;
      binding.value(binding.arg).finally(() => {
        el._disabled = false;
      });
    };
    if(el.dataset && el.dataset.disabled){
      return;
    }
    el.addEventListener("click", el.onCLickAwait);
  },
  unbind: function(el) {
    el.removeEventListener("click", el.onCLickAwait);
  }
});
