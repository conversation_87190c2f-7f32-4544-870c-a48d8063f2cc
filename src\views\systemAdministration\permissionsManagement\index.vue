<template>
  <div class="flex">
    <!-- 人员列表   -->
    <div class="bg-white listOfPeople">
      <list-of-people  @selectStaff="selectStaff" />
    </div>
    <!-- 菜单列表   -->
    <div class="bg-white tree menuList mx-[24px]">
      <menu-list @menuConfirm="menuConfirm" :disabled="disabled" :select-ids="selectIds" :menu-list="menuList" :tree-props="treeProps" />
    </div>
    <!-- 权限列表   -->
    <div class="bg-white tree flex-1">
      <list-of-button-permissions :tree-props="treeProps" :menu-list="selectMenuList" />
    </div>
  </div>
</template>

<script>
import ListOfPeople from "views/systemAdministration/permissionsManagement/components/ListOfPeople.vue";
import MenuList from "views/systemAdministration/permissionsManagement/components/MenuList.vue";
import ListOfButtonPermissions
  from "views/systemAdministration/permissionsManagement/components/listOfButtonPermissions.vue";
import { sysModuleSelectAllList, sysModuleSelectByStaffId } from "@/api/system";

export default {
  name: "PermissionsManagement",
  components: { ListOfButtonPermissions, MenuList, ListOfPeople },
  provide() {
    return {
      getPersonnelId: this.getPersonnelId
    };
  },
  data() {
    return {
      /* 人员id*/
      personnelId: undefined,
      /* 是否禁用*/
      disabled: false,
      /* 所有菜单*/
      menuList: [],
      /* 选择的菜单*/
      selectMenuList: [],
      /* 员工选中的菜单id*/
      selectIds: [],
      treeProps: {
        children: "list",
        label: "moduleName"
      }
    };
  },
  methods: {
    getPersonnelId(){
      return this.personnelId;
    },
    selectStaff({ id, isAdmin }){
      this.personnelId = id;
      this.disabled = !this.basics.isNull(isAdmin) && isAdmin === 1;
      this.getMenu();
      this.menuQueries();
    },
    /* 获取菜单*/
    getMenu(){
      if(this.basics.isNull(this.personnelId)) {
        this.menuList = [];
        return;
      }
      /* 查询全部菜单*/
      sysModuleSelectAllList({ level: 2 }).then(data => {
        this.menuList = this.formatTheMenu(data);
      });
    },
    /* 格式化菜单*/
    formatTheMenu(data){
      if (this.basics.isNull(data)) return[];
      if(!this.disabled) return data;
      return data.map(item => {
        return {
          ...item,
          ...(this.basics.isNull(item.list) || this.basics.isArrNull(item.list) ? {} : { list: this.formatTheMenu(item.list) }),
          disabled: this.disabled
        };
      });
    },
    /* 查询当前选中员工的菜单*/
    async menuQueries(needSelectIds=true){
      if(needSelectIds) this.selectIds = [];
      if(!this.basics.isNull(this.personnelId)) {
        try {
          /* 查询用户菜单*/
          this.selectMenuList = await sysModuleSelectByStaffId({
            id: this.personnelId
          });
          if(needSelectIds) this.selectIds = this.filterIds(this.selectMenuList);
        }catch (e) {
          this.selectMenuList = [];
        }
      }else{
        this.selectMenuList = [];
      }
    },
    filterIds(data){
      const ids = [];
      data.forEach(item => {
        if(item.list && item.list.length){
          ids.push(...this.filterIds(item.list));
        }else{
          /* !只追加最后一级的id*/
          ids.push(item.id);
        }
        if(this.disabled) item["disabled"] = this.disabled;
      });
      return ids;
    },
    /* 菜单保存后 刷新权限*/
    menuConfirm(){
      this.menuQueries(false)
    }
  }
};
</script>

<style scoped lang="scss">
.listOfPeople{
  width: pxToPer(272);
}
.menuList{
  width: pxToPer(371);
}
.tree{
  ::v-deep{
    .el-icon-caret-right{
      color:  #333333;
    }
    .el-tree-node__expand-icon.is-leaf{
      color: transparent;
    }
  }
}
</style>
