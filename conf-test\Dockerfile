FROM registry.cn-hangzhou.aliyuncs.com/imlaw-test/nginx:alpine 

RUN ln -sf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime

RUN rm -rf /etc/nginx/nginx.conf
RUN rm -rf /etc/nginx/conf.d/default.conf

RUN mkdir -p /data/log
RUN mkdir -p /data/project/dist

COPY ./conf-test/nginx.conf /etc/nginx/nginx.conf
COPY ./conf-test/default.conf /etc/nginx/conf.d/default.conf

ADD ./dist /data/project/dist/

EXPOSE 29000