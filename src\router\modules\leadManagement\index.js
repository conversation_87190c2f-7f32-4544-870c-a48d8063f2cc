export default [
  {
    path: "/leadManagement",
    name: "leadManagement",
    meta: {
      title: "线索"
    },
    component: () => import("@/views/middleware"),
    children: [
      {
        path: "/clueSquare",
        name: "clueSquare",
        meta: {
          title: "线索广场"
        },
        component: () => import("@/views/middleware"),
        children: [
          {
            path: "/cluesToTheSourceOfTheCase",
            name: "cluesToTheSourceOfTheCase",
            meta: {
              title: "案源线索",
              isCache:true
            },
            component: () => import("views/leadManagement/clueSquare/cluesToTheSourceOfTheCase/index.vue"),
          },
          {
            path: "/instantConsultation",
            name: "instantConsultation",
            meta: {
              title: "即时咨询"
            },
            component: () => import("views/leadManagement/clueSquare/instantConsultation/index.vue"),
          }
        ]
      },
      {
        path: "/leadFollowUp",
        name: "leadFollowUp",
        meta: {
          title: "线索跟进",

        },
        component: () => import("@/views/middleware"),
        children: [
          {
            path: "/leadFollowUpPage",
            name: "leadFollowUpPage",
            meta: {
              title: "线索跟进",
              isCache:true
            },
            component: () => import("views/leadManagement/leadFollowUp/leadFollowUpPage/index.vue"),
          }
        ]
      },
      {
        path: "/statistics",
        name: "statistics",
        meta: {
          title: "数据统计"
        },
        component: () => import("@/views/middleware"),
        children: [
          {
            path: "/leadAnalysis",
            name: "leadAnalysis",
            meta: {
              title: "线索分析"
            },
            component: () => import("views/leadManagement/statistics/leadAnalysis/index.vue"),
          }
        ]
      }

    ]
  },

];
