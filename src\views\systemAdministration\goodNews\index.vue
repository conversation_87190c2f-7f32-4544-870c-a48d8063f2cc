<template>
  <div class="wrap-box">
    <!--  筛选    -->
    <div class="list-search">
      <label>发布时间</label>
      <el-date-picker
        v-model="time"
        class="!w-[360px]"
        type="datetimerange"
        value-format="yyyy-MM-dd HH:mm:ss"
        start-placeholder="开始日期"
        end-placeholder="结束日期" />
      <label class=" ml-[40px]">查看状态</label>
      <el-select class="w-[160px]" v-model="statusRead" clearable placeholder="请选择">
        <el-option
          v-for="item in readStatus"
          :key="item.value"
          :label="item.label"
          :value="item.value" />
      </el-select>
      <app-button @click="handleSearch" class="ml-[40px]">查询</app-button>
      <app-button class="ml-[8px]" type="info" @click="handleReset">重置</app-button>
    </div>
    <div class="list-box flex">
      <div class="list-left translate-x-0">
        <div class="list-wap-c pr">
          <div v-if="list.length <1" class="none-data tc">
            <img src="@/assets/images/empty.png" />
            <p class="fs14 text-[#666666]">当前条件下暂无数据～</p>
          </div>
          <div v-else>
            <ul>
              <li v-for="(item,i) in list" :key="i"
                  :class="['px-[24px]', Number(item.id) === Number(detailId) && 'checked']"
                  @click="handleListItem(i)">
                <!--              <img :src="item.coverPic" />-->
                <div class="list-content  border-0 box-border   border-b-[1px] border-solid border-[#EEEEEE] pr py-[16px]">
                  <!--      已读未读          -->

                  <div class="flex justify-between items-center">
<!--                    -->
                    <div class="flex items-center">
                      <p class="text-ellipsis font-bold text-[16px] text-[#333333] max-w-[269px]">{{ item.title }}</p>
                      <i v-if="!item.read" />
                    </div>
                    <p class="text-[12px] text-[#999999]">{{item.pubTime}}</p>
                  </div>
                  <p class=" text-ellipsis !mt-[7px] text-[14px] text-[#666666]">
                    {{item.description}}
                  </p>
                </div>
              </li>
            </ul>
            <!--    分页      -->
            <div  class="fixed bg-[#ffffff] border-0 border-[#EEEEEE] border-solid  border-t-[1px] bottom-0 left-0 right-0 px-[24px] pt-[12px] pb-[24px] ">
              <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page.sync="currentPage"
                :page-size="pageSize"
                :pager-count="5"
                layout="prev, pager, next, jumper"
                :total="total" />
            </div>
          </div>
        </div>
      </div>
      <div class="list-right list-wap-c" v-if="!basics.isObjNull(detail)">
        <div class="list-right-tit">
          <h5>{{ detail.title }}</h5>
          <p>{{ detail.pubTime }}</p>
        </div>
        <div class="list-right-content">
          <div class="list-right-item">
            <p class="title">活动描述</p>
            <label class="fs14 text-[#666666]">{{ detail.description }}</label>
          </div>
<!--          <div class="list-right-item">
            <p class="title">成案金额</p>
            <span class="list-right-item-money">{{ priceNumber(detail.amount) }}<i>元</i></span>
          </div>
          <div class="list-right-item">
            <p class="title">付费明细</p>
            <div class="list-right-item-table flex flex-space-between">
              <div class="list-right-item-table-tr">
                <p v-for="(item,k) in payDetail" :key="k" v-if="k<4"><label>{{ item.label }}</label>
                  <span>{{ item.type === 'money' ? priceYuan(detail[item.prop]) : detail[item.prop] }}</span></p>
              </div>
              <div class="list-right-item-table-tr">
                <p v-for="(item,k) in payDetail" :key="k" v-if="k>=4" :class="item.className">
                  <label>{{ item.label }}</label>
                  <span>{{ item.type === 'money' ? priceYuan(detail[item.prop]) : detail[item.prop] }}</span></p>
              </div>
            </div>
            <p class="desc">注：投产比数据真实有效，来自客户使用后的实际反馈成案金额</p>-->
            <div class="list-right-item-imgs" id="goodNewsList">
              <img v-for="img in detail.imgs" class="img-box" :key="img" :src="img" />
<!--              <img class="bottom-img" src="@/assets/images/<EMAIL>" />-->
            </div>
            <preview-the-image container="#goodNewsList" />
          </div>
        </div>
      </div>
  </div>
</template>

<script>


import { lcGoodNewsDetailById, lcGoodNewsPage } from "@/api/workbenchManagement";
import { priceNumber, priceYuan } from "../../../utils/toolMethods";
import AppButton from "components/appButton/index.vue";
import { readStatus } from "@/enum/common.js";
import PreviewTheImage from "components/PreviewTheImage/index.vue";

export default {
  name: "GoodNews",
  components: {PreviewTheImage, AppButton },
  data() {
    return {
      time: [],
      statusRead: "",
      currentPage: 1,
      pageSize: 10,
      total: 0,
      list: [],

      detailId: null,
      detail: {},
      payDetail: [
        {
          label: "使用时长",
          prop: "useDay"
        }, {
          label: "有效案源线索数（条）",
          prop: "effectiveClueNum"
        }, {
          label: "律客云消费金额（元）",
          type: "money",
          prop: "lcConsumeAmount"
        }, {
          label: "收款金额（元）",
          type: "money",
          prop: "collectedAmount"
        }, {
          label: "案源线索锁定数（条）",
          prop: "useClueNum"
        }, {
          label: "案源线索单价（元/条）",
          type: "money",
          prop: "clueUnitPrice"
        }, {
          label: "签单数",
          prop: "signedOrdersNum"
        }, {
          label: "投入产出比",
          className: "text-red",
          prop: "inputOutputRatio"
        },
      ],
    };
  },
  computed: {
    readStatus() {
      return readStatus;
    },
  },
  created() {
    this.getList();
  },
  methods: {
    priceYuan,
    priceNumber,
    /** 获取列表**/
    getList() {
      const data = {
        currentPage: this.currentPage,
        pageSize: this.pageSize,
        readStatus: this.statusRead
      };
      if (this.time) {
        data.pubTimeStart = this.time[0];
        data.pubTimeEnd = this.time[1];
      }
      lcGoodNewsPage(data).then((r) => {
        let findIndex = 0;
        const { records = [] } = r;
        this.list = records;
        this.total = r.total;
        if (this.basics.isArrNull(records)) {
          findIndex = -1;
        }else if (this.$route.query.id) {
          findIndex = records.findIndex((s) => Number(s.id) === Number(this.$route.query.id));
        }
        this.getDetail(findIndex);
      });
    },
    /** 获取详情 **/
    getDetail(index) {
      if(index < 0) {
        this.detailId = null;
        this.detail = {};
        return;
      }
      const item = this.list[index];
      this.detailId = item.id;
      /* 更新全局的通知数量*/
      if(item.read === 0){
        this.$store.commit("REDUCE_NOTICE_COUNTS", "goodNewsUnreadNum");
      }
      this.$set(item, "read", 1);
      lcGoodNewsDetailById({ id: this.detailId }).then(r => {
        console.log(r, "详情");
        if (r.pic) r.imgs = r.pic.split(",");
        this.detail = r;
      });
    },
    /* 搜索*/
    handleSearch(){
      this.currentPage = 1;
      this.getList();
    },
    /* 重置 */
    handleReset(){
      this.time = [];
      this.statusRead = "";
      this.currentPage = 1;
      this.getList();
    },
    handleSizeChange(val) {
      console.log(val, 9);
    },
    handleCurrentChange(val) {
      console.log(val, 8);
      this.currentPage = val;
      this.getList();
    },
    /** 点击列表的单条数据**/
    handleListItem(index) {
      this.getDetail(index);
    }
  }
};
</script>

<style scoped lang="scss">
.wrap-box {
  background: #FFFFFF;
  padding: 24px;

  .list-search {
    label {
      margin-right: 8px;
    }
  }

  .list-wap-c{
    height: calc(100vh - 237px);
    overflow-y: auto;
  }
  .list-box {
    border: 1px solid #EEEEEE;
    border-radius: 5px;
    margin-top: 24px;

    .list-left {
      width: 462px;
      border-right: 1px solid #EEEEEE;
      box-sizing: border-box;
      .list-wap-c{
        box-sizing: border-box;
        padding-bottom: 74px;
      }
      li {
        cursor: pointer;

        &.checked {
          background: #F5F5F7;
        }

        &:hover {
          background: #F5F5F7;
        }

        .list-content {

          i {
            width: 8px;
            height: 8px;
            background: #EB4738;
            display: inline-block;
            border-radius: 50%;
            margin-left: 4px;
          }

          &-desc {
            margin-top: 6px;
          }
        }

        img {
          width: 48px;
          height: 48px;
          margin-right: 12px;
          background: #FFFFFF;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #EEEEEE;
        }
      }
    }

    .list-right {
      width: calc(100% - 462px);
      padding: 24px;
      box-sizing: border-box;


      &-tit {
        padding-bottom: 24px;
        border-bottom: 1px dotted #DDDDDD;

        h5 {
          font-weight: 600;
          font-size: 23px;
          color: #333333;
        }

        p {
          font-weight: 400;
          font-size: 12px;
          color: #999999;
          margin-top: 8px;
        }
      }

      &-content {
        padding-top: 24px;
      }

      &-item {
        padding-bottom: 16px;

        &-money {
          font-weight: 600;
          font-size: 40px;
          color: #9F6310;
          padding: 16px 56px;
          background: #F7F1E6;
          border-radius: 4px 4px 4px 4px;
          border: 1px solid #F6E6C7;
          display: inline-block;

          i {
            font-style: normal;
            font-weight: 400;
            font-size: 16px;
            color: #9F6310;
            margin-left: 4px;
          }
        }

        &-table {
          width: 615px;

          &-tr {
            width: 302px;
            border: 1px solid #DDDDDD;
          }

          p {
            line-height: 33px;
            font-weight: normal;
            font-size: 12px;
            border-bottom: 1px solid #DDDDDD;

            &:last-child {
              border-bottom: none;
            }

            label {
              width: 140px;
              height: 33px;
              background: #F5F5F7;
              border-right: 1px solid #DDDDDD;
              display: inline-block;
              text-align: center;
              color: #666666;
            }

            span {
              width: 156px;
              height: 33px;
              background: #FFFFFF;
              //border: 1px solid #DDDDDD;
              display: inline-block;
              padding-left: 16px;
              box-sizing: border-box;
              color: #333333;
            }

          }
        }

        .desc {
          font-size: 12px;
          color: #F78C3E;
          margin: 12px 0 16px;
        }
      }

      .img-box {
        max-width: 100%;
      }

      .bottom-img {
        width: 100%;
      }
    }
  }
}

.title {
  font-weight: 500;
  font-size: 16px;
  color: #111111;
  padding-left: 9px;
  position: relative;
  margin-top: 12px;
  margin-bottom: 10px;

  &:after {
    position: absolute;
    content: "";
    width: 3px;
    height: 18px;
    background: #EB4738;
    left: 0;
    top: 2px;
  }
}

.text-red {
  span {
    color: #EB4738 !important;
  }
}

.none-data {
  padding-top: 72px;

  img {
    width: 160px;
    height: 120px;
  }
}

</style>
