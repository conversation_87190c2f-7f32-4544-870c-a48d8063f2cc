import Vue from "vue";
import VueRouter from "vue-router";
import leadManagement from "router/modules/leadManagement/index.js";
import systemAdministration from "router/modules/systemAdministration/index.js";
import basics from "utils/basicsMethods";
import goodNewsManagement from "router/modules/goodNewsManagement";
import invoiceManagement from "router/modules/invoiceManagement";
import { updateAllMenuPermissions } from "router/menuPermissions";

Vue.use(VueRouter);

// 解决当前路由跳转当前路由报错
const originalPush = VueRouter.prototype.push;
VueRouter.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err);
  // return originalPush.call(this, location)
};

/** 界面路由 */
export const viewRoutes = [...leadManagement, ...systemAdministration];
// 登录后的默认路由
export let defaultRoute = null;

export const createRoutes = routes => {
  const finalRoutes = [];
  let allMenu = {};

  /** 比对路由 */
  function compareRoutes(serviceRoutes, final) {
    serviceRoutes.forEach(item => {
      allMenu[item.id] = { ...item };
      if(item.active){
        defaultRoute = { ...item };
      }
      const name = item.modulePath.replaceAll("/", "");

      final.push({
        path: item.modulePath,
        name,
        meta: {
          moduleIcon: item.moduleIcon,
          title: item.moduleName,
          cachedView: name !== "workbench",
          icon: item.icon || "",
          /* 当前用户菜单按钮权限*/
          btnRoots: basics.isNull(item.enableFunc) ? [] : item.enableFunc.split(","),
          /* 当前菜单有哪些按钮权限*/
          roots: basics.isNull(item.functions) ? [] : item.functions.split(",") || [],
          parentId: item.parentId,
        },
        component: () => import("@/views" + item.filePath),
      });

      // 如果二级路由存在，递归比对
      if (item.list && item.list.length) {
        final[final.length - 1].children = [];

        compareRoutes(item.list, final[final.length - 1].children);
      }
    });
  }

  compareRoutes(routes, finalRoutes);

  console.log(finalRoutes, "finalRoutesfinalRoutes");
  updateAllMenuPermissions(allMenu);
  allMenu = null;
  return [
    {
      path: "/",
      name: "layout",
      component: () => import("../views/layout/layout.vue"),
      children: [...finalRoutes],
    },
  ];
};

/** 不需要权限，一直会存在的路由 */
const constantRoutes = [
  {
    path: "/",
    name: "layout",
    component: () => import("../views/layout/layout.vue"),
    children: [],
  },
  {
    path: "/login",
    name: "Login",
    meta: {
      title: "登录",
    },
    hidden: true,
    component: () => import("../views/login/login.vue"),
  },
  {
    path: "/401",
    name: "401",
    meta: {
      title: "无权限",
    },
    hidden: true,
    component: () => import("../views/errorPage/401.vue"),
  },
  {
    path: "*",
    name: "404",
    meta: {
      title: "无权限",
    },
    hidden: true,
    component: () => import("../views/errorPage/404.vue"),
  },
  {
    path: "/500",
    name: "500",
    meta: {
      title: "页面丢失",
    },
    hidden: true,
    component: () => import("../views/errorPage/500.vue"),
  },
  ...goodNewsManagement,
  ...invoiceManagement
];

const router = new VueRouter({
  // mode: 'history',
  base: process.env.BASE_URL,
  routes: [...constantRoutes],
});

/* 路由异常错误处理，尝试解析一个异步组件时发生错误，重新渲染目标页面 */
router.onError(error => {
  const pattern = /Loading chunk (\d)+ failed/g;
  const isChunkLoadFailed = error.message.match(pattern);
  const targetPath = router.history.pending.fullPath;
  if (isChunkLoadFailed) {
    router.replace(targetPath);
  }
});

export default router;
