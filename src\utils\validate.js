import basics from "utils/basicsMethods";

export const commonReg = (reg, msg) => {
  return (rule, value, callback) => {
    if (basics.isNull(value)) {
      callback();
    } else if (!reg.test(value)) {
      callback(new Error(msg));
    } else {
      callback();
    }
  };
};

/* 数字最小验证*/

export function minNumber(min, msg) {
  return (rule, value, callback) => {

    if (basics.isNull(value)) {
      return callback();
    }
    let num = 0;
    try {
      /* 判断是不是输入纯数字*/
      num = Number(value);
    }catch (e) {
      return callback(new Error("请输入数字"));
    }
    if (isNaN(num)) {
      return callback(new Error("请输入数字"));
    }
    if (num < min) {
      return  callback(new Error(msg));
    }
    callback();

  };
}

export function regular(type) {
  switch (type) {
  case "phone": // 手机号码
    return commonReg(/^1\d{10}$/, "请输入正确的手机号码");
  case "phoneLandline": // 手机号码或者座机
    return commonReg(/^((0\d{2,3}-\d{7,8})|(1[35847]\d{9}))$/, "请输入正确号码");
  case "ID": // 身份证
    // return commonReg(/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, '请输入正确的身份证号')
    return commonReg(/(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}$)/, "请输入正确的身份证号");
  case "pwd": // 密码以字母开头，长度在6~18之间，只能包含字母、数字和下划线
    return commonReg(/^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,.\/]).{6,18}$/, "请输入6~18位需要包含字母,数字,特殊字符");
  case "usn": // 账号以字母开头，长度在6~18之间，只能包含字母、数字和下划线
    return commonReg(/^.{6,18}$/, "请输入6~18位字符");
  case "email": // 邮箱
    return commonReg(/^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/, "请输入正确的邮箱");
  case "URL": // 网址
    return commonReg(/(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/, "请输入正确的网址");
  case "IP": // IP
    return commonReg(/((?:(?:25[0-5]|2[0-4]\\d|[01]?\\d?\\d)\\.){3}(?:25[0-5]|2[0-4]\\d|[01]?\\d?\\d))/, "请输入正确的IP");
  case "number": // 数字
    return commonReg(/^(\d)*$/, "请输入整数");
  case "fullName": // 昵称
    return commonReg(/^[\u4E00-\u9FA5A-Za-z0-9]{1,20}$/, "请输入正确的名称，最多20个字符");
  case "percentage":
    return commonReg(/^(100|[1-9]?\d(\.\d\d?)?)%$/, "请输入百分数如10%");
  case "floatingNum":
    return commonReg(/^\d+\.\d{1,2}$/, "输入最多2位小数且是数字");
  case "floatingNumOne":
    return commonReg(/^(\d+\.\d{1,2}|(\d)*)$/, "请输入正确的数字");
  case "mostTwoSit":
    return commonReg(/^\d{1,6}$|(^\d{1,6}[\.]{1}[0-9]{1,2}$)/, "最多输入6位，且最多输入两位小数");
  case "icon":
    return commonReg(/^[^\u4e00-\u9fa5\s]*$/, "不支持中文图标，请重新输入");
  case "numLetter": // 只能包含字母、下划线
    return commonReg(/[A-Za-z]+[_]+|[_]+[A-Za-z]+$/, "请输入字母加下划线");
  case "website": // 只能包含字母、下划线
    return commonReg(/[A-Za-z]+[.]+|[.]+[A-Za-z]+$/, "请输正确的网址");
  case "numOrLetter": // 只能包含字母、数字
    return commonReg(/^[0-9A-Za-z]+$/, "请输入字母，数字");
  case "keyLetter": // 只能包含字母、数字、_
    return commonReg(/^[0-9A-Za-z_]+$/, "请输入字母、数字、`_`组合");
  case "fullTitle": // 中英文、数字或者以上两种组合
    return commonReg(/^[\u4E00-\u9FA5A-Za-z0-9]/, "请输入中英文、数字或者以上两种组合");
  case "CandE": // 中英文或者以上两种组合
    return commonReg(/^[\u4E00-\u9FA5A-Za-z]/, "请输入中英文或者以上组合");
  case "account": // 账号限制只能输入汉字或字母 并限制20位
    return commonReg(/^[\u4E00-\u9FA5A-Za-z0-9]{1,20}$/, "请输入中英文、数字或者以上组合,最多20个字符");
  case "noCanZoreStart": // 账号限制只能输入汉字或字母 并限制20位
    return commonReg(/^(\d|[1-9]\d*)$/, "不能以0开头");
  case "numberZero": // 数字
    return commonReg(/^(\d{1,10})*$/, "请输入整数");
  case "realName":
    // 必须为中文汉字，且小于5个字符
    return commonReg(/^[\u4E00-\u9FA5]{1,5}$/, "请输入中文汉字,最多5个字符");
  default:
    console.error("No times regular");
    return true;
  }
}

export function notEsitSpace(rule, value, callback) {
  const reg = /(^\s+)|(\s+$)|\s+/g;
  if (reg.test(value)) {
    callback(new Error("输入的内容不能有空格"));
  } else {
    callback();
  }
}

export function validatePass(rule, value, callback) {
  const reg = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,.\/]).{6,18}$/;
  if (!value) {
    callback(new Error("请输入账号的密码"));
  } else if (!reg.test(value)) {
    callback(new Error("请输入6~18位字母,数字,特殊字符"));
  } else {
    callback();
  }
}

export function validateOldPass(rule, value, callback) {
  const reg = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,.\/]).{6,18}$/;
  if (!value) {
    callback(new Error("请输入原密码"));
  } else if (!reg.test(value)) {
    callback(new Error("请输入6~18位字母,数字,特殊字符"));
  } else {
    callback();
  }
}
export function validateNewPass(rule, value, callback) {
  const reg = /^(?=.*[a-zA-Z])(?=.*\d)(?=.*[~!@#$%^&*()_+`\-={}:";'<>?,.\/]).{6,18}$/;
  if (!value) {
    callback(new Error("请输入新密码"));
  } else if (!reg.test(value)) {
    callback(new Error("请输入6~18位字母,数字,特殊字符"));
  } else {
    callback();
  }
}
// 身份证电话号码脱敏
export function numberHidden(str, frontLen, endLen) { // str：要进行隐藏的变量  frontLen: 前面需要保留几位    endLen: 后面需要保留几位
  if (!str) return "-";
  const len = str.length - frontLen - endLen;
  let xing = "";
  for (let i = 0; i < len; i++) {
    if (i === "*") return;
    xing += "*";
  }
  return str.substring(0, frontLen) + xing + str.substring(str.length - endLen);
}

