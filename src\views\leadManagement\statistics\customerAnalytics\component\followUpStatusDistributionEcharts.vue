<template>
<!--  跟进状态分布-->
  <div class="p-[24px]">
    <app-echarts  ref="echarts" class="echarts" />
  </div>
</template>

<script>
import AppEcharts from "components/appEcharts/index.vue";
export default {
  name: "FollowUpStatusDistributionEcharts",
  components: { AppEcharts },
  data() {
    return {
      info: {}
    };
  },
  mounted() {
    this.$nextTick(() => {
      this.$refs.echarts.initEcharts(this.echartsOptions);
    });
  },
  methods: {
    echartsOptions(){
      return  {
        title: {
          text: "总数（条）", // 主标题文本
          subtext: this.info.grabClueTotal || 0, // 副标题文本
          left: "center",
          top: "32%",
          textStyle: {
            fontSize: 11,
            color: "#666666",
            align: "center",
            fontWeight: "400"
          },
          subtextStyle: {
            fontSize: 19,
            color: "#333333",
            align: "center",
            fontWeight: "600"
          }
        },
        tooltip: {
          trigger: "item",
          formatter: "{a} <br/>{b}: {c} ({d}%)"
        },
        legend: {
          data: ["跟进结束", "跟进中" ],
          bottom: 10,
          icon: "circle",
          itemWidth: 6,  // 设置宽度
          itemHeight: 6, // 设置高度,
          textStyle: {
            fontSize: 14,
            color: "#999999"
          }
        },
        color: ["#EB4738", "#3887F5"],
        series: [
          {
            name: "跟进状态分布",
            type: "pie",
            center: ["50%", "40%"],
            radius: ["45%", "55%"],
            avoidLabelOverlap: false,
            labelLine: {
              lineStyle: {
                color: "#999999",
              }
            },
            label: {
              show: true,
              formatter: ({ percent, value }) => {
                return [
                  `{c|${value}\n}`,
                  `{d|${percent}%}`
                ].join("\n");
              },
              rich: {
                c: {
                  fontSize: 14,
                  color: "#000000",
                  align: "center"
                },
                d: {
                  fontSize: 14,
                  color: "#000000",
                  align: "center"
                }
              }
            },
            itemStyle: {
              borderWidth: 2, // 设置border的宽度有多大
              borderColor: "#fff",
            },
            emphasis: {
              itemStyle: {
                shadowOffsetX: 0,
                shadowColor: "rgba(0, 0, 0, 0.5)"
              }
            },
            data: [
              { value: this.info.followEndCount || 0, name: "跟进结束", color: "#EB4738" },
              { value: this.info.followIngCount || 0, name: "跟进中", color: "#3887F5" },
            ]
          }
        ]
      };
    }
  }
};
</script>

<style scoped lang="scss">

.echarts{
  width: 306px;
  height: 258px;
}
</style>
