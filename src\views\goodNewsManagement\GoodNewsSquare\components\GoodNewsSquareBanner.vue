<template>
	<app-detected-dom @change="bannerSizeChange">
		<div class="relative mx-auto cursor-pointer" @click="turnToUploadGoodNews">
			<img
				class="block w-full h-full"
				alt=""
				src="@/views/goodNewsManagement/GoodNewsSquare/img/Frame1321315739.png"
			/>
			<div
				class="absolute font-bold text-[24px] text-[#FF6D55] [text-shadow:0px_0px_0px_rgba(255,255,255,0.41),_inset_0px_0px_0px_rgba(211,46,27,0.93)]"
				:style="{
					left: `${countPosition.left}px`,
					top: `${countPosition.top}px`,
					fontSize: `${countPosition.fontSize}px`,
				}"
			>
				{{ priceNumberTwo(countInfo.awardAmount) }}
			</div>
		</div>
	</app-detected-dom>
</template>

<script>
import { turnToUploadGoodNews } from "utils/turnPage";
import { commonConfigKey } from "@/api/common";
import { priceNumberTwo } from "utils/toolMethods";
import AppDetectedDom from "components/AppDetectedDom/index.vue";

export default {
  name: "GoodNewsSquareBanner",
  components: { AppDetectedDom },
  data() {
    return {
      countInfo: {},
      countPosition: {
        top: 0,
        left: 0,
        fontSize: 24,
      },
    };
  },
  created() {
    this.getCount();
  },
  methods: {
    priceNumberTwo,
    turnToUploadGoodNews() {
      turnToUploadGoodNews();
    },
    getCount() {
      commonConfigKey({
        paramName: "lc_good_news_v2_award",
      }).then(data => {
        try {
          this.countInfo = JSON.parse(data);
        } catch (e) {
          console.error(e);
        }
      });
    },
    bannerSizeChange(dom) {
      const { width, height } = dom;

      const widthRatio = width / 1112;
      const heightRatio = height / 188;

      this.countPosition.top = heightRatio * 93;
      this.countPosition.left = widthRatio * 238;
      this.countPosition.fontSize = widthRatio * 24;
    },
  },
};
</script>
