import router,{defaultRoute} from "./index";
import store from "store";
import NProgress from "nprogress"; // progress bar
import "nprogress/nprogress.css"; // progress bar style
import { getToken } from "utils/storage";
import { scrollTop } from "utils/toolMethods";
import basics from "utils/basicsMethods.js";
import { Message } from "element-ui";
import {getDictValueByOrgDescKey} from "utils/tools";

NProgress.configure({ showSpinner: false }); // NProgress Configuration

const whiteList = ["/login", "/500", "/404", "/401"]; // no redirect whitelist

export function findLastChildrenName(data){
  if(!basics.isNull(defaultRoute)){
    return defaultRoute.modulePath||defaultRoute.path;
  }
  if (data?.children) {
    return findLastChildrenName(data.children[0]);
  } else {
    return data?.path;
  }
}

/*路由跳转分配*/
const routerPermission = (to,from, next,callback) => {
  const isLoginPath = to.path==='/login'
  /*是不是根路径*/
  const isRootPath = to.path === "/";
  // 如果访问的是根路径
  if (isRootPath||isLoginPath) {
    const res = store.getters["getMenusList"];
    // 如果登录后无权限可以再去登录 也可以跳转到404
    // 登录后有权限就不能区登录页面了
    if(isLoginPath && basics.isArrNull(res)){
      next();
      return;
    }

    // 递归找到data下最后一层children的第一个path
    const path = findLastChildrenName(res[0])||'/404';
    // 要重定向的路径是当前路径就取消进度条 因为一样的路径不会触发beforeEach
    if(path===from.path){
      NProgress.done()
    }
    // 重定向到第一个菜单
    next({
      path,
    });

    return;
  }
  // 不做拦截
  callback&&callback()
}
const getDocumentTitle = ()=>{
  getDictValueByOrgDescKey({key:'title'}).then(data=>{
    document.title = data;
  })
}
router.beforeEach((to, from, next) => {
  // 进度条
  NProgress.start(); // start progress bar

  // 修改文档title
/*  if (process.env.VUE_APP_EVN_CONFIG === "dev") {
    document.title = "律客云管理平台";
  } else if (process.env.VUE_APP_EVN_CONFIG === "test") {
    document.title = "律客云管理平台";
  }*/

  // 重置面包屑
  // store.dispatch('setBreadcrumb', [to.meta.title])

  // 需要缓存
  const needCache = [];
  // console.log(to, needCache, 888)
  if (needCache.includes(to.path)) {
    to.meta.isCache = true;
  }

  // keep-alive保存
  store.dispatch("addCachedView", to);

  // 关闭详情页面
  store.dispatch("setShowDetailPage", false);

  // 滚动条的重置
  scrollTop();
  // 如果已经登录了
  if (getToken()) {
    if (basics.isObjNull(store.getters["userInfo"])) {
      // eslint-disable-next-line no-debugger
      store
        .dispatch("getUserInfo")
        .then(() => {
          getDocumentTitle();
          routerPermission(to,from, next,()=>{
            next({
              path: to.path,
              params: to.params,
              query: to.query
            });
          });
        })
        .catch(() => {
          // 请求失败，重新登录
          next({ path: "/login" });
        });
    } else {
      routerPermission(to,from, next,()=>{
        next()
      });
    }
  } else {
    // 否则全部重定向到登录页
    // 如果没有登录
    if (whiteList.includes(to.path)) {
      // 在免登录白名单，直接进入
      next();
      return;
    }else{
      next(`/login`);
    }
    NProgress.done(); // finish progress bar
  }
});

router.afterEach(() => {
  NProgress.done(); // finish progress bar
});

export default router;
