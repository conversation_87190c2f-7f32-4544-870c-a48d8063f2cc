export const axiosBaseHeadersConfig = {
  osVersion: "17",
  platform: "saas",
  /** ! 版本号，根据产品定义的版本，后期版本迭代都需要进行修改 */
  apiVersion: process.env.VUE_APP_VERSION || "1.0.0",
  appVersionCode: "68",
};

/**
 * 律师是否认证
 * 认证状态 1:待认证,2.已认证,3.未认证,4.认证失败
 */
export function lawyerCertStatus(certStatus) {
  return Number(certStatus) === 2;
}

/* 协议链接*/
export const agreementLinks = {
  // 律客云充值协议
  lawyerCloudRechargeProtocol: process.env.VUE_APP_PROTOCOL_DOMAIN_NAME + "/lky_pc_recharge_protocol.html",
  // 自动抢案源风险协议
  automaticRobberySourceRiskProtocol: process.env.VUE_APP_PROTOCOL_DOMAIN_NAME + "/lky_pc_automatic_robbery_source_risk_protocol.html",
};
