/**
 * days:传入的天数
 */

var minDateP;
export default function datePickerOptions(days = 30) {
  return {
    disabledDate(time) {
      const times = 86400000 * days; // 毫秒数
      const curSelectTime = new Date(minDateP).getTime();
      const before = curSelectTime - times;// 前天数毫秒数
      const after = curSelectTime + times;// 后天数毫秒数
      return time.getTime() > after || time.getTime() < before;
    },
    // 选中开始时间和最后的时间
    onPick({ maxDate, minDate }) {
      if (!maxDate) {
        minDateP = minDate;
      }
    },
    shortcuts: [
      {
        text: "今天",
        onClick(picker) {
          picker.$emit("pick", [new Date(new Date().toLocaleDateString()), new Date()]);
        }
      },
      {
        text: "最近一周",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
          picker.$emit("pick", [start, end]);
        }
      },
      {
        text: "最近一个月",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
          picker.$emit("pick", [start, end]);
        }
      },
      {
        text: "最近三个月",
        onClick(picker) {
          const end = new Date();
          const start = new Date();
          start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
          picker.$emit("pick", [start, end]);
        }
      }
    ]
  };
}


/* datePickerOptions扩展*/
export const datePickerOptionsExpand = (days = 30) => {
  const data = datePickerOptions(days);
  const { shortcuts } = data;
  const [toDay, ...shortcutsData] = shortcuts;
  return {
    ...data,
    shortcuts: [toDay, {
      text: "24小时前",
      onClick(picker) {
        /* 开始时间前3个月 结束时间24小时前*/
        const start = new Date();
        const end = new Date();
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
        end.setTime(end.getTime() - 3600 * 1000 * 24);
        picker.$emit("pick", [start, end]);
      }
    }, ...shortcutsData]
  };
};
