import { commonConfigKey } from "@/api/common.js";

let  _updateDataTimerNum = null;
/* 定时刷新数据提示*/
export default ({ updateDataApi, msg = "", key = "" }) => ({
  data() {
    return {
      _updateDataTimer: null,
      _updateParams: {},
      _updateRequestState: false
    };
  },
  activated() {
    this._startUpdateData();
  },
  methods: {
    _updateData(api, data = {}){
      this._updateParams = data;
      /* 当重新请求第一页或者重置搜索的时候关闭提示弹窗*/
      if(data && data.currentPage === 1){
        this._closeCaseSourcePushToast();
      }
      this._startUpdateData();
      return api(data);
    },
    _startUpdateData(){
      /* 避免activated生成周期和_updateData方法同时执行*/
      if(this._updateRequestState) return;
      this._updateRequestState = true;
      this._getUpdateDataTimerNum().then((num) => {
        this._clearUpdateData();
        this._loopUpdateApi(num);
      }).finally(() => {
        this._updateRequestState = false;
      });
    },
    _loopUpdateApi(num){
      this._updateDataTimer = setTimeout(() => {
        updateDataApi(this._updateParams).then((res) => {
          const num = res[key];
          if(!this.basics.isNull(num) && num > 0){
            this.$store.commit("SET_CASE_SOURCE_PUSH_TOAST_SHOW", true);
            this.$store.commit("SET_CASE_SOURCE_PUSH_TOAST_MSG", msg.replace("num", `<span class="text-[#22BF7E] px-[4px]">${num}</span>`));
          }
        });
        this._loopUpdateApi(num);
      }, num);

    },
    _clearUpdateData(){
      clearTimeout(this._updateDataTimer);
    },
    /* 获取定时 时间*/
    _getUpdateDataTimerNum(){
      if(_updateDataTimerNum === null){
        return  commonConfigKey({
          paramName: "LC_CASE_SOURCE_AUTO_REFRESH",
        }).then(res => {
          _updateDataTimerNum = Number(res || 10000);
          return _updateDataTimerNum;
        });
      }
      return Promise.resolve(_updateDataTimerNum);
    },
    /* 关闭提示弹窗*/
    _closeCaseSourcePushToast(){
      this.$store.commit("SET_CASE_SOURCE_PUSH_TOAST_SHOW", false);
    }
  },
  destroyed() {
    this._closeCaseSourcePushToast();
    this._clearUpdateData();
  },
  deactivated(){
    this._closeCaseSourcePushToast();
    this._clearUpdateData();
  }

});
