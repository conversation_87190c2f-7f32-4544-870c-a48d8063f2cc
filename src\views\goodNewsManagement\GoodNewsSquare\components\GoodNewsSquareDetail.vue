<template>
	<app-popup :visible.sync="show" title="喜报详情" width="560px" hide-footer>
		<div class="p-[24px]">
			<clue-detail-title title="案件信息" />
			<div class="mt-[12px]">
				<app-descriptions :data="data" :column="2" :info-props="infoProps" />
			</div>
			<div class="mt-[16px]">
				<clue-detail-title title="案件回顾" />
			</div>
			<div class="mt-[12px]">
				<div class="bg-[#F5F5F7] rounded-[4px] p-[12px]">
					{{ data.caseReview }}
				</div>
			</div>
			<div class="mt-[24px]">
				<clue-detail-title title="案件详情" />
			</div>
			<div class="bg-[#F5F5F7] rounded-[4px] mt-[12px] p-[12px]">
				{{ data.caseDetail }}
			</div>
			<img
				class="block w-[512px] h-[36px] mt-[24px]"
				alt=""
				src="@/views/goodNewsManagement/GoodNewsSquare/img/Frame2320.png"
			/>
			<div class="text-[12px] text-[#666666] mt-[8px]">
				本文版权归原作者所有，内容仅代表作者本人观点，不代表法临平台的立场。如有任何疑问或需要删除请通过客服联系我们。
			</div>
		</div>
	</app-popup>
</template>
<script>
import ClueDetailTitle from "views/leadManagement/leadFollowUp/leadFollowUpPage/ClueDetailTitle.vue";
import AppPopup from "components/appPopup/index.vue";
import AppDescriptions from "components/AppDescriptions/index.vue";
import { priceNumber } from "utils/toolMethods";

export default {
  name: "GoodNewsSquareDetail",
  components: { AppDescriptions, AppPopup, ClueDetailTitle },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
    value: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      infoProps: [
        {
          value: "type",
          label: "成案线索类型",
        },
        {
          label: "案件类型",
          value: "typeLabel",
        },
        {
          label: "成案地点",
          value: "regionName",
        },
        {
          label: "成案周期",
          value: "caseSuccessDay",
          render(item) {
            return (item.caseSuccessDay || "") + "天";
          },
        },
        {
          label: "成案金额",
          value: "amount",
          render(item) {
            return priceNumber(item.amount) + "元";
          },
        },
        {
          label: "委托金额",
          value: "caseEntrustAmount",
          render(item) {
            return priceNumber(item.caseEntrustAmount) + "元";
          },
        },
      ],
    };
  },
  computed: {
    show: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit("input", val);
      },
    },
  },
};
</script>
