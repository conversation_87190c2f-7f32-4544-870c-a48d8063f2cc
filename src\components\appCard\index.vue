<template>
  <div class="app-card">
    <p class="card-title">{{title}}</p>
    <div class="card-content">
      <slot />
    </div>
  </div>
</template>

<script>
export default {
  name: "AppCard",
  props: {
    title: {
      type: String,
      default: ""
    },
  },
};
</script>

<style scoped lang="scss">
.app-card{
  border-radius: 4px 4px 4px 4px;
  border: 1px solid #EEEEEE;
}
.card-title{
  font-size: 16px;
  font-weight: 500;
  color: #333333;
  padding: 0 24px;
  line-height: 54px;
  border-bottom: 1px solid #EEEEEE;

}
</style>
