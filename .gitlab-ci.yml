#发布流程梳理
## Global variables
variables:
  ServiceName: 'law-sass'
  BuildVersion: $BuildVersion #从统一发布位置传递

stages:
  - law-sass-test-deploy
  - law-sass-pro-deploy


trigger-test:
  stage: law-sass-test-deploy
  trigger:
    include: conf-test/.law-sass-deploy.yml
    strategy: depend
  # rules:
  #   - if: '$CI_COMMIT_BRANCH == "test"'
  # variables:
  #   projectEnvJob: 'test-law-sass'
  only:
    refs:
      - test
    variables:
      - $projectEnvJob == "test-law-sass"  


#发布生产
trigger-pro:
  stage: law-sass-pro-deploy
  trigger:
    include: conf-pro/.law-sass-deploy.yml
    strategy: depend
  only:
    refs:
      - master
    variables:
      - $projectEnvJob == "pro-law-sass"
