<template>
  <div>
    <workbench-card-title>计算类型</workbench-card-title>
    <el-form
        class="page-form"
        label-position="left"
        ref="caculatorItemForm"
        :model="formData"
        :rules="ruleConfig"
        label-width="120px"
    >
      <div class="flex pt-[16px] px-[24px] flex-wrap justify-between">
        <el-form-item
            label="入职年限"
            class="w-full"
            prop="entryYear">
          <el-input
              class="w-full"
              v-model="formData.entryYear"
              placeholder="请输入入职年限"
              maxlength="4"
          /></el-form-item>

        <el-form-item
            label="平均税前月工资"
            class="w-full"
            prop="averageSalary">
          <el-input
              class="w-full"
              v-model="formData.averageSalary"
              placeholder="请输入平均税前月工资"
              maxlength="9"
          >
            <template #append>元</template>
          </el-input>
        </el-form-item>
        <el-form-item
            class="w-full"
            label="所在地区"
            prop="regionCodes">
          <region
              class="w-full city"
              v-model="formData.regionCodes"
              :clearable="true"
              :api="areaGetPlatformArea"
              :get-names="true"
              @getregionsname="getRegionsName"
          />
        </el-form-item>

        <el-form-item
            class="w-full"
            label="劳动合同类型"
            prop="contractType">
          <el-select
              @change="handleChange($event, 'contractType')"
              class="w-full"
              v-model="formData.contractType"
              placeholder="请选择"
          >
            <el-option
                v-for="item in contractTypeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>

        </el-form-item>
        <el-form-item
            class="w-full"
            label="违法类型">
          <div class="flex">
            <el-checkbox
                v-model="formData.illegalContract"
                label="违法解除劳动合同" />
            <el-checkbox
                v-model="formData.notNotice"
                label="未提前30天通知" />
          </div>
        </el-form-item>

      </div>
    </el-form>

    <workbench-card-title>计算结果</workbench-card-title>
    <div class="px-[24px] pt-[16px]">
      <div class="relative bg-[linear-gradient(_94deg,_#F2F9FE_0%,_#BDE0FC_100%)]">
        <div class="p-[16px]" v-if="!showCalculateResult||(showCalculateResult&&needShowCalculateAmount)">
          <p class="text-[16px] text-[#333333]">
            预估补偿金额
          </p>
          <div class="flex items-center pt-[8px]">
            <div class="font-[600] text-[18px] text-[#3887F5] flex items-center">
              ¥
              <p class="text-[24px]">{{ !showCalculateResult?0:calculateResult }}</p>
            </div>
          </div>
        </div>
        <div class="p-[16px]" v-if="showCalculateResult&&!needShowCalculateAmount">
          <p class="text-[16px] text-[#333333]">
            {{contractTypeDesc}}
          </p>
          <div class="flex items-center pt-[8px]">
            <p class="font-[600] text-[24px] text-[#F34747]">属违法行为</p>
          </div>
        </div>
        <img class="w-[220px] absolute top-0 right-0  h-full" src="@/components/CalculatorDrawer/components/labor-arbitration/imgs/<EMAIL>" alt="" />
      </div>
    </div>
    <div v-if="showCalculateResult">
      <workbench-card-title class="pt-[48px]">补偿标准</workbench-card-title>
      <div class="px-[24px] pt-[8px] pb-[16px]">
        <p class="font-[500] text-[14px] text-[#666666] !pt-[8px]">
          1. 补偿月数(N)
        </p>
        <p class="text-[14px] text-[#666666] !pt-[4px] leading-[20px]">
          工龄&lt;6个月：N=0.5<br />
          6个月≤工龄&lt;1年：N=1<br />
          工龄≥1年：N=取整(工龄年数)，小数≥0.5时加0.5
        </p>
        <p class="font-[500] text-[14px] text-[#666666] !pt-[8px]">
          2. 基础补偿金
        </p>
        <p class="text-[14px] text-[#666666] !pt-[4px] leading-[20px]">
          经济补偿金=N×月工资基数
        </p>
        <p class="font-[500] text-[14px] text-[#666666] !pt-[8px]">
          3. 附加项
        </p>
        <p class="text-[14px] text-[#666666] !pt-[4px] leading-[20px]">
          未提前30天通知：+1个月工资基数<br />
          违法解除劳动合同：补偿金×2
        </p>
        <p class="font-[500] text-[14px] text-[#666666] !pt-[8px]">
          4. 总计算公式
        </p>
        <p class="text-[14px] text-[#666666] !pt-[4px] leading-[20px]">
          总赔偿金=经济补偿金+附加项
        </p>
        <p class="text-[12px] text-[#999999] !pt-[16px]">注：计算结果仅供参考，实际金额以当地最新政策为准</p>
      </div>
    </div>
  </div>
</template>

<script>
import WorkbenchCardTitle from "views/workbenchManagement/workbench/components/CardTitle.vue";
import Region from "components/regionCascader/index.vue";
import { areaGetPlatformArea } from "@/api/common";
import { formatAmount } from "utils/toolMethods";
import {buriedPoint} from "@/api/buriedPoint";

export default {
  name: "LaborArbitration",
  components: { Region, WorkbenchCardTitle },
  data() {
    return {
      // 劳动合同类型list
      contractTypeList: [{
        label: "固定年限",
        value: "1",
        // 保存案源时存的文案
        info: "签的劳动合同是固定年限"
      }, {
        label: "终身合同",
        value: "2",
        // 描述
        description: "企业解除终身合同",
        // 保存案源时存的文案
        info: "签的劳动合同是终身合同"
      }, {
        label: "未签订合同",
        value: "3",
        // 描述
        description: "企业未签订劳动合同",
        // 保存案源时存的文案
        info: "没有签劳动合同"
      }],
      contractTypeDesc: "",
      // 表单数据
      formData: {
        // 入职年限
        entryYear: "",
        // 所在地区
        regionCode: "",
        // 平均税前月工资
        averageSalary: "",
        // 劳动合同类型
        contractType: "",
        // 违法解除劳动合同
        illegalContract: false,
        // 未提前30天通知
        notNotice: false,
        regionCodes: []
      },
      ruleConfig: {
        entryYear: [
          {
            required: true,
            message: "请填写入职年限"
          },
          // 请输入整数或最多一位小数
          {
            pattern: /^\d+(\.\d{1})?$/,
            message: "入职年限请输入整数或最多一位小数"
          }
        ],
        regionCodes: [
          {
            required: true,
            message: "请选择所在地区"
          }
        ],
        averageSalary: [
          {
            required: true,
            message: "请填写平均税前月工资"
          },
          {
            //   整数
            pattern: /^\d+$/,
            message: "平均税前月工资请输入整数"
          }
        ],
        contractType: [
          {
            required: true,
            message: "请选择劳动合同类型"
          }
        ]
      },
      //   计算结果
      calculateResult: null,
    };
  },
  computed: {
    // 选择的劳动合同类型
    selectedContractType() {
      return this.formData.contractType;
    },
    // 需不需要展示计算金额 只有劳动合同类型是固定年限时才需要展示
    needShowCalculateAmount() {
      return this.selectedContractType === "1" || this.basics.isNull(this.selectedContractType);
    },
    // 是否展示计算结果
    showCalculateResult(){
      return !this.basics.isNull(this.calculateResult);
    }
  },
  mounted() {
    buriedPoint({
      code: "LAW_CLOUD_UTILS_LABOR_ARBITRATION_CALCULATOR_PAGE_CALCULAT_CLICK"
    });
  },
  methods: {
    areaGetPlatformArea,
    handleChange(value, key){
      // 劳动合同类型 单独处理一下
      if(key === "contractType"){
        /* 获取当前的数据*/
        const event = (this.contractTypeList.find(item => item.value === value) || {});
        this.contractTypeDesc = event.description || "";
        this.calculateResult = null;
      }
    },
    /* 城市选择*/
    getRegionsName(data) {
      const { value = [], name = [] } = JSON.parse(data);
      /* provinceCode	是	Integer	省编码
regionCode	是	Integer	地区编码*/
      const [provinceCode = "", regionCode = ""] = value;
      const [provinceName = "", regionName = ""] = name;
      this.formData = {
        ...this.formData,
        provinceCode,
        regionCode,
        provinceName,
        regionName,
      };
    },
    // 计算总赔偿金
    calculateTotalCompensation({ entryYear, averageSalary, illegalContract, notNotice }) {
      // 拆分工作年限为年和月
      const years = Math.floor(entryYear);
      const months = Math.round((entryYear - years) * 12); // 将小数部分转换为月数

      // 计算补偿月数N
      let N = 0;

      // 处理年数部分：每满一年支付一个月工资
      N += years;

      // 处理月数部分
      if (months >= 6) {
        N += 1;  // 满6个月按一年计算
      } else if (months > 0) {
        N += 0.5;  // 不满6个月支付半个月工资
      }

      // 计算基础补偿金
      let baseCompensation = N * averageSalary;

      // 计算附加项
      let additionalCompensation = 0;
      if (notNotice) {
        additionalCompensation += averageSalary; // 未提前30天通知
      }
      if (illegalContract) {
        baseCompensation *= 2; // 违法解除劳动合同
      }

      // 计算总赔偿金
      const totalCompensation = baseCompensation + additionalCompensation;

      return Math.round(totalCompensation);
    },
    // 预估补偿金额
    estimatedCompensationAmount() {
      // 判断是否需要展示金额 不需要直接是0
      if(!this.needShowCalculateAmount) {
        this.calculateResult = 0;
        return;
      }

      const { entryYear, averageSalary, illegalContract, notNotice } = this.formData;
      const calculateResult = this.calculateTotalCompensation({
        entryYear: Number(entryYear),
        averageSalary: Number(averageSalary),
        illegalContract,
        notNotice
      });
      this.calculateResult = formatAmount(calculateResult);
    },
    resetForm() {
      const data = this.$options.data();
      Object.assign(this.$data, data);
    },
    // 计算
    handleCalculate(){
      this.$refs.caculatorItemForm.validate((valid) => {
        if (valid) {
          this.estimatedCompensationAmount();
        }
      });
    },
  },
};
</script>

<style scoped lang="scss">
.city {
  ::v-deep {
    .el-cascader {
      width: 100%;
    }
  }
}

.page-form {
  ::v-deep {
    .el-form-item--small.el-form-item {
      margin-bottom: 24px;
      .el-form-item__label{
        padding: 0;
      }
    }
  }
}
</style>
