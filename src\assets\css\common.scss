@import './var.scss';
// 主要存放公共的样式

// 滚动条样式
::-webkit-scrollbar {
	width: 7px;
	height: 7px;
}
::-webkit-scrollbar-track-piece {
	background: #fff;
}
::-webkit-scrollbar-thumb {
	background: #e7e6e6;
	border-radius: 20px;
}

/* fade-transform */
.fade-transform-leave-active,
.fade-transform-enter-active {
	transition: all 0.2s;
}

.fade-transform-enter {
	opacity: 0;
	transform: translateX(-30px);
}

.fade-transform-leave-to {
	opacity: 0;
	transform: translateX(30px);
}

// 常用文案颜色高亮
.primary-color {
	color: $primary-color;
}
.secondary-color {
	color: $text-color-secondary;
}

.cursor {
	cursor: pointer;
}

// 间距
.m-t-15 {
	margin-top: $distance-normal;
}

.m-t-20 {
	margin-top: $distance-large;
}

.m-r-15 {
	margin-right: $distance-normal;
}
.m-b-15 {
	margin-bottom: $distance-normal;
}
.m-l-15 {
	margin-left: $distance-normal;
}
.m-15 {
	margin: $distance-normal;
}
.m-lr-15 {
	margin-left: $distance-normal;
	margin-right: $distance-normal;
}
.m-tb-15 {
	margin-top: $distance-normal;
	margin-bottom: $distance-normal;
}

.p-t-15 {
	padding-top: $distance-normal;
}
.p-r-15 {
	padding-top: $distance-normal;
}
.p-b-15 {
	padding-bottom: $distance-normal;
}
.p-l-15 {
	padding-left: $distance-normal;
}
.p-15 {
	padding: $distance-normal;
}
.p-lr-15 {
	padding-left: $distance-normal;
	padding-right: $distance-normal;
}
.p-tb-15 {
	padding-top: $distance-normal;
	padding-bottom: $distance-normal;
}
.pt30 {
	padding-top: 30px;
}

.box-shadow {
	border-radius: 2px;
	box-shadow: 0px 1px 8px 0px rgba(174, 174, 174, 0.3);
}

.pswp {
	z-index: 9999;
}

.fr {
	float: right;
}
.fl {
	float: left;
}
.pr, .relative {
	position: relative;
}
.pa {
	position: absolute;
}

.cur {
	cursor: pointer;
}
.tc {
	text-align: center;
}
.inline {
	display: inline;
}
.inline-block {
	display: inline-block;
}
.block {
	display: block;
}
/* 文字为1行，多余的加省略号 */
.line1 {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

/* 文字为2行，多余的加省略号 */
.line2 {
	overflow: hidden;
	text-overflow: ellipsis;
	-o-text-overflow: ellipsis;
	-webkit-text-overflow: ellipsis;
	-ms-text-overflow: ellipsis;
	-moz-text-overflow: ellipsis;
	word-break: break-all;
	/* 这里的display必须用-webkit-box */
	display: -webkit-box;
	/* -webkit-line-clamp限制在一个块元素显示的文本的行数 */
	-webkit-line-clamp: 2;
	/* -webkit-box-orient排列方向*/
	-webkit-box-orient: vertical;
}

// 数据释义的图标
.filed-icon {
	margin-left: 10px;
	margin-right: 0;
	font-size: 24px;
	color: #ccc;
	cursor: pointer;
	// top: 45%;
	// left: 6%;
	margin: 0 10px;

	&:hover {
		color: $primary-color;
	}
}
.text-more-line {
	word-break: break-all;
}

.index-wrap{
	position: relative;
	width: calc(100% + 30px);
    left: -15px;
	height: 100%;
    overflow: hidden;

	.table-wrap{
        height: 100%;
		padding:0 15px;
        overflow-y: auto;
    }

	.detail-wrap{
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: #fff;
		z-index: 4;
		padding:0 15px;
		height: 100%;
        overflow-y: auto;
        overflow-x: hidden;
	}
}

// 文字字号
.fs12{font-size: 12px;}
.fs14{font-size: 14px;}

.upright-btn{
	margin-left: 0;
}
.cursor-pointer{
	cursor: pointer;
}
.cursor-no-drop{
	cursor: no-drop;
}

audio{
	outline: none;
	border-radius: 27px;
}
audio::-internal-media-controls-download-button {
	display:none;
}
audio::-webkit-media-controls-enclosure {
	overflow:hidden;
}
audio::-webkit-media-controls-panel {
	width: calc(100% + 33px);
}

audio::-webkit-media-controls {
	overflow: hidden !important;
}
audio::-webkit-media-controls-enclosure {
	width: calc(100% + 32px);
	margin-left: auto;
}

.pageMiddleContainer-btn-wr{
	margin-bottom: 16px;
	.btn1{
		position: relative;
		.dot{
			position: absolute;
			top: 4px;
			right: 10px;
			width: 8px;
			height: 8px;
			border-radius: 50%;
			background: #EB4738;
		}
	}
	::v-deep{
		.el-button.el-button--mini {
			padding: 7px 12px;
			border-radius: 4px;
			border-color: transparent;
			background: #EDF3F7;
			&.active {
				color: #3887F5;
				border-color: #3887F5;
				outline: none;
			}
		}
	}
}

.common-table-page{
	padding-top: 5px;
	background: #fff;
	border-radius: 8px;
}

.notification-fade-enter-active, .notification-fade-leave-active {
	transition: opacity .3s, transform .3s;
}
.notification-fade-enter, .notification-fade-leave-to {
	transform: scale(0.8);
	opacity: 0;
}

/*.refresh-btn{
	position: absolute;
	top: 3px;
	right: 24px;
	z-index: 1;
}*/

.text-ellipsis-3 {
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	overflow: hidden;
	word-wrap: break-word;
	word-break: break-all;
}
.text-ellipsis {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	word-wrap: break-word;
	word-break: break-all;
}
.translate-x-0{
	transform: translateX(0);
}
.-translate-x-50{
	transform: translateX(-50%);
}
.-translate-y-50{
	transform: translateY(-50%);
}
.translate-x-50{
	transform: translateX(50%);
}
.translate-y-50{
	transform: translateY(50%);
}
.word-wrap{
	word-break: break-all;
}
.title-lx{
	font-size: 16px;
	font-weight: 500;
	color: #333333;
	padding-left: 12px;
	position: relative;
	margin-top: 25px;
	margin-bottom: 16px;
	&:after{
		position: absolute;
		content: "";
		width: 4px;
		height: 16px;
		background: #3887F5;
		left: 0;
		top: 50%;
		transform: translateY(-50%);
	}
}

.select-wrap{
	-webkit-user-select: text !important; /* Safari */
	-moz-user-select: text !important;    /* Firefox */
	-ms-user-select: text !important;     /* IE/Edge */
	user-select: text !important;         /* 标准语法 */
}

.pre-line-tooltip{
	 white-space: pre-line;
}