<template>
  <div class="bg-[#800003]">
    <div class="h-[648px] relative">
      <img class="absolute w-full z-0 h-full left-0 top-0"
           src="@/views/goodNewsManagement/uploadGoodNews/imgs/<EMAIL>" alt="" />
      <div class="relative z-1 pt-[255px] flex flex-column items-center">
        <div class="avatar flex items-center justify-center w-[200px] h-[200px]">
          <img class="block w-[140px] h-[140px] rounded-[50%] overflow-hidden object-cover"
               :src="data.institutionLogo" alt="" />
        </div>
        <img class="relative mt-[-47px] w-[224px] h-[52px]"
             src="@/views/goodNewsManagement/uploadGoodNews/imgs/<EMAIL>" alt="" />

        <div class="mt-[20px] font-bold text-[22px] text-[#FFFFFF] w-[389px] text-center">
          {{data.institutionName}}【{{data.staffName}}】在{{data.regionName}}成案
        </div>

        <div class="flex justify-center pt-[20px]">
          <p class="w-[20px] font-bold text-[18px] text-[#FFDFAE]">标的额</p>
          <p class="font-bold text-[54px] !pl-[14px] !pr-[8px]  text-[#FFDFAE]">¥{{amount||0}}</p>
          <p class="font-bold text-[16px] text-[#FFDFAE] !pt-[40px]">元</p>
        </div>

      </div>
    </div>

    <img class="block h-[38px] w-full" src="@/views/goodNewsManagement/uploadGoodNews/imgs/<EMAIL>" alt="" />

    <div class="text-content">
      <div class="flex items-center justify-center flex-column pt-[45px]">
        <img class="w-[230px] mb-[-28px] relative z-0 h-[37px]"
             src="@/views/goodNewsManagement/uploadGoodNews/imgs/title@2x_3.png" alt="" />
        <div class="w-[390px]  rounded-[13px] overflow-hidden box-border border-solid border-[6px] border-[rgba(255,255,255,0.3)]">
          <div class="bg-[#FFF1E1]  overflow-hidden h-[266px]">
            <div class="relative z-1 mt-[52px] mx-auto w-[330px] border-[0px] border-r-[1px] border-b-[1px] border-solid border-[#FFCBC8]">
              <div class="flex">
                <p
                  class="leading-[44px] w-[140px] text-center  text-[16px] text-[#333333] bg-[#FFEFEF] border-[0px] border-l-[1px] border-t-[1px] border-solid border-[#FFCBC8]">
                  案件类型</p>
                <p
                  class="flex-1 leading-[44px] !pl-[16px] font-bold text-[16px] text-[#333333] bg-[#FFFFFF] border-[0px] border-l-[1px] border-t-[1px] border-solid border-[#FFCBC8]">
                  {{data.typeName||'-'}}</p>
              </div>
              <div class="flex">
                <p
                  class="leading-[44px] w-[140px] text-center  text-[16px] text-[#333333] bg-[#FFEFEF] border-[0px] border-l-[1px] border-t-[1px] border-solid border-[#FFCBC8]">
                  线索类型</p>
                <p
                  class="flex-1 leading-[44px] !pl-[16px] font-bold text-[16px] text-[#333333] bg-[#FFFFFF] border-[0px] border-l-[1px] border-t-[1px] border-solid border-[#FFCBC8]">
                  {{data.clueTypeName||'-'}}</p>
              </div>
              <div class="flex">
                <p
                  class="leading-[44px] w-[140px] text-center  text-[16px] text-[#333333] bg-[#FFEFEF] border-[0px] border-l-[1px] border-t-[1px] border-solid border-[#FFCBC8]">
                  成案周期</p>
                <p
                  class="flex-1 leading-[44px] !pl-[16px] font-bold text-[16px] text-[#333333] bg-[#FFFFFF] border-[0px] border-l-[1px] border-t-[1px] border-solid border-[#FFCBC8]">
                  {{data.caseSuccessDay||0}}天</p>
              </div>
              <div class="flex">
                <p
                  class="leading-[44px] w-[140px] text-center  text-[16px] text-[#333333] bg-[#FFEFEF] border-[0px] border-l-[1px] border-t-[1px] border-solid border-[#FFCBC8]">
                  成案金额</p>
                <p
                  class="flex-1 leading-[44px] !pl-[16px] font-bold text-[16px] text-[#333333] bg-[#FFFFFF] border-[0px] border-l-[1px] border-t-[1px] border-solid border-[#FFCBC8]">
                  {{amount||0}}元</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="flex items-center justify-center flex-column pt-[45px]">
        <img class="w-[230px] mb-[-28px] relative z-0 h-[37px]"
             src="@/views/goodNewsManagement/uploadGoodNews/imgs/<EMAIL>" alt="" />
        <div class="w-[390px]  rounded-[13px] overflow-hidden box-border border-solid border-[6px] border-[rgba(255,255,255,0.3)]">
          <div class="bg-[#FFF1E1] overflow-hidden">
            <div class="pt-[52px] word-wrap pb-[30px] mx-auto w-[330px] font-bold text-[16px] text-[#333333]">
              {{data.caseReview}}
            </div>
          </div>
        </div>
      </div>

      <div class="flex items-center justify-center flex-column pt-[45px]">
        <img class="w-[230px] mb-[-28px] relative z-0 h-[37px]"
             src="@/views/goodNewsManagement/uploadGoodNews/imgs/title@2x_1.png" alt="" />
        <div class="w-[390px]  rounded-[13px] overflow-hidden box-border border-solid border-[6px] border-[rgba(255,255,255,0.3)]">
          <div class="bg-[#FFF1E1] overflow-hidden">
            <div class="pt-[52px] word-wrap pb-[30px] mx-auto w-[330px] font-bold text-[16px] text-[#333333]">
              {{data.caseDetail}}
            </div>
          </div>
        </div>
      </div>
    </div>

    <img class="pt-[32px] pb-[24px] block w-full h-[274px] " src="@/views/goodNewsManagement/uploadGoodNews/imgs/Frame8658@2x_1.png" alt="" />
  </div>
</template>

<script>
import { priceNumberTwo } from "utils/toolMethods";

export default {
  name: "GoodNewsPreview",
  props: {
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
    /* 需不需要转金额*/
    isMoney: {
      type: Boolean,
      default: false,
    },
  },
  computed: {
    /* 转换金额*/
    amount() {
      return this.isMoney ? priceNumberTwo(this.data.amount) : this.data.amount;
    }
  },
};
</script>

<style scoped lang="scss">
.avatar {
  background: url("~@/views/goodNewsManagement/uploadGoodNews/imgs/<EMAIL>") no-repeat;
  background-size: 100% 100%;
}
.text-content{
  background: url("~@/views/goodNewsManagement/uploadGoodNews/imgs/<EMAIL>") no-repeat;
  background-size: 100% 100%;

}
</style>
