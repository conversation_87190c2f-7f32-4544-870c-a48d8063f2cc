export default {
  mounted() {
    this.scrollStart();
  },

  methods: {
    scrollStart() {
      this.$nextTick(() => {
        // 给目标表格增加元素 id
        this.$refs.multipleTable.bodyWrapper.id = "multipleTable";
        // 绑定事件
        this.scrollFunction("", "multipleTable");
      });
    },
    scrollFunction(obj, id) {
      obj = document.getElementById(id);
      if (obj.attachEvent) {
        // attachEvent方法，为某一事件附加其它的处理事件
        // attachEvent 是后绑定先执行，addEventListener 是先绑定先执行
        obj.attachEvent("onmousewheel", this.mouseScroll(obj));
      } else if (obj.addEventListener) {
        obj.addEventListener("DOMMouseScroll", this.mouseScroll(false));
      }
      obj.onmousewheel = obj.onmousewheel = this.mouseScroll(obj);
    },
    // 鼠标滚动方法
    mouseScroll(obj, callback) {
      return function() {
        if (!obj || obj.className.indexOf("is-scrolling-none") >= 0) return;
        // 获取目标事件
        const e = window.event || document.all ? window.event : arguments[0] ? arguments[0] : event;
        let detail, moveForwardStep, moveBackStep;
        let step = 0;
        if (e.wheelDelta) {
          detail = e.wheelDelta;
          moveForwardStep = -1;
          moveBackStep = 1;
        } else if (e.detail) {
          detail = e.detail;
          moveForwardStep = 1;
          moveBackStep = -1;
        }
        step = detail > 0 ? moveForwardStep * 100 : moveBackStep * 100;

        e.preventDefault();
        obj.scrollLeft = obj.scrollLeft + step;
        // obj.scrollTop = obj.scrollLeft + step
        // 当 wheelDelta 小于零时，鼠标向右滚动，
        // 当 wheelDelta 大于 0 时，鼠标时向左滚动
        if (obj.scrollLeft === 0 && e.wheelDelta > 0) {
          // obj.scrollTop = obj.scrollTop + step
        //   console.log('右边达到顶点 --------')
        } else if ((obj.clientWidth + obj.scrollLeft) >= obj.scrollWidth && e.wheelDelta < 0) {
        //   console.log('z左 --------边达到顶点 --------')
        }
        // obj.scrollTop = obj.scrollTop + step
        // console.log('  obj.scrollLeft  ', obj.offsetWidth, obj.clientWidth, obj.scrollWidth, e.wheelDelta, obj.scrollLeft)
      };
    }

  }
};
