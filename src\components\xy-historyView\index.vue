<template>
	<div class="tags-view-container pr mt-[16px]">
		<div class="tags-view-wrapper flex ml-[24px] flex-wrap gap-[5px]">
			<router-link
				v-for="tag in visitedViews"
				ref="tag"
				:class="isActive(tag) ? 'active' : ''"
				:to="{ path: tag.path, query: tag.query, fullPath: tag.fullPath }"
				:key="tag.path"
				tag="span"
				class="tags-view-item flex items-center rounded-tl-[4px] rounded-br-[4px] rounded-tr-[4px] rounded-bl-[4px] py-[6px] px-[12px]"
				@click.middle.native="closeSelectedTag(tag)"
				@contextmenu.prevent.native="openMenu(tag, $event)"
			>
				<div class="flex items-center">
					<div>{{ filterTitle(tag) }}</div>
					<i
						class="iconfont icon-xianxingtubiao-13 block h-[16px] ml-[8px]"
						@click.prevent.stop="closeSelectedTag(tag)"
					/>
				</div>
			</router-link>
		</div>
    <div class="refresh-btn pa" @click="restRouter()"><i class="iconfont icon-shuaxin" /> 刷新</div>
		<ul v-show="visible" :style="{ left: left + 'px', top: top + 'px' }" class="contextmenu">
			<li @click="refreshSelectedTag(selectedTag)">刷新</li>
			<li @click="closeSelectedTag(selectedTag)">关闭</li>
			<li @click="closeOthersTags">关闭其他</li>
			<li @click="closeAllTags">关闭全部</li>
		</ul>
	</div>
</template>

<script>
import store from "store";
import { findLastChildrenName } from "router/routerPermission";

export default {
  data() {
    return {
      visible: false,
      top: 0,
      left: 0,
      selectedTag: {},
      fuse: undefined,
    };
  },
  computed: {
    visitedViews() {
      return this.$store.state.historyView.visitedViews;
    },
    clickedViews() {
      return this.$store.state.historyView.clickedViews;
    },
    routes() {
      return this.$store.getters.getMenusList;
    },
  },
  watch: {
    $route() {
      this.addViewTags();
      this.moveToCurrentTag();
    },
    visible(value) {
      if (value) {
        document.body.addEventListener("click", this.closeMenu);
      } else {
        document.body.removeEventListener("click", this.closeMenu);
      }
    },
  },
  mounted() {
    this.addViewTags();
  },
  methods: {
    filterTitle(tag) {
      return tag.title;
    },
    isActive(route) {
      return route.path === this.$route.path;
    },
    addViewTags() {
      const { name } = this.$route;
      if (name) {
        this.$store.dispatch("addView", this.$route);
      }
      return false;
    },
    moveToCurrentTag() {
      const tags = this.$refs.tag;
      this.$nextTick(() => {
        for (const tag of tags) {
          if (tag.to.path === this.$route.path) {
						this.$refs.scrollPane?.moveToTarget(tag);

						// when query is different then update
						if (tag.to.fullPath !== this.$route.fullPath) {
						  this.$store.dispatch("updateVisitedView", this.$route);
						}

						break;
          }
        }
      });
    },
    refreshSelectedTag(view) {
      this.$store.dispatch("delCachedView", view).then(() => {
        this.$store.dispatch("setIsRouterRefresh");
      });
    },
    restRouter(){
      location.reload();
    },
    closeSelectedTag(view) {
      this.$store.dispatch("delView", view).then(({ visitedViews }) => {
        if (this.isActive(view)) {
          const latestView = visitedViews.slice(-1)[0];
          if (latestView) {
            this.$router.push(latestView);
          } else {
            this.isCurrentPage();
          }
        }
      });
    },
    /** 判断当前页面是不是和标签一致 */
    isCurrentPage() {
      const res = store.getters["getMenusList"];

      // 递归找到data下最后一层children的第一个path
      const path = findLastChildrenName(res[0]);

      // 如果path就是当前页面的path，那么就不跳转了
      if (path !== this.$route.path) {
        this.$router.push("/");
      } else {
        // 并且保留当前标签
        this.addViewTags();
      }
    },
    closeOthersTags() {
      this.$router.push(this.selectedTag);
      this.$store.dispatch("delOthersViews", this.selectedTag).then(() => {
        this.moveToCurrentTag();
      });
    },
    closeAllTags() {
      this.$store.dispatch("delAllViews");
      this.isCurrentPage();
    },
    openMenu(tag, e) {
      const menuMinWidth = 105;
      // const offsetLeft = this.$el.getBoundingClientRect().left // container margin left
      const offsetWidth = this.$el.offsetWidth; // container width
      const maxLeft = offsetWidth - menuMinWidth; // left boundary
      // const left = e.clientX - offsetLeft + 15 // 15: margin right
      const left = e.clientX; // 15: margin right

      if (left > maxLeft) {
        this.left = maxLeft;
      } else {
        this.left = left;
      }
      // this.top = e.clientY
      this.top = this.$el.getBoundingClientRect().top + 35;

      this.visible = true;
      this.selectedTag = tag;
    },
    closeMenu() {
      this.visible = false;
    },
  },
};
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.tags-view-container {
	width: 100%;
	box-sizing: border-box;

	.tags-view-wrapper {
    max-width: calc(100% - 100px);
    overflow-y: auto;
		.tags-view-item {
			display: inline-block;
			position: relative;
			cursor: pointer;
			border: 1px solid #d8dce5;
			color: #495060;
			background: #fff;
			font-size: 12px;

			&.active {
				background-color: $primary-color;
				color: #fff;
				border-color: $primary-color;
			}
		}
	}

	.contextmenu {
		margin: 0;
		background: #fff;
		z-index: 100;
		position: absolute;
		list-style-type: none;
		padding: 5px 0;
		border-radius: 4px;
		font-size: 12px;
		font-weight: 400;
		color: #333;
		box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);

		li {
			margin: 0;
			padding: 7px 16px;
			cursor: pointer;

			&:hover {
				background: #eee;
			}
		}
	}
}
</style>

<style rel="stylesheet/scss" lang="scss">
//reset element css of el-icon-close
.tags-view-wrapper {
	.tags-view-item {
		.el-icon-close {
			width: 16px;
			height: 16px;
			vertical-align: 2px;
			border-radius: 50%;
			text-align: center;
			transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
			transform-origin: 100% 50%;

			&:before {
				transform: scale(0.6);
				display: inline-block;
				vertical-align: -3px;
			}

			&:hover {
				background-color: rgba(#fff, 0.2);
				color: #b4bccc;
			}
		}
	}
}
.refresh-btn{
  color: #666666;
  font-size: 14px;
  right: 24px;
  top: 5px;
  cursor: pointer;
}
</style>
