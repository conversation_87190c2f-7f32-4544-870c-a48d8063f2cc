<template>
  <div>
  <!--  支付弹窗  -->
    <app-drawer
      @confirm="handleRootPopupClick"
      @close="handleRootPopupClose"
      @cancel="handleRootPopupClose"
      title="法临币线上充值"
      show-cancel
      size="880px"
      :visible.sync="show"
      confirm-text="立即充值"
    >
      <div>
          <!--  列表     -->
        <ul class="product-lists flex tc" ref="productBox">
          <li v-for="item in lists"  ref="product" :key="item.id" :class="['pr', selectId === item.id && 'checked' , item.activityTag && 'mt-18']" @click="handleProduct(item)">
            <label v-if="item.activityTag" class="icon">{{item.activityTag}}</label>
            <div class="price">
              <p class="price-m">{{priceNumber(item.rechargeAmount)}} <i>元</i> </p>
              <p  class="price-b" v-if="!basics.isNull(item.giftAmount)&&item.giftAmount!==0">送{{priceNumber(item.giftAmount)}}法临币</p>
            </div>
            <div v-if="item.giftAmount"  class="product-date pa tc">{{item.giftValidityDay ? '（赠送有效期'+item.giftValidityDay+'天)' :'永久有效' }}</div>
          </li>
        </ul>
        <!--   列表箭头     -->
        <label v-if="showRightArrow" class="arrow-box arrow-r-box cursor" @click="handleScrollRight('r')"><i class="el-icon-arrow-right" /> </label>
        <label v-if="showLeftArrow" class="arrow-box arrow-l-box cursor" @click="handleScrollRight('l')"><i class="el-icon-arrow-left" /> </label>
        <!-- 图片 -->
        <div class="detail-img flex flex-align-center">
          <img class="block" :src="selectInfo.imageUrl" alt="" />
        </div>

      </div>
      <template #footer>
        <div>
          <div class="btn-box">
            <!--   支付方式     -->
            <div class="pay-list">
              <h4>请选择支付方式</h4>
              <div class="pay-types flex flex-align-center ">
                <div v-for="(ty,i) in payLists" :class="payLists.length < 2 && 'flex flex-space-between flex-align-center plr24'" :key="i" @click="handlePay(ty)">
                  <label class="pay-left">
                    <img :src="ty.icon" />
                    {{ty.title}}
                  </label>
                  <i :class="['iconfont','cursor', payType === ty.code ? 'icon-yixuanze checked' :  'icon-weixuanzhong']" />
                </div>
              </div>
            </div>

          </div>
          <!--    协议    -->
          <div class="agreement">
            <i :class="['iconfont','cursor',  isAgree ?  'icon-yixuanze checked' :  'icon-weixuanzhong']" @click="handleAgreement()" />
            支付即代表同意
            <span class="cursor" @click="handleProtal()">《法临币充值协议》</span>
          </div>

          <!-- 按钮 -->
          <div class="text-right btns pb-[24px]">
            <app-button type="info" @click="handleRootPopupClose()">取消</app-button>
            <app-button class="ml-[16px]" type="primary" @click="handleRootPopupClick()">立即充值</app-button>
          </div>
        </div>
      </template>
    </app-drawer>

    <!--    协议内容    -->
    <agreement :show.sync="showProtocol" />

    <!--   支付结果     -->
    <pay-result v-if="dialogVisibleResult" :dialog-visible.sync="dialogVisibleResult" :info="selectInfo" :order-info="orderInfo" />

  </div>
</template>

<script>
import PayResult from "components/payPop/components/payResult.vue";
import { goodsList } from "@/api/pay";
import { priceNumber } from "utils/toolMethods";
import { createOrderFun } from "components/payPop/components/createOrder";
import { commonConfigKey } from "@/api/common";
import Agreement from "components/payPop/components/agreement.vue";
import AppDrawer from "components/AppDrawer/index.vue";
import AppButton from "components/appButton/index.vue";

export default {
  name: "PayPop",
  components: { AppButton, AppDrawer, Agreement, PayResult },
  data(){
    return{
      isShowDeductNum: false,
      lists: [],
      selectId: null,  // 选中商品的id
      selectInfo: {},

      payLists: [ ],
      payType: null,

      isAgree: false,
      dialogVisibleResult: false,
      orderInfo: {},

      showLeftArrow: false,
      showRightArrow: false,

      showProtocol: false
    };
  },
  computed: {
    show: {
      get(){
        return this.$store.getters.isPayPc;
      },
      set(val){
        this.$store.commit("SET_IS_PAY_PC", val);
      }
    }
  },
  watch: {
    show(){
      if(this.show){
        this.getProduct();
        this.getPayTypes();
      }
    }
  },
  methods: {
    priceNumber,
    // 获取商品
    getProduct(){
      goodsList().then(r => {
        console.log(r, 99);
        this.lists = r || [];
        this.handleProduct(this.lists[0]);
        this.showRightArrow = this.lists.length > 3;
      });
      this.showLeftArrow = false;
    },
    // 获取支付方式
    getPayTypes(){
      commonConfigKey({
        paramName: "LC_PC_PAY_TYPE_LIST",
      }).then(res => {
        if(!res) return;
        try {
          this.payLists = JSON.parse(res);
          this.payType = this.payLists[0]?.code;
        }catch (e) {
          console.log(e);
        }
      });
    },
    // 点击立即充值,下单
    handleRootPopupClick(){
      if(!this.selectId) return this.$message.warning("请选择商品");
      if(!this.payType) return this.$message.warning("请选择支付方式");
      if(!this.isAgree) return this.$message.warning("请勾选支付协议，勾选后才能发起支付");
      const data = {
        goodsId: this.selectId,
        type: 1,
        payType: this.payType
      };

      createOrderFun(data).then(r => {
        this.orderInfo = Object.assign({}, r, data);
        this.dialogVisibleResult = true;
      });

    },
    handleRootPopupClose(){
      this.$store.commit("SET_IS_PAY_PC", false);
    },
    // 点击向右滑动
    handleScrollRight(direction){
      const itemWidth = this.$refs.product && this.$refs.product[0].clientWidth;
      const div  = this.$refs.productBox;
      div.scrollIntoView({ behavior: "smooth" });
      if(direction === "r"){
        div.scrollLeft += itemWidth;
      }else {
        div.scrollLeft -= itemWidth;
      }
      this.showLeftArrow = div.scrollLeft !== 0;
      this.showRightArrow = (  Number(div.scrollLeft.toFixed()) + this.$refs.productBox.clientWidth) !== div.scrollWidth;
      console.log(div.scrollLeft, div.scrollWidth, this.$refs.productBox.clientWidth, itemWidth, 88888);
    },
    // 选中商品
    handleProduct(item){
      this.selectInfo = item;
      this.selectId = item.id;
    },
    // 选中支付方式
    handlePay(item){
      this.payType = item.code;
    },
    // 协议同意
    handleAgreement(){
      this.isAgree = !this.isAgree;
    },
    // 打开协议
    handleProtal(){
      this.showProtocol = true;
    }
  }
};
</script>

<style lang="scss" scoped>
  .product-lists{
    gap: 22px;
    border-radius: 8px 8px 8px 8px;
    // border: 1px solid #F6F7F7;
    padding: 12px 0 24px;
    margin:0px 24px;
    align-items: end;
    overflow-y: auto;
    li{
      background-image: url("~@/assets/images/pay/pay-unchecked.png");
      background-size: 100% 100%;
      flex: 1;
      min-width:  218px;
      height: 224px;
      cursor: pointer;

      .price{
        margin-top: 56px;
      }
      .price-m{
        font-weight: bold;
        font-size: 32px;
        color: #CE6269;

        i{
          font-size: 22px;
          font-style: normal;
        }
      }
      .price-b{
        font-weight: 400;
        font-size: 18px;
        color: #333333;
        margin-top: 8px;
      }

      .product-date{
        font-weight: 400;
        font-size: 16px;
        color: #7D6E6D;
        bottom: 13px;
        left: 0;
        right: 0;
      }
      .icon{
        //width: 133px;
        height: 36px;
        background: linear-gradient( 90deg, #CE5A49 0%, #D88C5C 100%);
        border-radius: 10px 10px 10px 0px;
        font-weight: 500;
        font-size: 16px;
        color: #FFFFFF;
        position: absolute;
        left: 0;
        top: -18px;
        display: block;
        line-height: 36px;
        padding: 0 21px;
      }

      &.mt-18{
        margin-top: 18px;
      }

      &.checked{
        background-image: url("~@/assets/images/pay/pay-checked.png") !important;

        .price-b{
          color: #532A2D !important;
        }
        .product-date{
          color: #FFFFFF !important;
        }
      }
    }
  }

  .arrow-box{
    display: block;
    width: 42px;
    height: 46px;
    background: rgba(51, 51, 51, 0.20);

    color: #000000;
    font-size: 24px !important;
    text-align: center;
    line-height: 46px;
    position: absolute;

    &.arrow-r-box{
      right: 25px;
      top: 205px;
      border-radius: 22px 0 0 22px;
    }
    &.arrow-l-box{
      left: 25px;
      top: 205px;
      border-radius:0px 22px 22px  0;
    }
  }
  .pay-list{
    padding: 0 24px;
    h4{
      font-weight: 500;
      font-size: 16px;
      color: #333333;
    }
    .pay-types{
      font-weight: 400;
      font-size: 18px;
      color: #333333;
      background: #F5F5F7;
      border-radius: 8px 8px 8px 8px;
      padding: 16px 0 ;
      margin-top: 12px;
      margin-bottom: 16px;
      flex-wrap: wrap;
      >div{
        flex: 1;
        text-align: center;

        &.plr24{
          padding: 0 24px;
        }
      }
      img{
        width: 34px;
        height: 34px;
        background: #FFFFFF;
        border-radius: 50%;
        vertical-align: middle;
      }
      i{
        color: rgba(102, 102, 102, 1);

        &.checked{
          color: rgba(56, 135, 245, 1) !important;
        }
      }
    }

  }
  .agreement{
    font-weight: 400;
    font-size: 18px;
    color: #666666;
    padding: 0 24px 24px;
    border-bottom: 1px solid #EEEEEE;
    span{
      color: rgba(56, 135, 245, 1);
    }

    .checked{
      color: rgba(56, 135, 245, 1) !important;
    }
  }
  .detail-img{
    margin: 24px;
    img{
      width: 100%;
    }
  }
  .btns{
    margin-top: 8px;
    margin-right: 24px;
  }
  .btn-box{
    //position: fixed;
    // left: 0;
    // right: 0;
    bottom: 20px;
    width: 880px;
  }
</style>
