export const exportFile = (data, fileName, _this) => {
  // 地址不存在时，禁止操作
  if (!data) return;
  // 下载文件并保存到本地
  // 创建a标签，使用 html5 download 属性下载，
  const link = document.createElement("a");
  // 创建url对象
  const objectUrl = window.URL.createObjectURL(new Blob([data]));
  link.style.display = "none";
  link.href = objectUrl;
  // 自定义文件名称， fileName
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  // 适当释放url
  window.URL.revokeObjectURL(objectUrl);
};
