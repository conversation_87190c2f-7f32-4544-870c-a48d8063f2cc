
/* im消息类型*/
export const imMessageType = {
  /* 用户发送手机号*/
  SERVER_CONTACT_PHONE: "server_contact_phone",
  /* 律师发送服务卡片*/
  SERVER_ONE_TO_ONE: "server_one_to_one",
  /** 2.2.0 im 支付成功 */
  SEVER_OF_PAY_SUCCESS: "sever_of_pay_success",
  /* 律师评价 */
  SERVER_EVALUATE: "lawyer_rate",
  /* 律师电话 */
  CONTACT_INFO_LAWYER: "contact_info_lawyer",
  // 退款
  REFUND_TIP: "refund_tip",
  /* 追问付费服务发送提示*/
  SERVER_QA_APPEND_COUNT_TIP: "server_qa_append_count_tip",
  /* 敏感词提示消息*/
  SERVER_QA_AUTO_SEND_CARD: "server_qa_auto_send_card"
};

/* ! im消息对应的卡片 对应vue文件的nanme*/
export const imCardMap = {
  [imMessageType.SERVER_CONTACT_PHONE]: "userPhoneCard",
  [imMessageType.SERVER_ONE_TO_ONE]: "serveCard",
  [imMessageType.SERVER_EVALUATE]: "appraiseCard",
  [imMessageType.CONTACT_INFO_LAWYER]: "contactInfoLawyerCard",
  [imMessageType.REFUND_TIP]: "refundTipCard",
  /* 自动回复*/
  lawyer_auto_reply: "lawyerAutoReplyCard",
  /* 法律意见书*/
  server_lawyer_idea: "submissions-card",
  /* 用户提问卡片*/
  server_QA_asked: "user-questions-card",
  [imMessageType.SERVER_QA_APPEND_COUNT_TIP]: "server-qa-append-count-tip-card",
  [imMessageType.SERVER_QA_AUTO_SEND_CARD]: "sensitive-word-tip-card",
  /* 用户已申请换律师，服务已关闭*/
  server_change_lawyer_tips: "refundTipCard"
};

/* 不需要显示头像的card*/
export const personLessMsgCardMap = [
  imMessageType.SERVER_EVALUATE,
  imMessageType.REFUND_TIP,
  "invite_comment",
  "invite_complete",
  "lawyer_services_end",
  "service_instance",
  "server_upgrade",
  "one-on-one_qa",
  "server_QA_addCount",
  "server_change_lawyer_tips",
  imMessageType.SEVER_OF_PAY_SUCCESS,
  imMessageType.SERVER_QA_APPEND_COUNT_TIP,
  imMessageType.SERVER_QA_AUTO_SEND_CARD
];

/* im会话列表 tag配置*/
export const imConversationTagMap = {
  /* 1V1咨询*/
  ONE_TO_ONE: {
    text: "1V1咨询",
    style: {
      color: "#FA700D",
      backgroundColor: "#FFF7EB",
      borderColor: "#FFCFAC"
    }
  },
  /* 公共咨询*/
  PUBLIC_CONSULTATION: {
    text: "公共咨询",
    style: {
      color: "#3887F5",
      backgroundColor: "#EDF3F7",
      borderColor: "#A0CFFB"
    }
  },
  /* 问答*/
  QUESTION_AND_ANSWER: {
    text: "问答",
    style: {
      color: "#22BF7E",
      backgroundColor: "#E6F5EC",
      borderColor: "#B7E7D5"
    }
  }
};

/* im 案源类型*/
export const imCaseSourceType = {
  /* 咨询*/
  CONSULTATION: 1,
  /* 问答*/
  QUESTION_AND_ANSWER: 2
};

/* im 刷新代码指令code*/
export const imDirectivesFlushedCodes = [
  "user_refund_already",
  "user_change_lawyer_apply",
  "user_change_lawyer_revocation",
  "user_change_lawyer_already",
  "lawyer_start_server",
  "user_refunding",
  "server_complete",
  "server_refresh"
];
