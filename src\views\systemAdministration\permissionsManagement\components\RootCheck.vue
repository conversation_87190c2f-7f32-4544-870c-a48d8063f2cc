<template>
  <el-checkbox v-model="checked"  :key="personnelId" @change="checkboxChange" :disabled="data.disabled" :label="buttonData.remark" />
</template>

<script>
import { updateStaffModuleFuncById } from "@/api/system";

export default {
  name: "RootCheck",
  inject: ["getPersonnelId"],
  props: {
    /* 按钮配置*/
    buttonData: {
      type: Object,
      default: () => {
        return {};
      },
    },
    /* tree数据*/
    data: {
      type: Object,
      default: () => {
        return {};
      },
    },
  },
  data() {
    return {
      checked: false
    };
  },
  computed: {
    /* 是否选中*/
    // checked(){
    //   console.log(this.enableFunc.indexOf(this.buttonData.code) > -1)
    //   return this.enableFunc.indexOf(this.buttonData.code) > -1;
    // },
    /* 当前用户勾选的权限*/
    enableFunc(){
      return this.basics.isNull(this.data.enableFunc) ? [] : this.data.enableFunc.split(",");
    },
    /* 人员id*/
    personnelId() {
      return this.getPersonnelId();
    }
  },
  watch: {
    enableFunc: {
      handler: "setChecked",
      immediate: true,
    }
  },
  methods: {
    setChecked(){
      this.checked = this.enableFunc.indexOf(this.buttonData.code) > -1;
    },
    checkboxChange(value){
      /*
      * moduleId	是	Long	菜单id
staffId	是	Long	人员id
function	是	String	功能权限code
check	是	Integer	0取消勾选，1勾选
      *
      * */
      updateStaffModuleFuncById({
        moduleId: this.data.id,
        staffId: this.personnelId,
        function: this.buttonData.code,
        check: ~~value
      }).catch(() => {
        this.checked = !value;
      });
    }
  }
};
</script>

<style scoped lang="scss">

</style>
