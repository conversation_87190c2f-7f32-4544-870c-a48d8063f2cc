<template>
	<div
		v-if="show"
		class="fixed z-[9990] left-[52px] bottom-[150px] px-[16px] py-[6px] box-border bg-[#FFFFFF] [box-shadow:0px_2px_12px_0px_rgba(0,0,0,0.12)] rounded-tl-[8px] rounded-br-[8px] rounded-tr-[8px] rounded-bl-none"
	>
		<div class="flex items-center">
			<div class="text-[14px] text-[#333333] w-[112px] shrink-0 mr-[26px]">
				<div>
					上传律师成单喜报
				</div>
				<div>
					免费领取专属福利
				</div>
			</div>
			<app-button type="success" class="shrink-0" @click="upload">去上传</app-button>
			<i
				class="iconfont icon-guanbi ml-[8px] text-[14px] cursor-pointer text-[#CCCCCC]"
				@click="handleClose"
			/>
		</div>
	</div>
</template>

<script>
import AppButton from "components/appButton/index.vue";
import moment from "moment";
import { turnToUploadGoodNews } from "utils/turnPage";

/**
 * https://lanhuapp.com/web/#/item/project/product?tid=43efda02-ce73-4682-acd1-afc75cbf6b0c&pid=8bdb9ebf-6030-471f-b626-cdf914769d16&versionId=4ef030d7-1290-4033-8c13-c6b593dda353&docId=f1805a1a-974c-412a-a19e-8a6ec5095782&docType=axure&pageId=749858ee607f4099ae7435fd1cd35ea9&image_id=f1805a1a-974c-412a-a19e-8a6ec5095782&parentId=022d5b44-caa6-48df-83ee-68fbe8b177ff
 */
export default {
  name: "GoodNewsUpdateGuide",
  components: { AppButton },
  data() {
    return {
      show: false,
    };
  },
  created() {
    this.getBeforeCloseTime();
  },
  methods: {
    getBeforeCloseTime() {
      const time = localStorage.getItem("GoodNewsUpdateGuideTime");

      if (time) {
        // 于上次关闭时间是否差了一个自然日
        this.show = moment(time).isBefore(moment().startOf("day"));
        return;
      }

      this.show = true;
    },
    handleClose() {
      localStorage.setItem("GoodNewsUpdateGuideTime", moment().format("YYYY-MM-DD"));

      this.show = false;
    },
    upload() {
      turnToUploadGoodNews();

      this.handleClose();
    },
  },
};
</script>
