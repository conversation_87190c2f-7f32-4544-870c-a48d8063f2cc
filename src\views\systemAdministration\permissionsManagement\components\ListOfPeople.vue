<template>
  <div>
    <permissions-header>选择人员</permissions-header>
    <div class="flex items-center px-[12px] mx-[14px] bg-[#F5F5F7] rounded-[2px] my-[8px]">
      <i class="iconfont icon-xianxingtubiao-12 !text-[#999999] !text-[16px]" />
      <input placeholder="搜索人员"
             class="text-[14px] text-[#333333] border-none h-[32px] p-0 flex-1 outline-none bg-[#F5F5F7] indent-[8px] w-full"
             v-model="params.userName"
             type="text" />
      <app-button style-type="text" class="!pl-[8px] !pr-[0] flex-shrink-0" @click="search">搜索</app-button>
    </div>
    <div class="px-[24px]  list-container">
      <div v-for="i in staffList" :key="i.id"
         :class="{'bg-[#EDF3F7]':i.id===selectedStaffId}"
         @click="selectStaff(i.id)"
         class="cursor-pointer items-center flex  text-[14px] mt-[8px] rounded-[4px] hover:bg-[#EDF3F7] first-of-type:mt-[4px] h-[40px]">
        <p class="text-ellipsis text-[#333333]  indent-[12px]">{{ i.userName }}</p>
        <span v-if="i.isAdmin===1" class="flex-shrink-0 px-[4px] ml-[8px] leading-[20px] text-center rounded-[4px] border-[1px] border-solid border-[#A0CFFB] text-[12px] text-[#3887F5]">
          管理员
        </span>
      </div>
    </div>
  </div>
</template>

<script>
import PermissionsHeader from "views/systemAdministration/permissionsManagement/components/PermissionsHeader.vue";
import AppButton from "components/appButton/index.vue";
import { lcInstitutionStaffList } from "@/api/system";

export default {
  name: "ListOfPeople",
  components: { AppButton, PermissionsHeader },
  data() {
    return {
      /* 人员列表*/
      staffList: [],
      /* 选中的人员下标*/
      selectedStaffId: "",
      /* 搜索内容*/
      inputText: "",
      /* 查询条件*/
      params: {
        userName: ""
      },
      /* 滚动加载状态*/
      loading: false
    };
  },
  computed: {
    disabled () {
      return this.loading || this.noMore;
    }
  },
  activated() {
    const id = this.getLinkParams();
    if(this.basics.isArrNull(this.staffList) || id){
      if(id){
        this.params.userName = "";
      }
      this.load(id);
    }
  },
  methods: {
    getLinkParams(){
      const id = this.$route.query.id;
      if(id){
        return Number(id);
      }
      return  "";
    },
    /* 滚动加载*/
    load(id){
      this.loading = true;
      lcInstitutionStaffList(this.params).then((data) => {
        this.staffList = data;
        /* 默认选中第一个*/
        this.selectStaff(this.basics.isNull(id) ? (this.staffList[0] || {}).id : id);
      }).finally(() => {
        this.loading = false;
      });
    },
    /* 人员选择*/
    selectStaff(id){
      console.log(id, "===id");
      /* 判断当前点击的是不是当前激活的  防止多次点击*/
      if(id === this.selectedStaffId) return;
      this.selectedStaffId = id;
      let data = this.staffList.find(item => item.id === id);
      if(!data) data = {
        id: undefined,
        userName: undefined
      };
      this.$emit("selectStaff", data);
    },
    // 搜索
    search(){
      this.load();
    }
  }
};
</script>

<style scoped lang="scss">
.list-container {
  overflow-y: auto;
  height: calc(100vh - 237px);
}
</style>
