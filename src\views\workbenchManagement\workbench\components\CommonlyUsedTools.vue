<template>
  <div class="bg-[#FFFFFF] rounded-[2px]">
    <workbench-card-title>常用工具</workbench-card-title>
    <div class="flex flex-wrap pt-[16px]">
      <div
        class=" flex flex-column items-center tool-item   cursor-pointer hover:text-[#3887F5]  text-[#333333] "
           v-for="i in toolsConfig" @click="i.click"  :key="i.name">
        <img class="block w-[48px] h-[48px]" :src="i.icon" alt="" />
        <!-- <img class="block w-[48px] h-[48px]" :src="i.meta && i.meta.icon" alt="" /> -->
        <!-- <p class="text-[14px] !pt-[10px]">{{ i.meta && i.meta.title }}</p> -->
        <p class="text-[14px] !pt-[10px]">{{ i.label }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import WorkbenchCardTitle from "views/workbenchManagement/workbench/components/CardTitle.vue";
import { turnToCasePush, turnToGoodNews, turnToInvoice, turnToAutoCasePush } from "utils/turnPage";
import { hasMenuPermissions } from "router/menuPermissions";
import menuPermissionIDEnum from "@/enum/menuPermissionIDEnum";

export default {
  name: "CommonlyUsedTools",
  components: { WorkbenchCardTitle },
  data() {
    return {
      list: []
    };
  },
  computed: {
    /* 判断是不是超级管理员*/
    isAdministrator() {
      return this.$store.getters.isAdministrator;
    },
    toolsConfig(){
      return [
        { command: "invoice", icon: "https://oss.imlaw.cn/test/image/2025/07/01/a563ddb89a1c49dcb22c4dcd12a23789.png", show: this.isAdministrator, label: "开发票", click: turnToInvoice },
        // { command: "invoiceRecord", icon: "https://oss.imlaw.cn/test/image/2025/07/01/d4aa710ed2f2411bbf7cd7909e52047b.png", show: this.isAdministrator, label: "开票记录", click: turnToInvoiceRecord },
        { command: "laborCalculator", icon: "https://oss.imlaw.cn/test/image/2025/07/01/1df59c7ac77249e888f70875b4b87ab2.png", label: "劳动仲裁计算器", click: () => {this.openTools(1);} },
        { command: "lawyerFeeCalculator", icon: "https://oss.imlaw.cn/test/image/2025/07/01/b809cddd5a6f45bd9d433d0e2f6c0cce.png", label: "律师费计算器", click: () => {this.openTools(0);} },
        { command: "invoiceRecord",  icon: "https://oss.imlaw.cn/pro/law_cloud_icon/caseSourcePush.png", show: hasMenuPermissions(menuPermissionIDEnum.AUTO_ROB_CASE_SOURCE), label: "自动抢案源", click: turnToAutoCasePush },
        { command: "invoiceRecord",  icon: "https://oss.imlaw.cn/test/image/2025/07/29/f0431e09781f4adca7af29283c79a386.png", show: hasMenuPermissions(menuPermissionIDEnum.CASE_SOURCE_AUTO_PUSH), label: "案源推送", click: turnToCasePush },
        { command: "goodNewsSquare", icon: "https://oss.imlaw.cn/pro/law_cloud_icon/goodNews.png", label: "成单喜报", click: turnToGoodNews },
      ].filter(item => {
        return this.basics.isNull(item.show) || item.show;
      });
    }
  },
  mounted() {
    // this.getMenus(this.menusList);
  },
  methods: {

    /* 打开工具*/
    openTools(activeTabBarIndex) {
      this.$store.commit("SET_CALCULATOR", {
        isShow: true,
        showIndex: activeTabBarIndex
      });
    },
    /* 获取所有菜单 最后一级*/
    getMenus(list) {
      (list || []).forEach(item => {
        if (item.children && item.children.length > 0) {
          this.getMenus(item.children);
        } else {
          /* 去除工作台页面*/
          if (item.name !== this.$route.name) {
            this.list.push(item);
          }
        }
      });
    },
    jump(data) {
      this.$router.push({ name: data.name });
    }
  }
};
</script>

<style scoped lang="scss">
.tool-item {
  width: pxToPer(130);
  padding-bottom: 26px;
  // width: pxToPer(106);
}
</style>
