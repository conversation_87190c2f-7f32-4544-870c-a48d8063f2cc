<template>
  <div class="pb-[24px]">
    <div class="cell flex flex-wrap">
      <div class="cell-item w-2/4 " v-for="(i) in list" :class="i.className||''" :key="i.prop">
        <div class="cell-left flex">{{i.label}}</div>
        <div class="cell-right flex">
          <div class="flex"  v-if="i.type==='img'||(i.prop==='contractImg'&&!basics.isNull(info[i.prop]))">
            <el-image class="img"  :preview-src-list="[img]" v-for="(img,index) in (info[i.prop]||'').split(',')" :src="img" :key="index" alt="" />
          </div>
          <!-- 这里是为了弄紧急需求 就这样了  合同模板 可能是图片也可能是文件         -->
          <div class="flex file items-center" v-else-if="i.prop==='contractImg'&&!basics.isNull(info['contractFileUrl'])">
            <img class="w-[28px] h-[28px]" src="@/assets/images/<EMAIL>" alt="" />
            <p class="!pl-[8px] flex-1 text-ellipsis-3 min-w-[198px]">{{info.fileName}}</p>
            <div class="flex items-center pl-[8px] pr-[20px]">
              <div class="text-center cursor-pointer" @click="preview">
                <img class="m-auto w-[16px] h-[16px] block" src="@/assets/images/<EMAIL>" alt="" />
                <p class="text-[12px] text-[#3887F5] text-center">预览</p>
              </div>
              <div class="text-center pl-[16px] cursor-pointer" @click="download">
                <img class="m-auto w-[16px] h-[16px] block" src="@/assets/images/<EMAIL>" alt="" />
                <p  class="text-[12px] text-[#3887F5] text-center">下载</p>
              </div>
            </div>
          </div>
          <p v-else>{{info[i.prop]}}</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { baseList } from "views/systemAdministration/lawFirmInformation/index.js";
import { lcLawFirmsInstitutionDetail } from "@/api/system.js";
import { dataDetailList } from "@/api/common.js";
import FileSaver from "file-saver";

export default {
  name: "BasicsInfo",
  data() {
    return {
      list: baseList,
      info: {},
      /* 入驻类型列表*/
      listOfOccupancyTypes: [],
      /* 线索可见范围*/
      listOfClueRange: [],
      /* 机构类型列表*/
      listOfInstitutionTypes: []
    };
  },
  mounted() {
    /* 入驻类型字典 线索可见范围*/
    const promiseAll = [
      dataDetailList({ groupCode: "LC_LAW_FIRMS_INSTITUTION_SETTLE_IN_TYPE" }),
      dataDetailList({ groupCode: "LC_CLUE_RANGE" }),
      dataDetailList({ groupCode: "INSTITUTION_TYPE" })
    ];

    /* 获取机构基本信息*/
    lcLawFirmsInstitutionDetail().then(data => {
      this.info = data;
      Promise.all(promiseAll).then((arr) => {
        this.listOfOccupancyTypes = arr[0];
        this.listOfClueRange = arr[1];
        this.listOfInstitutionTypes = arr[2];
        /* 过滤一下列表需要请求字典的字段*/
        this.list.forEach((item) => {
          if(item.list){
            this.info[item.prop] = this.filterList(this.info[item.prop], this.basics.isString(item.list) ? this[item.list] : item.list);
          }
        });
      });
    });
  },
  methods: {
    filterList(value, list){
      return (list || []).find(item => String(value) === String(item.value))?.label;
    },
    preview(){
      this.$store.dispatch("setIframePreviewVisible", {
        title: "文件预览",
        previewUrl: this.info.contractFileUrl,
        nestApp: false,
        isFilePreview: true
      });
    },
    download(){
      FileSaver.saveAs(this.info.contractFileUrl, this.info.fileName);
    }
  },
};
</script>

<style scoped lang="scss">
.cell{
  border: 1px solid #EEEEEE;
  border-bottom: none;
  border-right: none;

  .cell-item{
    border-bottom: 1px solid #EEEEEE;
    border-right: 1px solid #EEEEEE;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    &:nth-child(odd){
      border-right: none;
    }
    &:last-child{
      border-right: 1px solid #EEEEEE;
    }
    .cell-left{
      width: 202px;
      font-size: 14px;
      font-weight: 500;
      background: #F5F5F7;
      text-align: right;
      color: #333333;
      height: 100%;
      align-items: center;
      justify-content: end;
      box-sizing: border-box;
      padding-right: 24px;
    }
    .cell-right{
      flex: 1;
      align-items: center;
      font-size: 14px;
      font-weight: 400;
      color: #666666;
      min-height: 48px;
      padding-left: 24px;
      .img{
        width: 72px;
        height: 72px;
        padding-top: 12px;
        padding-bottom: 12px;
        object-fit: cover;
        padding-left: 8px;
        @extend .cursor-pointer;
        &:first-child{
          padding-left: 0;
        }
      }
    }
  }
}
</style>
