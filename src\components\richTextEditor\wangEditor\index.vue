<template>
  <div class="wangEditor">
      <div :id='id' class="editor" />
  </div>
</template>

<script>
import Editor from "wangeditor";
import axios from "axios";
import { uplodImages } from "@/api/common";
export default {
  name: "WangEditor",
  props: {
    meanList: {
      type: Array,
      default: () => []
    },
    value: {
      type: String,
      default: ""
    },
    height: {
      type: [Number, String],
      required: false,
      default: 500
    },
    id: {
      type: String,
      default: function() {
        return "editor-" + +new Date() + ((Math.random() * 1000).toFixed(0) + "");
      }
    }
  },
  data() {
    return {
      editor: "",
      htmls: "",
      hasChange: false,
      hasFocus: false
    };
  },
  watch: {
    value: {
      handler(val) {
        if (!this.hasChange && val) {
          this.$nextTick(() => {
            this.editor.txt.html(val);
            this.editor.selection.moveCursor(this.editor.$textElem.elems[0], false);
          });
        }
      },
      deep: true,
      immediate: true
    }
  },
  created() {
  },
  mounted() {
    this.getEditor();
  },
  methods: {
    // 编辑器的加载
    getEditor() {
      // js  自定义自己所需要的
      this.editor = new Editor("#" + this.id); /* 括号里面的对应的是html里div的id */
      /*
        配置菜单栏
        */
      this.editor.config.menus = [
        "head", // 标题
        "bold", // 粗体
        "fontSize", // 字号
        "fontName", // 字体
        "italic", // 斜体
        "underline", // 下划线
        "strikeThrough", // 删除线
        "foreColor", // 文字颜色
        "backColor", // 背景颜色
        "link", // 插入链接
        "list", // 列表
        "justify", // 对齐方式
        "quote", // 引用
        "emoticon", // 表情
        "image", // 插入图片
        "table", // 表格
        "code", // 插入代码
        "undo", // 撤销
        "redo" // 重复，
      ];
      /* 设置编辑器的高度 */
      this.editor.config.height = this.height;

      /* 自定义图片上传（支持跨域和非跨域上传，简单操作） */
      this.editor.config.customUploadImg = async(files, insert) => {
        /* files 是 input 中选中的文件列表 */
        const formData = new FormData();
        const file = files[0];
        formData.append("file", file);

        /* 调用后台提供的上传图片的接口 */
        await axios.post(uplodImages, formData, { headers: { "Content-Type": "multipart/form-data" } }).then(({ data }) => {
          // console.log(data)
          if (data.data) {
            insert(data.data);
          } else {
            this.$message.error({
              type: "info",
              message: data.message || "上传文件失败"
            });
          }
          //  loading.close();
        });
        /* insert 是编辑器自带的 获取图片 url 后，插入到编辑器的方法 上传代码返回结果之后，将图片插入到编辑器中 */
      };

      this.editor.config.onchange = (html) => {
        this.hasChange = true;
        this.$emit("input", html);
      };

      this.editor.create(); /* 创建编辑器 */
      // console.log(this.editor)
    }
  }
};
</script>
<style lang="scss" scoped>
.wangEditor{
  margin-bottom: 20px;
}
</style>
