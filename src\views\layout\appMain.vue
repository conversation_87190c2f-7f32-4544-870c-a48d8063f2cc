
<template>
  <section class="main-page" id="app-main">

    <!-- <div class="top-scroll">回到顶部</div> -->
<!--    <el-backtop target="#app-main" :bottom="80"></el-backtop>-->
    <transition name="fade-transform" mode="out-in">
      <div v-if="isRouterRefresh">
        <keep-alive max="5">
          <router-view v-if="cachedView" :key="key" />
        </keep-alive>
        <router-view v-if="!cachedView"  :key="key" />
      </div>

    </transition>
  </section>
</template>

<script>

import watermark from "utils/waterMark";
import { getDictValueByOrgDescKey } from "utils/tools";

export default {
  name: "AppMain",
  data() {
    return {
      getIsSpecialCustomerService: false
    };
  },
  computed: {
    // 页面刷新
    isRouterRefresh() {
      return this.$store.state.common.isRouterRefresh;
    },
    // 需要缓存的页面
    cachedView() {
      return this.$route.meta.cachedView; // 需要做页面的keep-alive就开启这个
    },
    key() {
      return this.$route.fullPath;
    },
    userInfo() {
      return this.$store.getters.userInfo;
    }
  },
  mounted() {
    setTimeout(async () => {
      try {
        await getDictValueByOrgDescKey({ key: "showType" }).then(data => {
          this.getIsSpecialCustomerService = data === 1;
          return data;
        });
      }catch (e) {
        console.log(e);
      }
      watermark.set(`${this.userInfo.institutionName}-${this.userInfo.userName} ${this.getIsSpecialCustomerService ? "," : ",律客云管理平台"}`, "app-main");
    }, 50);
  }
};
</script>

<style scoped>
</style>
