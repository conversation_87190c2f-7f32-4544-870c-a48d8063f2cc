<!--
 * @Description: 搜索左侧的标题
 * @FilePath: \law-front\src\components\xy-header\search.vue
-->
<template>
<div :class="{'show':show}" class="header-search">
    <i class="el-icon-search" style="font-size:22px"  @click.stop="click" />
    <el-select
      ref="headerSearchSelect"
      v-model="search"
      :remote-method="querySearch"
      filterable
      default-first-option
      remote
      placeholder="Search"
      class="header-search-select"
      @change="change"
    >
      <el-option v-for="item in options" :key="item.path" :value="item" :label="item.title" />
    </el-select>
  </div>
</template>

<script>
// 搜索过滤js
import Fuse from "fuse.js";

export default {
  name: "SearchHead",
  data() {
    return {
      search: "",
      options: [],
      show: false,
      fuse: undefined
    };
  },
  computed: {
    routes() {
      return this.$store.getters.getHandleMeanList;
    }
  },
  watch: {
    show(value) {
      if (value) {
        document.body.addEventListener("click", this.close);
      } else {
        document.body.removeEventListener("click", this.close);
      }
    }
  },
  mounted() {
    this.initFuse();
  },
  methods: {
    //   搜索点击，展示输入框
    click() {
      this.show = !this.show;
      if (this.show) {
        this.$refs.headerSearchSelect && this.$refs.headerSearchSelect.focus();
      }
    },
    change(val) {
      this.$router.push(val.path);
      this.search = "";
      this.options = [];
      this.$nextTick(() => {
        this.show = false;
      });
    },
    // 过滤方法
    querySearch(query) {
      if (query !== "") {
        const searchData = this.fuse.search(query);
        console.log(searchData);
        this.options = searchData.map(res => res.item);
      } else {
        this.options = [];
      }
    },
    // 初始化过滤组件
    initFuse() {
      this.fuse = new Fuse(this.routes, {
        keys: [{
          name: "title",
          weight: 0.7
        }, {
          name: "path",
          weight: 0.3
        }]
      });
    }
  }
};
</script>

<style lang="scss" scoped>
  .header-search {
  font-size: 0 !important;
  .search-icon {
    cursor: pointer;
    font-size: 18px;
    vertical-align: middle;
  }
  .header-search-select {
    font-size: 18px;
    transition: width 0.2s;
    width: 0;
    overflow: hidden;
    background: transparent;
    border-radius: 0;
    display: inline-block;
    vertical-align: middle;
    ::v-deep .el-input__inner {
      border-radius: 0;
      border: 0;
      padding-left: 0;
      padding-right: 0;
      box-shadow: none !important;
      border-bottom: 1px solid #ffffff;
      vertical-align: middle;
      background: #ffffff;
      color: #8C8C8C;

      &::placeholder{
          color: #8C8C8C;
      }
    }
  }
  &.show {
    .header-search-select {
      width: 210px;
      margin-left: 10px;
    }
  }
}
</style>
