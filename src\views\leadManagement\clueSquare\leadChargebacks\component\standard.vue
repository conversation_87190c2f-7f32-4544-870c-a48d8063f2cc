<template>
  <div>
    <app-button style-type="text" class="btn" @click="visible = true">案源退单标准</app-button>
    <!-- 案源退单标准 弹窗   -->
    <app-popup
      title="案源退单标准"
      width="560px"
      :hide-footer="true"
      :visible.sync="visible">
      <div class="texts-word">
        <p class="mb-16">律客云平台一直以来致力于打造公平、有效的法律咨询平台，为了维护全体律师的合法权益，我司制定了《案源退单标准》。请您仔细阅读相关政策，确保对其内容及相应后果已全部知晓并充分理解</p>

        <p>退单说明：</p>
        <p>1. 只能在锁定案源成功后{{hourVal}}小时内申请退单, 且只有一次退单的机会</p>
        <p>2. 在线索-线索退单进行退单申请，再输入需要退单的线索ID号，选择退单原因并填写说明，添加相应附件凭证即可提交退单申请</p>
        <p>3.平台收到申请后会在3~5个工作日核实退款原因</p>
        <p class="mb-16">4.抢单权益数、法临币数将在平台审核通过后当日退回律所账户</p>

        <p>案源退单核实时效：</p>
        <p class="mb-16">案源退单承诺在3-5个工作日内核实完毕</p>

        <p>可申请退单的案源类型：</p>
        <p>1、联系方式异常：当事人预留手机号为空号（需提供联系截图）</p>
      </div>
    </app-popup>
  </div>
</template>

<script>
import AppButton from "components/appButton/index.vue";
import AppPopup from "components/appPopup/index.vue";

export default {
  name: "RefundStandard",
  components: { AppPopup, AppButton },
  props: {
    hourVal: {
      type: Number,
      default: 24
    }
  },
  data(){
    return{
      visible: false
    };
  }
};
</script>

<style lang="scss" scoped>
.btn{
  font-weight: 400;
  font-size: 14px;
  color: #3887F5;
}
.texts-word{
  padding: 16px 24px;
  font-weight: 400;
  font-size: 14px;
  color: #666666;
  line-height: 22px;
}
.mb-16{
  margin-bottom: 20px;
}
</style>
