<template>
  <div class="min-h-[250px] mt-[16px] bg-[#FFFFFF] rounded-[2px]">
    <workbench-card-title>
      律所动态
    </workbench-card-title>
    <div class="px-[24px] pt-[8px] pb-[16px]">
      <p class="text-[14px] text-[#333333] text-ellipsis leading-[36px]" v-for="(i,index) in list" :key="index">{{i.content}}</p>
    </div>
  </div>
</template>

<script>
import WorkbenchCardTitle from "views/workbenchManagement/workbench/components/CardTitle.vue";
import { stagingDynamics } from "@/api/workbenchManagement.js";

export default {
  name: "LawFirmDynamics",
  components: { WorkbenchCardTitle },
  data() {
    return {
      list: []
    };
  },
  mounted() {
    stagingDynamics().then(data => {
      this.list = data;
    });
  }
};
</script>

<style scoped lang="scss">

</style>
