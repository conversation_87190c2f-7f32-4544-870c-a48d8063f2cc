
import { scrollTop } from "@/utils/toolMethods";
export default {
  created() {
    this.getShowPageInfos();
  },

  methods: {
    //  显示表头信息
    getShowPageInfos() {
      this.$store.dispatch("setBreadcrumb", this.listName);
      this.$store.dispatch("setShowBackButton", true);
      scrollTop();
    },
    // 关闭详情页面
    handleBack() {
      // 关闭详情页
      this.$store.dispatch("setShowDetailPage", false);
      // 修改面包屑
      this.$store.dispatch("setBreadcrumb", [this.$route.meta.title]);
      // 隐藏返回按钮
      this.$store.dispatch("setShowBackButton", false);

      scrollTop();
    }

  }

};
