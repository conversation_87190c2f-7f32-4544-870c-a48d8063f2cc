<template>
  <div class="wrap">
    <h5>退单详情 <i>请如实填写原因方便平台尽快为您处理，只支持案源线索退单哦~</i>  <refund-standard :hour-val="hourShow" class="fr" /> </h5>
    <!--  表单   -->
    <form-lead-chargebacks  :hour-val="hourShow" @sure="handleFlushed" />
    <!--  退单申请记录  -->
    <div class="title-lx flex justify-between items-center">

      <p>退单申请记录</p>
      <xyExportButton
        file-name="退单申请记录"
        :export-option="exportOption"
        :afferent-column="column"
      />
    </div>
    <xy-page class="page-box" :show-search="false" :request="request" :column="column">
      <template slot="handleButton">
        <el-table-column
          fixed="right"
          width="90px"
          label="操作">
          <template slot-scope="{row}">
            <app-button @click="handleDetail(row)"  style-type="text">详情</app-button>
          </template>
        </el-table-column>
      </template>
    </xy-page>
    <!-- 机构详情   -->
    <app-popup
      title="退单详情"
      width="560px"
      :hide-footer="true"
      :visible.sync="visible">
      <div class="detail-box">
        <h4 class="tc">退单{{auditTextObj.statusTxt}}</h4>
        <div class="status-text tc">{{auditTextObj.txt}}</div>
        <el-steps class="step-box" :align-center="true" :space="200" :active="auditTextObj.status" finish-status="success">
          <el-step  title="申请退单"  />
          <el-step   title="平台审核"  />
          <el-step  v-if="Number(detailInfo.checkStatus) === 3 " class="error"  title="审核失败" icon="el-icon-error" />
          <el-step  v-else  title="审核通过" />
        </el-steps>
        <div class="detail-info">
            <h5>退单信息</h5>
            <ul>
              <li> <label>退单原因</label> <span> <i># {{detailInfo.feedbackLabel}}</i> </span> </li>
              <li> <label>补充说明</label> <span>{{detailInfo.feedbackReason}}</span></li>
              <li>
                <label>截图凭证</label>
                <span> <img v-for="(item,i) in detailInfo.imgs" :key="i" :src="item" /> </span>
              </li>
            </ul>
        </div>
      </div>
    </app-popup>
</div>
</template>

<script>
import FormLeadChargebacks from "views/leadManagement/clueSquare/leadChargebacks/component/form.vue";
import RefundStandard from "views/leadManagement/clueSquare/leadChargebacks/component/standard.vue";
import XyPage from "components/xy-page/index.vue";
import AppButton from "components/appButton/index.vue";
import { feedBackCaseSourceExport, feedBackCaseSourceLists } from "@/api/clues";
import AppPopup from "components/appPopup/index.vue";
import { commonConfigKey } from "@/api/common";
import xyExportButton from "components/xy-buttons/exportButton.vue";


export default {
  name: "LeadChargebacks",
  components: { xyExportButton, AppPopup, AppButton, XyPage, RefundStandard, FormLeadChargebacks },
  data(){
    return{
      request: {
        getListUrl: feedBackCaseSourceLists
      },
      exportOption: {
        url: feedBackCaseSourceExport,
      },
      column: [
        {
          label: "线索ID",
          prop: "businessId"
        }, {
          label: "线索描述",
          prop: "info"
        }, {
          label: "线索类型",
          prop: "typeLabel"
        }, {
          label: "抢单时间",
          prop: "qdTime"
        }, {
          label: "退单时间",
          prop: "tkTime"
        }, {
          label: "退单状态",
          filter: this.$global.adminConfig.feekStatusOrders,
          filterItemStyleRule: {
            3: { color: "#EB4738" },
          },
          prop: "checkStatus"
        }, {
          label: "审核失败原因",
          prop: "refuseReason"
        }, {
          label: "退单人",
          prop: "createUserName"
        },
      ],
      visible: false,
      detailInfo: {},
      hourShow: null
    };
  },
  computed: {
    auditTextObj(){
      let txt = "", statusTxt = "", status = 1;
      switch (Number(this.detailInfo.checkStatus)){
      case 1:
        txt = "请耐心等待，平台在3-5个工作日审核完毕";
        statusTxt = "审核中";
        status = 1;
        break;
      case 2:
        txt = "平台审核已通过，感谢您的反馈，权益已退回律所账户";
        statusTxt = "审核成功";
        status = 3;
        break;
      case 3:
        txt = "平台审核失败，" + this.detailInfo.refuseReason;
        statusTxt = "审核失败";
        status = 2;
        break;
      }
      return { txt, statusTxt, status };
    }
  },
  created() {
    commonConfigKey({
      paramName: "lc_tk_valid_time",
    }).then(data => {
      console.log(data, 888);
      if(!this.basics.isNull(data)) this.hourShow = Number(data) / (60 * 60);
    });

  },
  methods: {
    handleDetail(row){
      row.imgs =  row.attachFile.split(",");
      this.detailInfo = row;
      this.visible = true;
    },
    handleFlushed(){
      this.$store.dispatch("setIsTableRefresh");
    }
  }
};
</script>

<style lang="scss" scoped>
.wrap {
  background: #FFFFFF;
  padding: 17px 24px;

  h5 {
    font-size: 16px;
    margin-bottom: 18px;

    i {
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      color: #EB4738;
    }

  }
  .page-box{
    padding: 0;
  }
}
.detail-box{
  padding: 24px;
  h4{
    font-size: 16px;
    color: #333333;
    margin-bottom: 4px;
    font-weight: bold;
  }
  .status-text{
    font-weight: 400;
    font-size: 14px;
    color: #666666;
  }
  .step-box{
    margin: 32px auto 26px;
  }
::v-deep{
  .el-step__head.is-success{
    color: #3887F5;
    border-color: #3887F5;
    .el-step__icon{
      background: #0089FD;
      color: #FFFFFF;
    }

    .el-step__line{
      background-color:  #0089FD;
    }
  }
  .el-step__head.is-wait{
    color: #FFFFFF;
    border-color:  #CCCCCC;

    .el-step__icon{
      background: #CCCCCC;
    }
  }

  .el-step__title.is-success {
    color: #333333;
  }

  .el-step__head.is-finish{

    .el-step__icon{
      background: #0089FD;
      color: #FFFFFF;
    }
  }
  .el-step__head.is-process{
    color: #FFFFFF;
    border-color: #3887F5;

    .el-step__icon{
      background: #3887F5;
    }
  }
  .el-step__title{
    font-size: 14px !important;
    color: #333333;
    font-weight: normal;
  }

  .error{
    .el-step__icon{
      background: #FFFFFF !important;
      color: #3887F5 !important;
      width: 24px !important;
      border: 2px solid;
      border-radius: 50%;
    }
  }
}
  .detail-info{
    font-size: 14px;
    color: #666666;
    h5{
      margin-bottom: 16px;
      font-weight: 400;
    }

    ul{
      background: #F5F5F7;
      border-radius: 8px 8px 8px 8px;
      padding: 12px 16px;

      li{
        padding: 12px 0;
        //margin-bottom: 12px;
      }

      span{
        margin-left: 16px;
        display: inline-block;
        width: calc(100% - 100px);
        vertical-align: top;

        i{
          font-style: normal;
          color: #3887F5;
        }

        img{
          width: 74px;
          height: 74px;
          border-radius: 16px;
          margin-right: 8px;
        }
      }
    }
  }
}

</style>
