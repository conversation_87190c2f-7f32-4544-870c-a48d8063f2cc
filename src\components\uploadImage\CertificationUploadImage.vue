<template>
	<div class="h-[116px]">
		<el-upload
			ref="upload"
			:accept="accept"
			:action="uplodImages"
			:before-upload="beforeUpload"
			:limit="limit"
			:multiple="multiple"
			:on-error="handleError"
			:on-success="handleSuccess"
			:show-file-list="false"
		>
			<!-- 图片列表展示 -->
			<div v-if="fileList.length > 0">
				<div
					v-for="(item, index) in fileList"
					:key="index"
					class="w-[200px] h-[116px] relative group rounded-[2px] overflow-hidden box-border"
				>
					<el-image
						v-if="showList"
						:preview-src-list="[item]"
						:src="item"
						class="w-full h-full block"
						fit="fill"
					/>
					<div
						v-if="!disabled"
						class="!invisible w-full h-full absolute left-0 top-0 bg-[rgba(0,0,0,0.6)] flex items-center justify-center cursor-pointer group-hover:!visible"
					>
						<div class="text-[14px] text-[#FFFFFF]">点击重新上传</div>
					</div>
				</div>
			</div>
			<div
				v-else
				class="w-[200px] h-[116px] rounded-[2px] border-[1px] border-solid border-[#EEEEEE] box-border"
			>
				<slot />
			</div>
		</el-upload>
	</div>
</template>
<script>
import mixin from "./minxin";

export default {
  name: "CertificationUploadImage",
  mixins: [mixin],
  inject: ["elForm"],
  computed: {
    disabled() {
      return this.elForm?.disabled;
    },
  },
  methods: {
    /** 重新上传 */
    beforeUpload(file) {
      this.$emit("input", "");

      this.loading = this.$loading({
        lock: true,
        text: "Loading",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)",
      });

      return this.verification(file);
    },
  },
};
</script>
