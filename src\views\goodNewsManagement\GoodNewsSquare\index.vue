<template>
	<div class="bg-[#FFFFFF] box-border p-[24px]">
		<good-news-square-banner />
		<div>
			<div class="mt-[24px] flex items-center justify-end">
				<app-button type="primary" icon="!text-[16px] icon-wodexibao" @click="turnToMyNews">
					我的喜报
				</app-button>
			</div>
			<div class="mt-[48px]">
				<good-news-square-card
					v-for="item in list"
					:data="item"
					:key="item.id"
					@click="handleClick"
				/>
			</div>
			<good-news-square-detail v-model="show" :data="detailInfo" />
		</div>
	</div>
</template>

<script>
import AppButton from "components/appButton/index.vue";
import GoodNewsSquareCard from "views/goodNewsManagement/GoodNewsSquare/components/GoodNewsSquareCard.vue";
import GoodNewsSquareDetail from "views/goodNewsManagement/GoodNewsSquare/components/GoodNewsSquareDetail.vue";
import { turnToMyNews } from "utils/turnPage";
import { lcGoodNewsV2SquareDetailById, lcGoodNewsV2SquareList } from "@/api/goodNewsManagement";
import GoodNewsSquareBanner from "views/goodNewsManagement/GoodNewsSquare/components/GoodNewsSquareBanner.vue";

export default {
  name: "GoodNewsSquare",
  components: { GoodNewsSquareBanner, GoodNewsSquareDetail, GoodNewsSquareCard, AppButton },
  data() {
    return {
      show: false,
      list: [],
      detailInfo: {},
    };
  },
  created() {
    this.getList();
  },
  methods: {
    turnToMyNews,
    getList() {
      lcGoodNewsV2SquareList().then(data => {
        this.list = data;
      });
    },
    handleClick({ id, type }) {
      lcGoodNewsV2SquareDetailById({
        id,
        type,
      }).then(data => {
        this.detailInfo = data;
        this.show = true;
      });
    },
  },
};
</script>
