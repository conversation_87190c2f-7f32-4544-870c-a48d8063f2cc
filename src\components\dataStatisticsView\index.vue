<template>
	<div class="dataStatisticsView">
		<el-row :gutter="10" class="datas-lists">
			<el-col v-for="(item, i) in dataLits" :key="i" :md="4" :sm="8">
				<div class="lists">
					<!-- 标题 -->
					<div class="lists-tit flex">
						<span>{{ item.title || item.label }}</span>
						<!-- 释义 -->
						<el-tooltip
							v-if="item.desc || item.headTip"
							:content="item.desc || item.headTip"
							class="item"
							effect="dark"
							placement="top-start"
						>
							<i class="el-icon-question cur" />
						</el-tooltip>
					</div>
					<!-- 内容 -->
					<div :class="item.type === 'progress' && 'flex'">
						<!-- 进度条 -->
						<div v-if="item.type === 'progress'" class="lists-result-pro">
							<el-progress
								:percentage="handlePercent(totalValue[item.prop])"
								:width="70"
								color="#1ABC9C"
								type="circle"
							/>
						</div>
						<!-- 其他 -->
						<div v-else class="lists-result">
							<label v-if="!basics.isObj(item.prop)">{{
								totalValue[item.prop] | handleVal(item.type, item.unit)
							}}</label>
							<div v-else>
								<label>{{ totalValue[item.prop.val1] | handleVal(item.type, item.unit) }}</label>
								<label v-if="item.prop.val2">
									/ {{ totalValue[item.prop.val2] | handleVal(item.type, item.unit) }}</label
								>
							</div>
						</div>
						<!-- 与昨日对比 -->
						<div class="lists-text">
							与昨日对比
							<div v-if="!basics.isObj(item.compare)" class="inline">
								<span :class="handleStyle(totalValue[item.compare])">
									<label>{{ totalValue[item.compare] | handleVal(item.type, item.unit,true) }}</label>
								</span>
							</div>
							<div v-else class="inline">
								<span :class="handleStyle(totalValue[item.compare.val1])">
									<label>{{
										totalValue[item.compare.val1] | handleVal(item.type, item.unit,true)
									}}</label>
								</span>
								<i v-if="item.compare.val2"> / </i>
								<span
									v-if="item.compare.val2"
									:class="handleStyle(totalValue[item.compare.val2])"
								>
									<label>{{
										totalValue[item.compare.val2] | handleVal(item.type, item.unit,true)
									}}</label>
								</span>
							</div>
						</div>
					</div>
				</div>
			</el-col>
		</el-row>
	</div>
</template>

<script>
import { priceYuan, priceNumber } from "@/utils/toolMethods";

// 处理数字的，+和-
const handleAddSub = (val = "0") => {
  const num = Number(val) || 0;
  let str = "";
  if (num >= 0 && String(val).indexOf("+") < 0) {
    str = "+";
  } else if (num < 0 && String(val).indexOf("-") < 0) {
    str = "-";
  }

  return str + val;
};

export default {
  name: "DataStatisticsView",
  filters: {
    /**
	 * type, 'progress', 进度条  'money'，金额
	 */
    handleVal(row, type, unit = "min", isCompare = false) {
      let val = "";
      if (/^[+-].+%$/.test(row)) return row;
      switch (type) {
      case "progress":
        // eslint-disable-next-line no-case-declarations
        const _val = isCompare ? handleAddSub(row) : (row || "0");
        val = _val.indexOf("%") >= 0 ? _val : (_val || 0) + "%";
        break;
      case "money":
        if (isCompare) {
          val = "￥" + handleAddSub(priceNumber(row));
        } else {
          val = priceYuan(row);
        }
        break;
      case "time":
        val = `${row || 0}(${unit})`;
        break;
      default:
        if (/^[+-].*/.test(row)) {
          val = row;
        } else {
          val = isCompare ? handleAddSub(row) : row;
        }

        break;
      }

      return val || 0;
    }
  },
  props: {
    dataLits: {
      type: Array,
      default: () => {
        return [];
      },
      desc: "列表的标题展示内容"
    },
    totalValue: {
      type: Object,
      default: () => {
        return {};
      }
    }
  },
  methods: {
    // 处理颜色
    handleStyle(val) {
      return Number(val) >= 0 ? "green" : "red";
    },
    // 处理%号，去掉%
    handlePercent(val) {
      let _val = val;
      if (val && val.indexOf("%") >= 0) {
        _val = val.split("%")[0];
      }

      return Number(_val) || 0;
    }
  }
};
</script>

<style lang="scss" scoped>
.datas-lists {
	padding: 10px 15px;
	display: flex;
    flex-wrap: wrap;
}
.lists {
	padding: 10px 10px 20px 10px;
	border: 1px solid #eee;
	// height: 130px;
	box-sizing: border-box;
	height: calc(100% - 10px);
	// margin-bottom: 10px;
	.lists-result-pro {
		// margin: 5px 0;
		margin-top: 20px;
	}
	.lists-result {
		font-size: 20px;
		font-weight: bold;
		margin: 20px 0;
	}
	.lists-tit i {
		color: #ddd;
	}
	.lists-text {
		font-size: 12px;

		span {
			font-style: normal;
			&.red {
				color: $error-color;
			}
			&.green {
				color: $success-color;
			}
		}
	}
}
</style>
