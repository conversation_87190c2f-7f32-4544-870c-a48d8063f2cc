<template>
  <div class="mt-[16px] min-h-[438px] bg-[#FFFFFF] rounded-[2px]">
    <workbench-card-title>
      抢单排名
      <template #append>
        <div class="flex">
          <p
            :class="{'text-[#3887F5]':activeIndex===index}"
            @click="changeTab(index)"
            class="text-[14px] text-[#333333] !pl-[16px] cursor-pointer hover:text-[#3887F5]"
             v-for="(i,index) in tabList" :key="index">{{ i.name }}</p>
        </div>
      </template>
    </workbench-card-title>
    <div class="pt-[24px] pb-[12px] min-h-[348px] flex">
      <div class="pl-[44px] pr-[39px] flex-1 table-c" v-for="i in data" :key="i.key">
        <p class="font-bold text-[14px] text-[#333333] !pb-[16px]">{{i.label}}</p>
        <table style="width:100%">
          <tr>
            <td class="w-[60px]">排名</td>
            <td>律师</td>
            <td class="text-end  min-w-[56px] text-ellipsis">抢单次数</td>
          </tr>
          <tr class="h-[60px]" v-for="(c,index) in i.list" :key="i.key+index">
            <td>
              <img class="w-[24px] h-[24px]" :src="require(`@/views/workbenchManagement/workbench/imgs/Frame887@2x_${index+1}.png`)" alt="" />
            </td>
            <td>
              <div class="flex items-center">
<!--                <img class="w-[36px] h-[36px] border-[1px] border-solid border-[#EEEEEE]" src="" alt="" />-->
                <p class="!pl-[8px] text-[14px] text-[#333333]">{{c.lawyerName}}</p>
              </div>
            </td>
            <td class="text-end font-bold text-[16px] text-[#333333]">{{c.grabCount}}</td>
          </tr>
        </table>
      </div>
    </div>
  </div>
</template>

<script>
import WorkbenchCardTitle from "views/workbenchManagement/workbench/components/CardTitle.vue";
import moment from "moment";
import { stagingRankingForGrabbingOrders } from "@/api/workbenchManagement.js";
const format = "YYYYMMDD";
export default {
  name: "RankingOfOrderGrabbing",
  components: { WorkbenchCardTitle },
  data() {
    return {
      activeIndex: 2,
      tabList: [
        {
          name: "累计",
          query: {}
        }, {
          name: "昨日",
          query: {
            beginDate: moment().add(-1, "day").format(format),
            endDate: moment().add(-1, "day").format(format)
          }
        }, {
          name: "今日",
          query: {
            beginDate: moment().format(format)
          }
        }, {
          name: "近7日",
          query: {
            beginDate: moment().add(-7, "day").format(format)
          }
        }, {
          name: "近15日",
          query: {
            beginDate: moment().add(-15, "day").format(format)
          }
        }, {
          name: "近30日",
          query: {
            beginDate: moment().add(-30, "day").format(format)
          }
        }],
      data: [
        /* 实时案源抢单排名*/
        {
          list: [],
          label: "案源线索抢单排名",
          key: "realTimeCaseSourceGrabbingRanking"
        },
        /* 实时咨询抢单排名*/
        {
          list: [],
          label: "即时咨询抢单排名",
          key: "realTimeQaMessageGrabbingRanking"
        }
      ]
    };
  },
  computed: {
    /* 当前选择的时间*/
    activeQuery() {
      return this.tabList[this.activeIndex].query;
    }
  },
  mounted() {
    this.getData();
  },
  methods: {
    changeTab(index){
      this.activeIndex = index;
      this.getData();
    },
    getData(){
      stagingRankingForGrabbingOrders(this.activeQuery).then(res => {
        this.data.forEach(item => {
          if(res[item.key]) item.list = res[item.key];
        });
      });
    }
  },
};
</script>

<style scoped lang="scss">
.table-c{
  position: relative;
  &:after {
    position: absolute;
    right: 0;
    top: 0;
    content: " ";
    border-right: 1px dashed #DDDDDD;
    height: 100%;
  }
  &:last-of-type {
    &:after {
      display: none;
    }
  }
}
</style>
