<template>
  <el-drawer
    :visible.sync="show"
    :with-header="false"
    append-to-body
    direction="rtl"
    v-bind="$attrs"
    v-on="$listeners"
  >
    <div class="flex h-full flex-col">
      <div class="py-[17px] px-[24px] [box-shadow:inset_0px_-1px_0px_0px_#EEEEEE] font-bold flex justify-between">
        <p class="text-[16px] text-[#333333]">{{title}}</p>
        <i @click="close" class="iconfont icon-xianxingtubiao-13 text-[24px] w-[24px] h-[24px] block cursor-pointer" />
      </div>
      <div class="flex-1 overflow-y-auto">
        <slot />
      </div>
      <div>
        <slot name="footer" />
      </div>
    </div>
  </el-drawer>
</template>

<script>
export default {
  name: "AppDrawer",
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "新手指南"
    }
  },
  computed: {
    show: {
      get(){
        return this.visible;
      },
      set(val){
        this.$emit("update:visible", val);
      }
    }
  },
  methods: {
    close(){
      this.show = false;
    }
  }
};
</script>



<style scoped lang="scss">

</style>
