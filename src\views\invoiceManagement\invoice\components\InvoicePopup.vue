<template>
	<app-popup
		:visible.sync="show"
		title="开发票"
		:show-cancel="true"
		width="560px"
		@confirm="submitInvoice"
	>
		<div class="p-[24px] form-container">
			<el-form ref="invoiceForm" :model="invoiceForm" :rules="rules" label-width="160px">
				<!-- 发票类型 -->
				<el-form-item label="发票类型" prop="valueAddedTaxInvoiceType">
					<el-radio-group v-model="invoiceForm.valueAddedTaxInvoiceType">
						<el-radio
							:label="item.value"
							v-for="item in valueAddedTaxInvoiceTypeList"
							:key="item.value"
							>{{ item.label }}</el-radio
						>
					</el-radio-group>
				</el-form-item>

				<!-- 抬头类型 -->
				<el-form-item label="抬头类型" prop="invoiceHeader">
					<el-radio-group v-model="invoiceForm.invoiceHeader">
						<el-radio :label="item.value" v-for="item in invoiceHeaderType" :key="item.value">{{
							item.label
						}}</el-radio>
					</el-radio-group>
				</el-form-item>

				<!-- 企业单位表单 -->
				<template v-if="Number(invoiceForm.invoiceHeader) === 1">
					<!-- 公司名称 -->
					<el-form-item label="公司名称" prop="companyName">
						<el-input v-model="invoiceForm.companyName" placeholder="请输入" />
					</el-form-item>

					<!-- 公司税号 -->
					<el-form-item label="公司税号" prop="companyTaxId">
						<el-input v-model="invoiceForm.companyTaxId" placeholder="请输入" />
					</el-form-item>

					<!-- 上传证明函 -->
					<el-form-item label="上传证明函" prop="proofLetter">
						<upload-image
							v-model="invoiceForm.proofLetter"
							accept="image/jpeg,image/png"
							:form-max-size-m="5"
						/>
						<div class="text-[12px] text-[#999999] -mt-[7px]">温馨提示：证明函需加盖律所公章</div>
					</el-form-item>
				</template>

				<!-- 个人/非企业单位表单 -->
				<template v-else>
					<!-- 个体名称 -->
					<el-form-item label="个体名称" prop="companyName">
						<el-input v-model="invoiceForm.companyName" placeholder="请输入" />
					</el-form-item>
				</template>

				<!-- 备注 -->
				<el-form-item label="备注" prop="institutionRemark">
					<el-input
						v-model="invoiceForm.institutionRemark"
						type="textarea"
						:autosize="{ minRows: 3, maxRows: 5 }"
						maxlength="80"
						show-word-limit
						placeholder="如企业有特殊要求，可填写备注，该内容会打印在发票上"
					/>
				</el-form-item>

				<!-- 总金额 -->
				<div class="mt-[32px] flex items-center justify-end">
					<div class="text-[14px] text-[#333333]">
						总金额：<span class="text-[16px] font-bold text-[#333333]"
							>¥{{ priceNumber(totalAmount) }}</span
						>
					</div>
				</div>
				<div class="text-[12px] text-[#F78C3E] text-right mt-[8px]">
					此金额基于用户勾选需要开具发票的金额合计
				</div>
			</el-form>
		</div>
	</app-popup>
</template>

<script>
import { dataDetailList } from "@/api/common";
import { institutionInvoiceInvoice } from "@/api/invoice";
import AppPopup from "@/components/appPopup/index.vue";
import UploadImage from "@/components/uploadImage/index.vue";
import { priceNumber } from "@/utils/toolMethods";

export default {
  name: "InvoicePopup",
  components: {
    AppPopup,
    UploadImage,
  },
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    bizIds: {
      type: Array,
      default: () => [],
    },
    totalAmount: {
      type: [Number, String],
      default: 0,
    },
    invoiceType: {
      type: [Number, String],
      default: 1,
    },
  },
  data() {
    return {
      invoiceHeaderType: [],
      valueAddedTaxInvoiceTypeList: [],
      invoiceForm: {
        /** 增值税发票类型 */
        valueAddedTaxInvoiceType: 1,
        /** 发票抬头[1.企业单位，2.个人/非企业单位] */
        invoiceHeader: 1,
        /** 开票金额（分） */
        amount: 0,
        /** 公司名称 */
        companyName: "",
        /** 公司税号 */
        companyTaxId: "",
        /** 机构备注内容 */
        institutionRemark: "",
        /** 是否全选（0.取消全选，1.全选） */
        selectAll: 0,
        /** 证明函 */
        proofLetter: "",
      },
      rules: {
        valueAddedTaxInvoiceType: [
          { required: true, message: "请选择发票类型", trigger: "change" },
        ],
        invoiceHeader: [{ required: true, message: "请选择抬头类型", trigger: "change" }],
        companyName: [{ required: true, message: "请输入名称", trigger: "blur" }],
        companyTaxId: [{ required: true, message: "请输入公司税号", trigger: "blur" }],
      },
    };
  },
  computed: {
    show: {
      get() {
        return this.visible;
      },
      set(val) {
        this.$emit("update:visible", val);
      },
    },
  },
  created() {
    this.getInvoiceTypeList();
  },
  methods: {
    getInvoiceTypeList() {
      dataDetailList({ groupCode: "INSTITUTION_INVOICE_HEADER" }).then(data => {
        this.invoiceHeaderType = data;
        this.invoiceForm.invoiceHeader = data[0].value;
      });

      dataDetailList({ groupCode: "VALUE_ADDED_TAX_INVOICE_TYPE" }).then(data => {
        this.valueAddedTaxInvoiceTypeList = data;
        this.invoiceForm.valueAddedTaxInvoiceType = data[0].value;
      });
    },
    priceNumber,
    // 提交发票信息
    submitInvoice() {
      this.$refs.invoiceForm.validate(valid => {
        if (!valid) {
          return false;
        }

        institutionInvoiceInvoice({
          ...this.invoiceForm,
          amount: this.totalAmount,
          bizIds: this.bizIds,
          invoiceType: this.invoiceType,
        }).then(data => {
          console.log(data);
          this.$message.success("发票申请提交成功");
          this.show = false;
          this.resetForm();
          this.$emit("success");
        });
      });
    },
    // 重置表单
    resetForm() {
      this.$refs.invoiceForm.resetFields();
    },
  },
};
</script>

<style lang="scss" scoped>
.form-container {
	::v-deep .el-radio {
		color: #333333;
		font-weight: 400;
	}
}
</style>
