<template>
  <div :id="id" :class="className" :style="{height:height,width:width}" />

</template>

<script>
import echarts from "echarts";
import resize from "@/minixs/resize";

export default {
  name: "LineChart",
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: "chart"
    },
    id: {
      type: String,
      default: "chart"
    },
    width: {
      type: String,
      default: "100%"
    },
    height: {
      type: String,
      default: "500px"
    },
    riseTrendData: {
      type: Array,
      default: () => []
    },
    xAxisData: {
      type: Array,
      default: () => []
    },
    legendData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      chart: null,
      seriesData: [],
      color: ["#58A3F7", "#FFC542", "#FF7474"]
      // borderColor: ['rgb(#58A3F7,0.2)', 'rgb(#FFC542,0.2)', 'rgb(#FF7474,0.2)']
    };
  },
  watch: {
    riseTrendData: {

      handler(val) {
        if (this.chart) this.chart.clear();
        this.initChart();
      },
      deep: true
    }
  },
  mounted() {
    this.initChart();
  },
  beforeDestroy() {
    if (!this.chart) {
      return;
    }
    this.chart.dispose();
    this.chart = null;
  },
  methods: {
    initChart() {
      this.seriesData = [];
      this.chart = echarts.init(document.getElementById(this.id));
      for (let i = 0; i < this.legendData.length; i++) {
        this.seriesData.push({
          name: this.legendData[i],
          type: "line",
          smooth: false,
          symbol: "circle",
          symbolSize: 6,
          showSymbol: true,
          lineStyle: {
            normal: {
              width: 2
            }
          },
          itemStyle: {
            normal: {
              color: this.color[i],
              // borderColor: this.borderColor[i]
              // borderWidth: 10,
              lineStyle: {
                width: 2,
                type: "solid" // 'dotted'虚线 'solid'实线
              }
            }
          },
          data: this.riseTrendData[i]
        });
      }

      this.chart.setOption({
        backgroundColor: "#fff",
        tooltip: {
          trigger: "axis",
          axisPointer: {
            lineStyle: {
              color: "#333"
            }
          }
        },
        legend: {
          bottom: 0,
          icon: "rect",
          itemWidth: 20,
          itemHeight: 5,
          itemGap: 13,
          data: this.legendData,
          textStyle: {
            fontSize: 12,
            color: "#999"
          }
        },
        grid: {
          top: 40,
          left: "3%",
          right: "3%",
          bottom: "90px",
          containLabel: true
        },
        dataZoom: [{
          show: true,
          height: 25,
          xAxisIndex: [
            0
          ],
          bottom: 35,
          start: 10,
          end: 80,
          handleIcon: "path://M306.1,413c0,2.2-1.8,4-4,4h-59.8c-2.2,0-4-1.8-4-4V200.8c0-2.2,1.8-4,4-4h59.8c2.2,0,4,1.8,4,4V413z",
          handleSize: "110%",
          handleStyle: {
            color: "#d3dee5"
          },
          textStyle: {
            color: "#fff"
          }
          // borderColor: '#90979c'
        }, {
          type: "inside",
          show: true,
          height: 15,
          start: 1,
          end: 35
        }],
        xAxis: [{
          type: "category",
          boundaryGap: false,
          axisLine: {
            lineStyle: {
              color: "#57617B"
            }
          },
          // axisLabel: { interval: 0 },
          data: this.xAxisData
        }],

        yAxis: [{
          type: "value",
          // name: '(%)',
          axisTick: {
            show: false
          },
          axisLine: {
            lineStyle: {
              color: "#57617B"
            }
          },
          axisLabel: {
            margin: 10,
            textStyle: {
              fontSize: 14
            }
          },
          splitLine: {
            lineStyle: {
              color: "#eee"
            }
          }
        }],
        series: this.seriesData
      }, true);
    }
  }
};
</script>
