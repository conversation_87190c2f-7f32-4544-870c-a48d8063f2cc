<template>
  <!--  当事人所在地弹窗  -->
  <!--  <app-popup title="当事人所在地" class="popup-wrap"  width="560px" :show-cancel="true" @cancel="close" @close="close" cancel-text="取消" @confirm="locationSure" :visible="dialogVisible">-->
  <div>
    <div class="flex px-[24px] pb-[12px] items-center text-[14px] text-[#333333]">
      选择当事人所在地
      <span class="text-[12px] text-[#999999] pl-[8px]">最多可选10项</span>
    </div>
    <div class="location-lists-box flex ">
      <!--    省    -->
      <ul class="location-province">
        <li v-for="(item,i) in provinceList" :class="[(provinceCode === item.code) && 'checkedStyle']" :key="i"
            @click="handleProvince(item)">
          <p v-html="handleProvinceNum(item.code)" />
          <span>{{ item.name }}</span>
        </li>
      </ul>
      <!--   市     -->
      <div class="location-city">
        <el-checkbox-group v-model="cityVal" @change="handleCity">
          <el-checkbox class="city" v-for="(city,k) in cityList" :label="city.code" :key="k">{{ city.name }}
          </el-checkbox>
        </el-checkbox-group>
      </div>
    </div>
  </div>
  <!--  </app-popup>-->
</template>

<script>
import { areaGetArea } from "@/api/common";
import { caseSourceCluesPushDetailUpdate } from "@/api/clues";

export default {
  name: "CaseLocation",
  props: {
    dialogVisible: {
      type: Boolean,
      default: false
    },
    detail: {
      type: Object,
      default: () => {
        return {};
      }
    },
    request: {
      type: Function,
      default: () => Promise.resolve()
    }
  },
  data() {
    return {
      /** 当事人的所在地弹窗展示 **/
      isShowLocation: false,
      /** 省列表 **/
      provinceList: [],
      provinceCode: "",
      /** 市列表 **/
      cityList: [],
      cityVal: [],
      provinceCityCode: [],
      locationText: [],
    };
  },
  created() {
    this.getProvince();
    this.handleDetails();
  },
  methods: {
    /** 获取详情的地区**/
    handleDetails() {
      // 所在地
      if (this.detail.cityV2Lists) {

        let newList = [];
        const city = [], text = [];
        this.detail.cityV2Lists.forEach(function (data) {
          city.push(data.regionCode);
          text.push(data.regionName);
          for (let i = 0; i < newList.length; i++) {
            if (newList[i].provinceCode === data.provinceCode) {
              newList[i].cityCode.push(data);
              return;
            }
          }
          newList.push({
            provinceCode: data.provinceCode,
            cityCode: [data]
          });
        });
        this.cityVal = city;
        this.provinceCityCode = newList;
      }
    },
    /** 获取省 **/
    getProvince() {
      areaGetArea({ type: 1 }).then(r => {
        this.provinceList = r || [];
        if (r.length > 0) {
          this.getCity(r[0].code);
          this.provinceCode = r[0].code;
        }
      });
    },
    /** 获取市 **/
    getCity(code) {
      areaGetArea({ type: 2, code }).then(r => {
        this.cityList = r || [];
      });
    },
    /** 点击省，获取市 **/
    handleProvince(item) {
      if (this.provinceCode === item.code) return;
      this.provinceCode = item.code;
      this.getCity(item.code);
    },
    handleProvinceNum(val) {
      const list = this.provinceCityCode.find(r => r.provinceCode === val);
      console.log(list);
      return list && list?.cityCode.length > 0 ? `<i class="province-num">${list?.cityCode.length}</i>` : "";
    },

    /* 验证 true验证通过*/
    getVerify() {
      if (this.cityVal.length < 1) {
        this.$message.warning("至少选择一个！");
        return false;
      }
      return true;
    },
    /** 选中市 **/
    handleCity(val) {
      if (this.cityVal.length > 10) {
        this.cityVal.pop();
        return this.$message.warning("最多可选10项");
      }
      // 选中最新的值
      const singnalVal = val[val.length - 1];
      const singnalValName = (this.cityList.find(l => l.code === singnalVal))?.name;

      let isNewProvince = true;
      this.provinceCityCode.forEach((r, k) => {
        console.log(r.provinceCode, "=", this.provinceCode, r);
        if (r.provinceCode === this.provinceCode) {
          isNewProvince = false;
          // 删除取消选中的市
          let isDel = false;
          r.cityCode.forEach((p, i) => {
            if (!val.includes(p.regionCode)) {
              isDel = true;
              r.cityCode.splice(i, 1);
            }
          });
          if (!this.basics.isObj(r.cityCode.find(c => c.regionCode === singnalVal)) && !isDel) {
            // console.log('我在push',singnalVal)
            r.cityCode.push({
              regionCode: singnalVal,
              regionName: singnalValName
            });
          }
          console.log(r.cityCode, 999);
          if (r.cityCode.length < 1) this.provinceCityCode = this.provinceCityCode.filter(a => a.provinceCode !== this.provinceCode);
        }
      });
      if (isNewProvince) {
        console.log("新增数据");
        this.provinceCityCode.push({
          provinceCode: this.provinceCode,
          cityCode: [{
            regionCode: singnalVal,
            regionName: singnalValName
          }],
        });
      }
      console.log(this.provinceCityCode);
    },
    /** 保存所在地 **/
    caseSure() {
      if (!this.getVerify()) return;
      let texts = [], locations = [];
      this.provinceCityCode.forEach(r => {
        r.cityCode.forEach(s => {
          texts.push(s.regionName);
          s.provinceCode = r.provinceCode;
        });
        locations = [...locations, ...r.cityCode];
      });
      const data = {
        // ...this.detail,
        // type: 1,
        cityV2Lists: locations,
      };
      if (this.detailId) data.id = this.detailId;
      return data;
      // caseSourceCluesPushDetailUpdate(data).then(() => {
      //   this.$message.success("操作成功");
      //   this.$emit("handleLocationText", texts);
      //   this.close();
      // });
    },
    close() {
      this.$emit("update:dialogVisible", false);
    }
  }
};
</script>

<style lang="scss" scoped>
::v-deep .province-num {
  font-style: normal;
  position: absolute;
  left: 16px;
  top: 13px;
  bottom: 0;
  width: 16px;
  height: 16px;
  background: #3887F5;
  border-radius: 8px 8px 8px 8px;
  display: block;
  font-size: 12px;
  line-height: 15px;
  text-align: center;
  color: #FFFFFF;
}

.popup-wrap {
  overflow: hidden !important;
}

.case-tit-desc {
  margin-left: 24px;
  margin-top: 16px;
  margin-bottom: 16px;
  color: #666666;
}

.location-lists-box {
  border: 1px solid #eee;
  border-radius: 12px;
  margin: 0 24px;

  .location-province {
    border-right: 1px solid #EEEEEE;
    width: 228px;
    max-height: 320px;
    overflow-y: auto;
    overflow-x: hidden;

    li {
      width: 228px;
      height: 40px;
      line-height: 40px;
      font-size: 14px;
      color: #333333;
      padding-left: 40px;
      box-sizing: border-box;
      position: relative;
      cursor: pointer;

      &.checkedStyle {
        background: #EDF3F7;
      }

      &:hover {
        background: #EDF3F7;
      }
    }
  }

  .location-city {
    width: 284px;
    max-height: 320px;
    overflow-y: auto;
    overflow-x: hidden;

    .city {
      display: block;
      width: 284px;
      height: 40px;
      padding-left: 16px;
      box-sizing: border-box;
      line-height: 40px;
    }
  }
}
</style>
