<template>
  <div>
    <RootCheck v-for="i in btnsData" :key="data.id+'_'+i" :data="data"  :button-data="buttonRootEnum[i]" />
  </div>
</template>

<script>
import RootCheck from "views/systemAdministration/permissionsManagement/components/RootCheck.vue";

export default {
  name: "RootChecks",
  components: {RootCheck},
  props: {
    /* 按钮权限枚举*/
    buttonRootEnum: {
      type: Object,
      default: () => {
        return {};
      },
    },
    /* 当前tree的数据*/
    data: {
      type: Object,
      default: () => {
        return {};
      },
    }
  },
  computed: {
    /*当前按钮的有哪些权限*/
    btnsData(){
      return this.basics.isNull(this.data.functions)?[]:this.data.functions.split(',')
    }
  }
};
</script>

<style scoped lang="scss">

</style>
