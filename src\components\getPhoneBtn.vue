<template>
  <div class="get-phone-btn flex-align-center" :key="params.caseSourceServerV2Id">
    <span class="text">{{phone || value}}</span>
    <span v-show="!isDisabled" v-bind="$attrs" class="real-btn cursor" :class="{disabled: isDisabled}"  @click="getPhone">
      <slot>查看号码</slot>
    </span>
  </div>
</template>

<script>

import { getNewServerCall } from "@/api/im";

export default {
  name: "GetPhoneBtn",
  props: {
    value: {
      type: String,
      default: ""
    },
    params: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      phone: ""
    };
  },
  computed: {
    isDisabled() {
      return this.phone && !/\*/.test(this.phone) || !/\*/.test(this.value);
    }
  },
  watch: {
    value() {
      this.phone = "";
    }
  },
  methods: {
    getPhone() {
      if (this.isDisabled) return false;
      getNewServerCall(this.params).then((res) => {
        this.phone = res.realPhone;
      });
    }
  }
};
</script>

<style scoped lang="scss">
.get-phone-btn{
  display: inline-flex;
  .text{
    font-size: 14px;
  }
  .real-btn{
    font-size: 12px;
    margin-left: 8px;

    &:hover{
      color: #3887F5;
    }
    &.disabled{
      color: #999;
      cursor: not-allowed;
    }
  }

}
</style>
