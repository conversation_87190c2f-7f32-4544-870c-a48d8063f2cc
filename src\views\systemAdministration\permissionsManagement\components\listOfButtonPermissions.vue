<template>
  <div>
    <permissions-header>
      功能
    </permissions-header>
    <div class="pl-[10px] list-container pr-[16px]">
      <el-tree
        :data="menuList"
        default-expand-all
        node-key="id"
        ref="tree"
        highlight-current
        :props="treeProps">
        <template #default="{ node, data }">
          <div class="custom-tree-node w-full flex justify-between">
            <span>{{ node.label }}</span>
            <div @click.stop v-if="data.functions">
              <RootChecks :button-root-enum="buttonRootEnum" :data="data"></RootChecks>
            </div>
          </div>
        </template>
      </el-tree>
    </div>
  </div>
</template>

<script>
import PermissionsHeader from "views/systemAdministration/permissionsManagement/components/PermissionsHeader.vue";
import { sysModuleFunc } from "@/api/system";
import RootChecks from "views/systemAdministration/permissionsManagement/components/RootChecks.vue";

export default {
  name: "ListOfButtonPermissions",
  components: {RootChecks, PermissionsHeader },
  props: {
    /* 菜单*/
    menuList: {
      type: Array,
      default: () => []
    },
    /* tree的参数*/
    treeProps: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      /* 按钮权限枚举*/
      buttonRootEnum: {}
    };
  },
  mounted() {
    sysModuleFunc().then(data => {
      (data || []).forEach(item => {
        this.buttonRootEnum[item.code] = item;
      });
    });
  },
  methods: {
  }
};
</script>

<style scoped lang="scss">
.list-container{

  overflow-y: auto;
  height: calc(100vh - 191px);
}
</style>
