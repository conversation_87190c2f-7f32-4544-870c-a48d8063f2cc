<template>
  <div @click="handleClick" class="flex items-center justify-center flex-col cursor-pointer" :class="activeStyle">
    <i class="w-[20px] h-[20px] text-[20px] text-[#FFFFFF] block iconfont" :class="[data.meta.moduleIcon]" alt="" />
    <p class="text-[12px] text-[#FFFFFF] mt-[4px]">{{ data.meta&&data.meta.title }}</p>
  </div>
</template>

<script>
const baseStyle = "w-[64px] h-[57px] rounded-tl-[4px] rounded-br-[4px] rounded-tr-[4px] rounded-bl-[4px] opacity-100";

const activeStyle = `${baseStyle} bg-[#3887F5]`;

export default {
  name: "FunctionMenuItem",
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      active: false
    };
  },
  computed: {
    activeStyle() {
      return this.active ? activeStyle : `${baseStyle} hover:bg-[rgba(255,255,255,0.1)]`;
    }
  },
  watch: {
    /** 监听路由变化 */
    $route: {
      handler() {
        /** 递归判断是否this.$route.name === data.name */
        const findName = (data) => {
          if (data.name === this.$route.name) {
            // ! 这里主要处理通过 点击 屏幕中的菜单，导航栏一二级能正常显示的问题
            this.$emit("click", this.data);
            return true;
          }

          if (data?.children) {
            // 对所有的children进行递归
            return data.children.some(item => findName(item));
          }

          return false;
        };

        this.active = findName(this.data);
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 递归找到data下最后一层children的第一个name
    findLastChildrenName(data){
      if (data?.children) {
        return this.findLastChildrenName(data.children[0]);
      } else {
        return data?.name;
      }
    },
    handleClick() {

      console.log(this.findLastChildrenName(this.data));

      this.$router.push({
        name: this.findLastChildrenName(this.data)
      });

      this.$emit("click", this.data);
    }
  }
};
</script>

<style scoped lang="scss">

</style>
