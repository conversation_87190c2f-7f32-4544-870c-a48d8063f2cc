
import { columnList } from "@/api/contentManage";

// 处理栏目数组
function handlerData(tree = []) {
  const arr = [];
  if (!!tree && tree.length !== 0) {
    tree.forEach(item => {
      let obj = {};
      obj = item;
      obj.label = obj.columnName;
      obj.value = obj.id;
      if (item.children && item.children.length > 0) {
        obj.children = handlerData(item.children);
      } // 递归调用
      arr.push(obj);
    });
  }
  return arr;
}

async function getCoulumnData() {
  try {
    let data = [];
    await columnList().then(res => { data = res; });
    return handlerData(data);
  } catch {
    //  return re
  }
}
export default getCoulumnData;

