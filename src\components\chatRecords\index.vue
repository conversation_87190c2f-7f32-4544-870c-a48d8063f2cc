<template>
	<div class="chat-ercords">
		<!-- 弹窗 -->
		<xyDialog
			:dialog-visible.sync="dialogVisible"
			@close="cancel"
			:options="option"
			:show-close="false"
			:show-footer="false"
		>
    <!-- 头部 -->
    <model-header :title="`总计${totalNumber}条会话消息`" class="headers-chart">
      <p  v-if="showPart.lockTime" class="time-box">{{rigtTittle}}
        <span class="time" v-if="lockTime">{{lockTime }}</span>
        <span v-else>-</span>
      </p>
    </model-header>
    <!--  日期段选择  -->
    <div class="box" v-if="showPart.time">
      <el-date-picker
        v-model="dateTime"
        type="datetimerange"
        @change="changeDateRange"
        value-format="yyyy-MM-dd HH:mm:ss"
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期" />
    </div>

    <div class="block infinite-chat-list" v-infinite-scroll="load" style="overflow:auto">
        <el-timeline>
          <el-timeline-item  v-for="(item,i) in dataValue" :key="i" :timestamp="item.createTime || '' " placement="top">
            <el-card shadow="hover">
              <div class="tag-absolute" v-if="item.msgType == 6">
                <el-tag>系统自动回复</el-tag>
              </div>
              <h4>{{item.nickName}}</h4>

              <!-- 图片 -->
              <el-image
                v-if="item.msgType == 2"
                style="width: 50px; height: 50px"
                :src="item.content"
                :preview-src-list="[item.content]" />
              <!-- 文件 -->
              <a v-else-if="item.msgType == 5" :href="item.url" target="_blank"  rel="noopener noreferrer" class="inline-block file" style="width: 50px; height: 50px">
                  <i  class="el-icon-document" />
              </a>
              <!-- 文本 -->
              <div v-else>
                <!-- 正常文本 -->
                 <span v-if="!basics.isObj(item.content)" v-html="item.content" />

                <!-- 自定义消息 -->
                <div v-else>
                  <!-- {{item}} -->
                  <!-- 服务 -->
                  <div v-if="item.content.style === 'server_one_to_one'" class="service-box">
                    <el-image
                    style="width: 163px; height: 92px"
                    :src="item.content.icon"
                    :preview-src-list="[item.content.icon]" />
                    <p>服务名称：{{item.content.title}} /  规格：{{item.content.content}}</p>
                  </div>
                  <!-- 查看电话 -->
                  <div v-else-if="item.content.style === 'contact_info_user' || item.content.style === 'contact_info_lawyer'" class="service-box">
                    <p>{{item.content.title}} {{item.content.phone}}</p>
                  </div>
                  <!-- 邀请评价 -->
                  <div v-else-if="item.content.style === 'invite_comment'" class="service-box">
                    <p class="text">{{item.content.title}}</p>
                    <p class="secondary-color">{{item.content.content}}</p>
                  </div>
                  <!-- 评价 -->
                  <div v-else-if="item.content.style === 'lawyer_rate'" class="service-box">
                    <p class="text">{{item.content.title}}</p>
                    <p class="secondary-color">得分：{{item.content.totalScore}}</p>
                    <p class="secondary-color" v-show="item.content.content">内容：{{item.content.content}}</p>
                    <p class="secondary-color" v-show="item.content.labels.length >0">标签：<el-tag v-for="i in item.content.labels" :key="i" class="m-r-15">{{i}}</el-tag></p>
                  </div>
                  <!-- 退款 -->
                  <div v-else-if="item.content.style === 'refund_tip'" class="service-box">
                    <p>您已发起退款, 等待审核</p>
                  </div>
                  <!--  -->
                  <div v-else class="service-box">
                    <p  v-html="item.content.title" />
                  </div>
                </div>

            </div>
              <!-- 服务 -->
              <!-- <div class="service-box" v-else>
                <el-image
                style="width: 163px; height: 92px"
                :src="item.url"
                :preview-src-list="[item.url]"></el-image>
                <p class="text">{{item.content}}</p>
              </div> -->
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>
		</xyDialog>
	</div>
</template>

<script>
import xyDialog from "@/components/xy-dialog/index.vue";
import ModelHeader from "@/components/modelHead/index.vue";

export default {
  name: "AddContent",
  components: {
    xyDialog,
    ModelHeader
  },
  props: {
    chatParams: {
      type: Object,
      default: () => {}
    },
    dialogVisible: {
      type: Boolean,
      default: false
    },
    showPart: {
      type: Object,
      default: () => {
        return {
          time: false,
          lockTime: false
        };
      },
      desc: "需要展示的某个部分"
    },

    request: {
      type: Object,
      default: () => {
        return {
          getListUrl: () => Promise.resolve()
        };
      },
      desc: "请求的接口"
    },
    option: {
      type: Object,
      default: () => {
        "会话列表";
      }
    },
    rigtTittle: {
      type: String,
      default: "锁定失效时间："
    }
  },
  data() {
    return {
      dateTime: null,
      dataValue: [], // 聊天数据
      dateChange: false, // 是否是时间改变
      totalPage: 1, // 当前分页
      currentPage: 1, // 当前分页
      totalNumber: 0, // 聊天条数总量
      lockTime: "--" // 锁定时间
    };
  },
  watch: {
    dateTime() {
      this.currentPage = 1;
      this.dataValue = [];
      this.totalNumber = 0;
      this.getList(true);
      this.dateChange = true;
    }
  },
  created() {
    this.getList();
  },
  methods: {
    getList(boo) {
      const params = {
        ...this.chatParams,
        pageSize: 100,
        currentPage: this.currentPage,
        chatStartTime: this.dateTime ? this.dateTime[0] : null,
        chatEndTime: this.dateTime ? this.dateTime[1] : null
      };
      // if (this.currentPage > this.totalPage && this.currentPage !== 2) return this.$message('暂时没有更多数据了！')
      this.request.getListUrl(params).then(res => {
        this.dateChange = false;
        // 时间选择
        if (boo && !res.msgHistory) this.$message("当前选定范围无消息记录");
        this.dataValue = [...this.dataValue, ...(res.msgHistory || [])];
        // console.log(res)
        this.dataValue.forEach(ele => {
          // 判断是否是JSPON对象
          if (this.basics.isJSON(ele.content)) {
            const obj = JSON.parse(ele.content);
            ele.content = this.obJuge(obj);
            ele.url = obj.url;
            // console.log(ele, '记录')
          }
        });

        this.currentPage++;
        this.totalNumber = res.count; // 总聊天数量
        // this.totalPage = !res.totalPage ? 1 : res.totalPage
        this.lockTime = res.lockTime || "";
      });
    },
    // 对象的循环判断
    obJuge(val) {
      const str = {};
      const _this = this;

      function LoopFun(val) {
        if (!_this.basics.isObjNull(val)) {
          for (const key in val) {
            if (_this.basics.isObj(val[key]) && !_this.basics.isObjNull(val[key])) {
              LoopFun(val[key]);
            } else {
              str[key] = val[key];
            }
          }
        }
      }
      LoopFun(val);
      return str;
    },
    load() {
      // !this.dateChange && this.getList()
    },
    // 日期段筛选
    changeDateRange() {
      console.log("dateTime:", this.dateTime);
    },
    cancel() {
      this.$emit("update:dialog-visible", false);
    }
  }
};
</script>

<style lang="scss" scoped>
.box{
  display: flex;
  margin-bottom: 20px;
  .total{
    color: $blue-color;
    font-size: 16px;
    margin-left: 30px;
    line-height: 30px;
  }
}
.el-card{
  position: relative;
  .tag-absolute{
    position: absolute;
    right: 0;
    top: 0;
  }
}
.headers-chart{
  margin-bottom: 15px;
}
.time-box{
  font-weight: bold;
  .time{
    color: #e30000;
  }
}

.infinite-chat-list{
  height: 600px;
  h4{
    margin-bottom: 5px;
  }
  .service-box{
    background: #fff;
    border-radius: 4px;
    padding: 3px 0;
    /deep/ .el-image{
      img{
        border-radius: 4px;
      }
    }
    .text{
      word-wrap: break-word;
      word-break: break-all;
      color: #46474b;
      font-weight: 600;
      font-size: 14px;
    }
  }
  .file{
    font-size: 50px;
    .el-icon-document{
      font-size: 50px;
    }
  }
}
</style>
