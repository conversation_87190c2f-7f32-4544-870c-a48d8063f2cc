export default [
  {
    path: "/goodNewsManagement",
    name: "goodNewsManagement",
    meta: {
      title: "喜报管理"
    },
    component: () => import("@/views/layout/layout.vue"),
    children: [
      {
        path: "/uploadGoodNews",
        name: "uploadGoodNews",
        meta: {
          title: "上传喜报",
          cachedView:true,
        },
        component: () => import("views/goodNewsManagement/uploadGoodNews/index.vue")
      },
      {
        path: "/goodNewsSquare",
        name: "goodNewsSquare",
        meta: {
          title: "喜报广场"
        },
        component: () => import("views/goodNewsManagement/GoodNewsSquare/index.vue")
      },
      {
        path: "/myGoodNews",
        name: "myGoodN<PERSON>",
        meta: {
          title: "我的喜报"
        },
        component: () => import("views/goodNewsManagement/MyGoodNews/index.vue")
      }
    ]
  },

];
