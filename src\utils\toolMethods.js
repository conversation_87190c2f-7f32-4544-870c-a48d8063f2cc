import global from "./constant";
import basics from "utils/basicsMethods";
import dayjs from "dayjs";
/**
 *谷歌系统通知
 *
 */
import logo from "@/assets/images/logo.png";
import moment from "moment";
import { getTriggerTime } from "utils/storage";


/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result;

  const later = function() {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp;

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last);
    } else {
      timeout = null;
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args);
        if (!timeout) context = args = null;
      }
    }
  };

  return function(...args) {
    context = this;
    timestamp = +new Date();
    const callNow = immediate && !timeout;
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait);
    if (callNow) {
      result = func.apply(context, args);
      context = args = null;
    }

    return result;
  };
}

/**
 * 递归多维数组
 * @param arr(Array) 要遍历的多维数组
 * @param children(String) 递归字段
 * @returns Array
 */
export const recursionArray = function(arr, children) {
  const newArr = [];

  function recursion(arr) {
    for (const item of arr) {
      newArr.push(item);
      if (item[children]) {
        if (item[children].length > 0) {
          recursion(item[children]);
        }
      }
    }
  }
  recursion(arr);
  return newArr;
};
/**
 * 递归多维数组
 * @param arr(Array) 要遍历的多维数组,取值，只需要自己的值
 * @param children(String) 递归字段
 * @param keysValues(Object) 想要的字段名-对应的字段值
 * @returns Array
 */
export const newRecursionArray = function(arr, children, keysValues = {}) {
  const newArr = [];

  function recursion(arr) {
    for (const item of arr) {
      if (item[children] && item[children].length) {
        recursion(item[children]);
      } else {
        const obj = {};
        if (!basics.isObjNull(keysValues)) {
          for (const key in keysValues) {
            obj[key] = item[keysValues[key]];
          }
        }

        newArr.push(basics.isObjNull(obj) ? item : obj);
      }
    }
  }
  recursion(arr);
  return newArr;
};

/**
 * @desc:获取当天时间
 * @param:fmt {string} 时间格式
 */
export function currentTimeFormat(fmt = "yyyy-MM-dd") {
  const now = new Date();
  const o = {
    "M+": now.getMonth() + 1, // 月份
    "d+": now.getDate(), // 日
    "h+": now.getHours(), // 小时
    "m+": now.getMinutes(), // 分
    "s+": now.getSeconds(), // 秒
    "q+": Math.floor((now.getMonth() + 3) / 3), // 季度
    S: now.getMilliseconds() // 毫秒
  };
  if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (now.getFullYear() + "").substr(4 - RegExp.$1.length));
  for (const k in o) {
    if (new RegExp("(" + k + ")").test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    }
  }
  return fmt;
}

/**
 * @desc:获取当天时间
 * @param:fmt {string} 时间格式
 */
export function currentDayTimeFormat(format = "YYYY/MM/DD HH:mm:ss") {
  return [dayjs().hour(0).minute(0).second(0).format(format), dayjs().add(1, "day").hour(0).minute(0).second(0).format(format)];
}

/**
 * @desc:获取昨天时间
 * @param:fmt {string} 时间格式
 * @param:dateType {number}获取特定时间或时间段类型区分 1：当天， 2：昨天， 3：一周前， 4：一个月前
 */
export function yesterdayTimeFormat(dateType = 1, fmt = "yyyyMMdd") {
  let now;
  switch (dateType) {
  case 2:
    now = new Date(new Date() - 3600 * 1000 * 24);
    break;
  case 3:
    now = new Date(new Date() - 7 * 3600 * 1000 * 24);
    break;
  case 4:
    now = new Date(new Date() - 30 * 3600 * 1000 * 24);
    break;
  default:
    now = new Date();
  }
  const o = {
    "M+": now.getMonth() + 1, // 月份
    "d+": now.getDate(), // 日
    "h+": now.getHours(), // 小时
    "m+": now.getMinutes(), // 分
    "s+": now.getSeconds(), // 秒
    "q+": Math.floor((now.getMonth() + 3) / 3), // 季度
    S: now.getMilliseconds() // 毫秒
  };
  if (/(y+)/.test(fmt)) fmt = fmt.replace(RegExp.$1, (now.getFullYear() + "").substr(4 - RegExp.$1.length));
  for (const k in o) {
    if (new RegExp("(" + k + ")").test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (("00" + o[k]).substr(("" + o[k]).length)));
    }
  }
  return fmt;
}

/**
 * @desc:默认获取当天前后三个月
 * @param:fmt {string} 时间格式
 * @param:num {Number} 数字
 */
export function trimesterTimeFormat(format = "YYYY/MM/DD HH:mm:ss", num = 3) {
  // 获取当前日期
  const now = dayjs();
  // 获取最近三个月的日期范围
  const threeMonthsAgo = now.subtract(num, "month");

  return [threeMonthsAgo.format(format), now.format(format)];
}
/**
 * @desc:判断是否为外部链接
 */
export function isExternal(path) {
  return /^(https?:|mailto:|tel:)/.test(path);
}

/**
 * // 递归判断列表，把最后的children设为undefined
 */
export function getTreeData(data, children = "list") {
  for (var i = 0; i < data && data.length; i++) {
    if (data[i][children] && data[i][children].length < 1) {
      // children若为空数组，则将children设为undefined
      data[i][children] = undefined;
    } else {
      // children若不为空数组，则继续 递归调用 本方法
      getTreeData(data[i][children]);
    }
  }
  return data;
}

/**
 * @description: 过滤枚举
 * @param:value 指定的值
 * @param:enums 需要过滤的枚举
 * @return:
 */
export const filterEnums = (value, enums) => {
  if (basics.isNull(value)) {
    return {
      value: "-",
      label: "-"
    };
  }
  if (basics.isNull(enums) || basics.isArrNull(enums)) {
    return {
      value: "-",
      label: "-"
    };
  }
  const filterEnum = enums.filter(item => {
    if (String(item.value) === String(value)) return item;
  });
  if (filterEnum.length > 0) return filterEnum[0];
  else {
    return {
      value: "-",
      label: "-"
    };
  }
};

/**
 * @description: 过滤元数据枚举
 * @param:value 指定的值
 * @param:enums 需要过滤的枚举
 * @return:
 */
export const filterMeta = (value, field, list) => {
  let enums = [];
  if (field && !basics.isArrNull(list)) {
    const temp = list.filter(item => item.field === field);
    enums = temp.length ? temp[0].items : [];
  }
  if (basics.isNull(value)) {
    return "-";
  }
  if (basics.isNull(enums) || basics.isArrNull(enums)) {
    return "";
  }
  const filterEnum = enums.filter(item => {
    if (String(item.value) === String(value)) return item.label;
  });
  if (filterEnum.length > 0) return filterEnum[0].label;
  else {
    return "-";
  }
};

/*
* 8位生日转换为yyyy-MM-dd */
export const formatBirthday = (birth, split = "-") => {
  if (birth) {
    const day = birth.replace(/(\d{4})(\d{1,2})(\d{1,2})/g, `$1${split}$2${split}$3`);
    return day;
  }
  return "-";
};

/*
* 手机号脱敏
* */
export const strPhone = (val) => {
  if (val && val.length === 11) {
    return val.substr(0, 3) + "****" + val.substr(-4);
  }
  return val;
};

/*
* 金额的转换(分)
* */
export const priceYuan = (val) => {
  if (val) {
    return "￥" + (Number(val) / 100).toFixed(2);
  }
  return "￥0.00";
};
/*
* 金额的转换(分)
* 过万的时候，加上万字
*/
export const priceYuanNum = (val) => {
  const price = (Number(val) / 100).toFixed(2);
  const num = price / 10000;
  if (price && num >= 1) {
    return (Number(num)).toFixed(2) + "万";
  } else if (price && num < 1) {
    return (Number(price)).toFixed(2);
  }
  return "0.00";
};

export const priceNumber = (val) => {
  if (val) {
    const num = (Number(val) / 100);
    if(isNaN(num)) return  val;
    return num.toFixed(2);
  }
  return "0.00";
};
export const priceNumberTwo = (val) => {
  if (!basics.isNull(val)) {
    return (Number(val) / 100).toFixed(0);
  }
  return "-";
};
/*
* 数字过万，单位为万
* */
export const numWan = (val, unit) => {
  const num = val / 10000;
  if (val && num >= 1) {
    return (Number(num)).toFixed(2) + "万";
  } else if (val && num < 1) {
    return (Number(val)).toFixed(2);
  }
  return "0.00";
};

/*
* 头像、图片地址处理
* */
export const picturePath = (imgUrl, type = 1, sync = true) => {
  if (!basics.isNull(imgUrl) && basics.isString(imgUrl)) {
    if (sync && imgUrl.indexOf("http") === -1) {
      return global.imgBasePath + imgUrl;
    } else {
      return imgUrl;
    }
  } else {
    switch (type) {
    case 1:
      return require("@/assets/images/defaultAvatar.png");
    case 2:
      return require("@/assets/images/logo.png");
    }
  }
};

/*
* 驼峰转下划线
* */
export const humpChange = (value) => {
  return value.replace(/([A-Z])/g, "_$1").toLowerCase();
};

/*
* 滚动到顶部
* */
export const scrollTop = () => {
  var scrollTop = document.querySelector(".main-page");
  if (scrollTop) {
    scrollTop.scrollTop = 0;
  }
};

/**
 *将秒转为 时分秒  hh:mm:ss
 * seconds
 */
export const formatTime = (seconds = 0) => {
  const h = Math.floor(seconds / 3600);
  const m = Math.floor((seconds % 3600) / 60);
  const s = seconds % 60;
  return [h || "00", m > 9 ? m : h ? m : "0" + m, s > 9 ? s : "0" + s].filter(a => a).join(":");
};

/**
 * 格式化传入的数字
 * @param number
 * @param placeholder
 */
export const formatNumber = (number = 0, placeholder = "-") => {
  return number ?? placeholder;
};

/**
 * 格式化百分比
 * @param {string|number} value
 * @returns {string|number}
 */
export const formatPercent = (value = 0) => {
  // 如果有百分号，则直接返回
  if (/%/.test(value.toString())) {
    return value;
  }

  // 加上百分号
  return value + "%";
};

export const popNotice = (text) => {
  if (window.Notification.permission === "granted") { // 判断是否有权限
    sendNotification(text);
  } else if (window.Notification.permission !== "denied") {
    window.Notification.requestPermission(function(permission) { // 没有权限发起请求
      sendNotification(text);
    });
  }
};
function sendNotification(text) {
  new Notification("提示", {
    body: text,
    icon: logo,
    vibrate: [300, 100, 300]
  });
}

/* 传入数组 每页数量  返回分页函数 包括下一页 上一页*/
export const pagination = (arr, num) => {
  let page = 0;
  const total = Math.ceil(arr.length / num);
  return {
    /* 最后一页状态*/
    lastPageState: false,
    next() {
      if (page < total) {
        page++;
        this.lastPageState = page >= total;
        return arr.slice((page - 1) * num, page * num);
      } else {
        this.lastPageState = true;
        return [];
      }
    },
    prev() {
      if (page > 1) page--;
      else page = 1;
      return arr.slice((page - 1) * num, page * num);
    }
  };
};

/* 将JSON.stringify 转成对象*/
export const parseJSON = (data) => {
  if (basics.isString(data)) {
    try {
      const parseData = JSON.parse(data);
      if (typeof parseData === "object") {
        return parseData;
      } else {
        return data;
      }
    } catch (e) {
      return data;
    }
  } else if (basics.isObj(data)) {
    Object.keys(data).forEach(key => {
      data[key] = parseJSON(data[key]);
    });
  } else if (basics.isArray(data)) {
    data.forEach((item, index) => {
      data[index] = parseJSON(item);
    });
  }
  return data;
};
// 将秒转换为时分秒
export function formatSeconds(value) {
  let theTime = parseInt(value); // 秒
  let theTime1 = 0; // 分
  let theTime2 = 0; // 小时
  if (theTime > 60) {
    theTime1 = parseInt(theTime / 60);
    theTime = parseInt(theTime % 60);
    if (theTime1 > 60) {
      theTime2 = parseInt(theTime1 / 60);
      theTime1 = parseInt(theTime1 % 60);
    }
  }
  let result = "" + parseInt(theTime) + "秒";
  if (theTime1 > 0) {
    result = "" + parseInt(theTime1) + "分" + result;
  }
  if (theTime2 > 0) {
    result = "" + parseInt(theTime2) + "时" + result;
  }
  return result;
}

/**
 * 计算时间与今天的差值，返回格式为 今天  或者  昨天 或者 日期
 * @param time moment支持的时间格式
 */
export const parseTimeDiffToday = (time) => {
  const date = moment(time);
  const todayDate = moment();
  /** 判断是今天 */
  if (date.isSame(todayDate, "day")) {
    return "今天" + date.format("HH:mm");
  }

  /** 判断是昨天 */
  if (date.isSame(todayDate.subtract(1, "day"), "day")) {
    return "昨天" + date.format("HH:mm");
  }
  /* 不是当前年份： 2022-04-15（yyyy-MM-dd）*/
  if (!(date.isSame(todayDate, "year"))) {
    return date.format("YYYY-MM-DD");
  }

  return date.format("MM-DD HH:mm");
};

/**
 * 时间格式化
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null;
  }
  const format = cFormat || "{y}-{m}-{d} {h}:{i}:{s}";
  let date;
  if (typeof time === "object") {
    date = time;
  } else {
    if (typeof time === "string") {
      if (/^[0-9]+$/.test(time)) {
        // support "1548221490638"
        time = parseInt(time);
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), "/");
      }
    }

    if (typeof time === "number" && time.toString().length === 10) {
      time = time * 1000;
    }
    date = new Date(time);
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  };
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key];
    // Note: getDay() returns 0 on Sunday
    if (key === "a") {
      return ["日", "一", "二", "三", "四", "五", "六"][value];
    }
    return value.toString().padStart(2, "0");
  });
  return time_str;
}

export const parseTimeToDay = (val) => {
  const day = parseTime(val, "{d}");
  const nowDay = parseTime(new Date(), "{d}");
  if (Math.abs(day - nowDay) === 0) return "今天 " + parseTime(val, "{h}:{i}");
  if (Math.abs(day - nowDay) === 1) return "昨天 " + parseTime(val, "{h}:{i}");
  if (Math.abs(day - nowDay) >= 1) return parseTime(val, "{y}-{m}-{d} {h}:{i}");
  // const res = parseTime(val)
  // return res
};

export const getWinClientWidth = () => {
  if (window.innerWidth) return window.innerWidth;
  else if ((document.body) && (document.body.clientWidth)) return document.body.clientWidth;
  return 0;
};

/* 预览file*/
export const createObjectURL = (file) => {
  // 获取 window 的 URL 工具
  var URL = window.URL || window.webkitURL;
  // 通过 file 生成目标 url
  var imgURL = URL.createObjectURL(file);
  return imgURL;
};

/* 将字符串 转换成正则*/
export const encodeReg = (text) => {
  const reg = /[\[\(\$\^\.\]\*\\\?\+\{\}\\|\)]/gi;
  return text.replace(reg, (key) => `\\${key}`);
};

/* 关键字替换*/
export const keywordReplace = (text, keywords = []) => {
  let testReg = false;
  for (const item of keywords) {
    const reg = new RegExp(encodeReg(item), "g");
    testReg = reg.test(text);
    if (testReg) {
      text = text.replace(reg, (...data) => {
        return "".padStart(data[0].length, "*");
      });
      return { text, testReg };
    }
  }
  return { text, testReg };
};


/**
 * 深拷贝
 * @param {string|number} obj
 * @returns {string|number}
 */

export const copyObj = (obj = {}) => {
  // 变量先置空
  let newObj = null;

  // 判断是否需要继续进行递归
  if (typeof obj === "object" && obj !== null) {
    newObj = obj instanceof Array ? [] : {};
    // 进行下一层递归克隆
    for (var i in obj) {
      newObj[i] = copyObj(obj[i]);
    }
    // 如果不是对象直接赋值
  } else newObj = obj;

  return newObj;
};

// 乘法函数，用来得到精确的乘法结果
// 说明：javascript的乘法结果会有误差，在两个浮点数相乘的时候会比较明显。这个函数返回较为精确的乘法结果。
// 调用：mulMethod(arg1,arg2)
// 返回值：arg1乘以arg2的精确结果
export const mulMethod = (arg1, arg2) => {
  var m = 0, s1 = arg1.toString(), s2 = arg2.toString();
  try { m += s1.split(".")[1].length; } catch (e) {
    console.log(e);
  }
  try { m += s2.split(".")[1].length; } catch (e) {
    console.log(e);
  }
  return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m);
};


export const divMethod = (arg1, arg2) => {
  var t1 = 0, t2 = 0, r1, r2;
  try { t1 = arg1.toString().split(".")[1].length; } catch (e) {
    console.log(e);
  }
  try { t2 = arg2.toString().split(".")[1].length; } catch (e) {
    console.log(e);
  }
  if (Math) {
    try {
      r1 = Number(arg1.toString().replace(".", ""));
      r2 = Number(arg2.toString().replace(".", ""));
    }catch (e) {
      console.log(e);
    }
    return (r1 / r2) * Math.pow(10, t2 - t1);
  }
};

/* 判断时间间隔缓存里面得key 是否差了一个自然日 false不相差 true相差*/
export const isDiffDay = (key) => {
  const data = getTriggerTime();
  if (basics.isNull(data[key])) return false;
  const time = data[key];
  return !(moment(time).isBefore(moment().startOf("day")));
};

// 金额每3位加一个逗号 考虑小数点
export function formatAmount(amount) {
  return Number(amount).toLocaleString("en-US");
}
/*对象排序*/
export const objKeySort = (obj) => {
  const newkey = Object.keys(obj).sort();
  const newObj = {};
  for (let i = 0; i < newkey.length; i++) {
    newObj[newkey[i]] = obj[newkey[i]];
  }
  return newObj;
};
