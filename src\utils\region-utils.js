/**
 * 地区代码相关工具方法
 * 用于处理中国行政区划代码的转换和操作
 */

/**
 * 将地区代码数组转换为级联选择器需要的格式
 * @param {Array} regionCodes - 地区代码数组，如 [110100, 320100]
 * @returns {Array} 级联格式数组，如 [[110000, 110100], [320000, 320100]]
 */
export const convertRegionCodesToCascaderFormat = (regionCodes) => {
  if (!regionCodes || !Array.isArray(regionCodes)) {
    return [];
  }

  const result = [];

  for (const regionCode of regionCodes) {
    try {
      // 根据地区代码推断省代码
      const provinceCode = getProvinceCodeFromRegionCode(regionCode);

      if (provinceCode) {
        result.push([provinceCode, regionCode]);
      } else {
        // 如果无法推断省代码，尝试通过API查询
        console.warn(`无法推断地区代码 ${regionCode} 对应的省代码`);
        // 这里可以添加API调用来获取正确的省代码，暂时跳过
      }
    } catch (error) {
      console.error(`处理地区代码 ${regionCode} 时出错:`, error);
    }
  }

  return result;
};

/**
 * 根据地区代码推断省代码
 * 中国行政区划代码规则：前2位是省代码，中间2位是市代码，后2位是区县代码
 * @param {number} regionCode - 地区代码
 * @returns {number|null} 省代码
 */
export const getProvinceCodeFromRegionCode = (regionCode) => {
  if (!regionCode) return null;

  const codeStr = regionCode.toString();

  // 如果是6位代码，前2位是省代码
  if (codeStr.length === 6) {
    const provincePrefix = codeStr.substring(0, 2);
    return parseInt(provincePrefix + "0000");
  }

  // 如果是4位代码，可能是市级代码，前2位是省代码
  if (codeStr.length === 4) {
    const provincePrefix = codeStr.substring(0, 2);
    return parseInt(provincePrefix + "0000");
  }

  // 特殊处理直辖市（北京11、天津12、上海31、重庆50）
  const directMunicipalities = {
    "11": 110000, // 北京
    "12": 120000, // 天津
    "31": 310000, // 上海
    "50": 500000  // 重庆
  };

  const prefix = codeStr.substring(0, 2);
  if (directMunicipalities[prefix]) {
    return directMunicipalities[prefix];
  }

  return null;
};
