<template>
  <div class="xy-side-menu">
    <ul class="first-menu">
      <li
        v-for="(item, index) in menuList"
        :key="index"
        :class="activeIndex === index ? 'active': ''"
        @click="changeMenu(index)">
        <i :class="item.moduleIcon" />
        <span>{{ item.moduleName.substr(0, 2) }}</span>
      </li>
    </ul>
    <transition name="fade" mode="out-in">
      <div class="second-menu" v-if="menuList[activeIndex].list.length && !isCollapse">
        <template v-for="(menu, index) in menuList[activeIndex].list">
          <sideMenuItem :children-menu="menu" :key="index" />
        </template>
      </div>
    </transition>
  </div>
</template>

<script>
import sideMenuItem from "@/components/xy-sideMenu/sideMenuItem.vue";
export default {
  name: "Index",
  components: {
    sideMenuItem
  },
  data() {
    return {
      activeIndex: 0
    };
  },
  computed: {
    menuList() {
      return this.$store.getters.getMenusList;
    },
    isCollapse() {
      return this.$store.getters.getIsCollapsed;
    }
  },
  watch: {
    $route(val) {
      this.activeMenu(val);
    }
  },
  created() {
    this.activeMenu(this.$route);
  },
  methods: {
    changeMenu(index) {
      // console.log(this.menuList,1);
      // 有子菜单，则展示二级菜单，无则收缩二级菜单
      this.activeIndex = index;
      if (!this.menuList[index].list.length) {
        this.$router.push(this.menuList[index].modulePath);
        this.$store.dispatch("setIsCollapse", true);
      } else {
        this.$store.dispatch("setIsCollapse", false);
      }
    },
    activeMenu(val) {
      // 通过路由回显第一级菜单
      if (this.menuList.length) {
        let temp = 0;
        dealMenu(this.menuList);
        function dealMenu(list, index) {
          list.forEach((item, i) => {
            if (item.list && item.list.length) {
              dealMenu(item.list, i);
            } else {
              if (item.modulePath === val.fullPath) {
                temp = index || 0;
              }
            }
          });
        }
        this.activeIndex = temp;
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.xy-side-menu{
  display: flex;
  height: 100%;
  i{
    margin-right: $distance-mini;
    font-size: $font-size-normal;
  }
  .first-menu{
    padding: $distance-small 0;
    width: 80px;
    height: 100%;
    color: $weak-color;
    background: $menu-main-bg-color;
    box-sizing: border-box;
    overflow-y: auto;
    li{
      line-height: 40px;
      height: 40px;
      text-align: center;
      cursor: pointer;
      position: relative;
      &:hover{
        color: #fff;
      }
      &.active{
        &:after{
          content: '';
          position: absolute;
          right: 0;
          top: 50%;
          margin-top: -6px;
          width: 0;
          height: 0;
          border-top: 6px solid transparent;
          border-right: 6px solid $weak-color;
          border-bottom: 6px solid transparent;
          border-left: 6px solid transparent;
        }
      }
    }
  }
  .second-menu{
    width: 120px;
    height: 100%;
    background: $menu-secondary-bg-color;
    overflow-y: auto;
  }
}
</style>
