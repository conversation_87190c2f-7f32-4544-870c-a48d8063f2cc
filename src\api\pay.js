import { request } from "utils/axios";
import { storeRequest } from "utils/storeRequest";

export const goodsList = (params) => request.post("/law-cloud/lc/goods/goodsList", params);// 律所商品列表
export const goodsListCatch = (data) => storeRequest({
  api: goodsList,
  data,
  cacheName: "goodsListCatch",
  storeMaxTime: 5000
});// 律所商品列表缓存
export const goodsListCreateOrder = (params) => request.post("/law-cloud/lc/order/createOrder", params);// 律所商品列表
export const goodsListPay = (params) => request.post("/law-cloud/lc/order/pay", params);// 律所商品列表
export const goodsListQueryOrderStatus = (params) => request.post("/law-cloud/lc/order/queryOrderStatus", params);// 律所商品列表

