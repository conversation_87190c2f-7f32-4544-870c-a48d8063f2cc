<template>
	<div
		class="xy-table bg-[#FFFFFF]"
		:class="[
			{
				'py-[24px] px-[24px]': isMargin,
			},
		]"
	>
		<!-- <div class="top-scroll">回到顶部</div> -->
		<el-backtop :bottom="80" :target="isTableWrap ? '.table-wrap' : null" />
		<!--  筛选条件  -->
		<div v-if="showSearch" class="search-box">
			<xyTableSearch
				ref="xyTableSearch"
        v-bind="$props"
				:key="sourceKey"
				:background="showSearchBackground"
				:border="showSearchBorder"
				:defind-query-search="restablequery"
				:is-handler-rest="isHandlerRest"
				:search-form="searchForm"
				:search-query="query"
				:show-head="showSearchHead"
				:show-reset="showReset"
				@handleSearch="handleSearch"
				@handleSearchRest="clickHandleSearchRest"
			>
				<template slot="searchContainer" slot-scope="{ searchData }">
					<slot :search-data="searchData" name="searchContainer" />
				</template>
				<template slot="searchBtn">
					<slot name="searchBtn" />
					<!--          <app-button class="refresh-btn"  type="info" size="large" @click="$store.dispatch('setIsTableRefresh')">刷新</app-button>-->

					<!-- 导出按钮 -->
					<el-form-item v-if="showExportBtn" label-width="15px">
						<xyExportButton
							:export-option="exportOption"
							:search-data="query"
							:search-query="searchQuery"
							:afferent-column="column"
						/>
					</el-form-item>

					<!-- 新增按钮 -->
					<el-form-item v-if="showAddBtn" label-width="15px">
						<addBtn @click="handleAdd">{{showAddBtnText}}</addBtn>
					</el-form-item>
				</template>
			</xyTableSearch>
		</div>

		<slot :total="rowData.total" name="pageMiddleContainer" />

		<!-- 表格头部筛选展示 -->
		<tableHead
			v-if="showHeadScreen"
			:column="column"
			:default-show-num="defaultShowNum"
			@handleChangeBox="handleChangeBox"
		/>

		<!--  表格标题  -->
		<xyModelHead
			v-if="showTableHead"
			:background="showTableBackground"
			:title="title"
			:title-list="titleList"
		>
			<!-- 字段释义 -->
			<template #paraphrase>
				<i
					v-if="showParaphrase"
					class="el-icon-question filed-icon"
					@click="handleParaphrase('数据详情')"
				/>
			</template>
			<slot :search-data="searchQuery" name="rightContent" />
		</xyModelHead>

		<!-- 字段释义 -->
		<paraphraseDialog :desc-data="paraphraseData" :dialog-visible.sync="paraphrase" />

		<!--  数据表格 v-if="isTableRefresh"  -->
		<el-table
			:id="id"
			:key="key"
			ref="multipleTable"
			:border="border"
			:data="rowData.data"
			:expand-row-keys="exprends"
			:max-height="maxHeight"
			:row-class-name="tableRowClassName"
			:row-key="rowKey"
			:stripe="stripe"
			:tree-props="treeProps"
			highlight-current-row
			style="width: 100%"
			v-bind="$attrs"
			:show-summary="showSummary"
			:summary-method="getSummaries"
			@select-all="selectionAll"
			@selection-change="selectionChange"
			@sort-change="handlesortChange"
			v-loading="loading"
		>
			<!--   序号   -->
			<el-table-column v-if="index" align="left" label="序号" type="index" width="70" fixed>
				<template slot-scope="scope">{{
					(searchQuery.currentPage - 1) * searchQuery.pageSize + scope.$index + 1
				}}</template>
			</el-table-column>
			<!--   多选   -->
			<el-table-column
				v-if="selection"
				align="center"
				type="selection"
				:reserve-selection="true"
				width="55"
			/>
			<!-- 单选 -->
			<el-table-column v-if="selectionRadio" align="center" width="55">
				<template v-slot="scope">
					<el-radio
						v-model="tableRadio"
						:label="scope.row.id"
						@change="handleRadioChange(scope.row)"
						>&nbsp;</el-radio
					>
				</template>
			</el-table-column>

			<template v-if="tableColumn && tableColumn.length">
				<template v-for="(item, i) in tableColumn">
					<el-table-column
						v-if="!item.tableHidden"
						:key="i"
						:label="item.label"
						:min-width="item.minWidth"
						:prop="item.prop"
						:show-overflow-tooltip="item.hideToolTip ? false : true"
						:sortable="item.sortable"
						:width="item.width"
					>
						<!-- 两级表头 -->
						<template v-if="item.children && item.children.length > 0">
							<el-table-column
								v-for="(li, idx) in item.children"
								:key="idx"
								:label="li.label"
								:prop="li.prop"
								:sortable="li.sortable"
							>
								<!-- 自定义列表头部 -->
								<template slot="header">
									<popoverShow :item-list="li" />
								</template>
								<template slot-scope="{ row }">
									<tableTempList :item-list="li" :row-list="row" />
								</template>
							</el-table-column>
						</template>

						<!-- 单个表头 -->
						<!-- 自定义列表头部 -->
						<template slot="header">
							<popoverShow :item-list="item" />
						</template>
						<template slot-scope="{ row }">
							<tableTempList :item-list="item" :row-list="row" />
						</template>
					</el-table-column>
				</template>
			</template>
			<template v-else>
				<slot />
			</template>
			<template>
				<slot name="handleButton" />
			</template>
			<template #empty>
				<div class="empty flex-dis flex-align-center flex-space-center flex-column">
					<img src="@/assets/images/empty.png" alt="" />
					<div class="empty-text">当前条件下暂无数据～</div>
				</div>
			</template>
		</el-table>
		<!-- 表格分页  -->
		<div class="pagination">
			<xyPagination
				v-if="showPagination"
				:current.sync="searchQuery.currentPage"
				:page-size.sync="searchQuery.pageSize"
				:total="rowData.total"
				@handleCurrentChange="handleCurrentChange"
				@handleSizeChange="handleSizeChange"
			/>
		</div>

		<div style="display: none">{{ isHandlerRest }}</div>
	</div>
</template>

<script>
import xyTableSearch from "@/components/xy-tableSearch/index.vue";
import xyModelHead from "@/components/modelHead/index.vue";
import xyExportButton from "@/components/xy-buttons/exportButton.vue";
import xyPagination from "@/components/xy-pagination/index.vue";
import paraphraseDialog from "@/components/paraphraseDialog/index.vue";
import addBtn from "@/components/xy-buttons/addButton.vue";
import tableTempList from "./tableTemp.vue";
import tableHead from "@/components/tableScreenHead";
import popoverShow from "./popover.vue";
// import scrollFunction from '@/minixs/mouseScroll'
import { humpChange } from "@/utils/toolMethods";

export default {
  name: "XyPage",
  components: {
    tableTempList,
    xyTableSearch,
    xyModelHead,
    xyExportButton,
    xyPagination,
    paraphraseDialog,
    addBtn,
    tableHead,
    popoverShow,
  },
  props: {
    /** 是否需要边距 */
    isMargin: {
      type: Boolean,
      default: true,
    },
    query: {
      type: Object,
      default: () => {},
      desc: "表格额外的参数",
    },
    request: {
      type: Object,
      default: () => {
        return {
          getListUrl: () => Promise.resolve(),
          deleteUrl: () => Promise.resolve(),
        };
      },
    },
    /* column属性值说明:
		 * label: 表头
		 * width: 列宽
		 * prop: 列绑定的值
		 * filter: 过滤器的值
		 * tableHidden: 表格中是否显示
		 * emptyDefault: 空值占位符 */
    column: {
      type: Array,
      default: () => [],
    },
    index: {
      type: Boolean,
      default: false,
      desc: "表格序号，不断号，分页连续编号",
    },
    selection: {
      type: Boolean,
      default: false,
      desc: "表格多选框",
    },
    selectionRadio: {
      type: Boolean,
      default: false,
      desc: "表格单选框",
    },
    title: {
      type: String,
      default: "数据列表",
    },
    titleList: {
      type: Array,
    },
    showExportBtn: {
      type: Boolean,
      default: false,
    },
    exportOption: {
      type: Object,
    },
    tableData: {
      type: Object,
      default: () => {
        return {
          data: [],
          total: 0,
        };
      },
      desc: "表格数据信息，包括行数据和总条数",
    },
    showSearch: {
      type: Boolean,
      default: true,
      desc: "是否显示筛选区域",
    },
    stripe: {
      type: Boolean,
      default: true,
    },
    border: {
      type: Boolean,
      default: false,
    },
    maxHeight: {
      type: [String, Number],
    },
    showTableHead: {
      type: Boolean,
      default: false,
      desc: "是否显示表格顶部操作区",
    },
    showPagination: {
      type: Boolean,
      default: true,
      desc: "是否显示表格分页",
    },
    showTableBackground: {
      type: Boolean,
      default: true,
      desc: "是否显示表格标题栏背景色",
    },
    showSearchBackground: {
      type: Boolean,
      default: true,
      desc: "是否显示筛选区标题栏背景色",
    },
    showSearchHead: {
      type: Boolean,
      default: true,
      desc: "是否显示筛选区标题栏",
    },
    showSearchBorder: {
      type: Boolean,
      default: true,
      desc: "是否显示筛选区边框",
    },
    defindQuerySearch: {
      type: Object,
      default: () => {},
      desc: "搜索框的自定义的参数",
    },
    exprends: {
      type: Array,
      default: () => [],
    },
    showReset: {
      type: Boolean,
      default: true,
      desc: "是否显示筛选条件后的重置按钮，否则显示顶部的重置按钮",
    },
    showParaphrase: {
      type: Boolean,
      default: false,
      desc: "显示字段释义",
    },
    paraphraseData: {
      type: Array,
      default: () => [],
      desc: "字段释义",
    },
    showAddBtn: {
      type: Boolean,
      default: false,
      desc: "新增按钮",
    },
    isSortUnderlineQuery: {
      type: Boolean,
      default: true,
      desc: "排序是否用下划线",
    },
    showHeadScreen: {
      type: Boolean,
      default: false,
      desc: "展示字段筛选部分",
    },
    defaultShowNum: {
      type: [Number, String],
      default: 10,
      desc: "展示筛选多少个字段",
    },
    tempIds: {
      type: Array,
      default: () => [],
      desc: "三方案源的ids，临时需求，下个版本要删除",
    },
    showCurrentTable: {
      type: String,
      desc: "只刷新当前的表格",
    },
    treeProps: {
      type: Object,
      default: () => {
        return {
          children: "children",
          hasChildren: "hasChildren", // 配和懒加载使用
          isNeedFile: false,
        };
      },
      desc: "关于表格的树形的子节点的参数",
    },
    rowKey: {
      type: String,
      default: "id",
      desc: "唯一值，渲染",
    },
    isNeedHeight: {
      type: Boolean,
      default: false,
      desc: "是否表格需要最小高度",
    },
    showSummary: {
      type: Boolean,
      default: false,
      desc: "是否汇总",
    },
    summaryData: {
      type: Array,
      default: () => {
        return [];
      },
      desc: "汇总的条数",
    },
    showAddBtnText: {
      type: String,
      default: "新增",
      desc: "新增按钮的文字",
    },
    /* 是否加载table就请求数据*/
    isLoadTableRequest: {
      type: Boolean,
      default: true,
      desc: "是否加载table就请求数据",
    },
    defaultLoadingPage: {
      type: Boolean,
      default: true,
      desc: "是否默认加载页面"
    }
  },
  data() {
    return {
      id: "table-" + +new Date() + ((Math.random() * 1000).toFixed(0) + ""),
      searchQuery: {
        currentPage: 1,
        pageSize: 10,
      },
      rowData: {
        data: [],
        total: 0,
      },
      sortQuery: {},
      restablequery: this.defindQuerySearch, // 传给筛选的自定义参数
      paraphrase: false,
      tableColumn: this.column, // 表格
      colorRows: this.tempIds,
      temp_data: [],
      key: 1,
      sourceKey: 1,
      isTableWrap: null,
      selectionListsCurrent: [], // 多选选的,当前页选中的
      selectionListsTotal: {}, // 多选的，总共已选择
      isselectionTurning: false,
      tableRadio: -1,
      loading: false,
    };
  },
  computed: {
    searchForm() {
      let temp = [];
      temp = this.column.filter(item => item.search);

      return temp;
    },
    isTableRefresh() {
      return this.$store.getters.isTableRefresh;
    },
    isHandlerRest() {
      this.handleTableReresh("isHandlerRest");
      return this.$store.getters.isHandlerRest;
    },
    // 监听table配置是否改变
    getTableConfig() {
      return this.$store.getters.getTableConfig;
    },
  },
  watch: {
    column: {
      handler(val) {
        // 当有头部筛选的时候已经触发了表格字段更改
        if (this.showHeadScreen) return;
        this.tableColumn = val;
        this.renderTables();
      },
      deep: true,
      immediate: true,
    },
    // 传入的表格的数据值
    tableData: {
      handler(val) {
        this.rowData = val;
      },
      deep: true,
    },
    isTableRefresh(value){
      if(value){
        const tableId =  this.showCurrentTable || String(this._uid);
        if (value !== tableId)  return;
        setTimeout(() => {
          console.log("isTableRefresh==========");
          this.getTableList();
        }, 200);
      }
    },
    // 监听table配置是否改变
    getTableConfig: {
      handler(data){
        const tableId =  this.showCurrentTable || String(this._uid);
        const { key, ...arg } = data;
        console.log(key, tableId);
        if (key !== tableId)  return;
        setTimeout(() => {
          console.log("getTableConfigChange==========");
          this.handleSearch(arg || {});
        }, 200);
      },
      deep: true,
    }
  },
  created() {
    this.$store.commit("SET_TABLE_ID", String(this._uid));
    this.handleAsyncSearch();
  },
  activated() {
    this.$store.commit("SET_TABLE_ID", String(this._uid));
  },
  mounted() {
    this.$nextTick(() => {
      if (!this.showSearch && this.isLoadTableRequest) {
        // console.log('执行4')
        this.getTableList();
      }
      if (this.isNeedHeight || this.showHeadScreen) {
        // 是否有‘table-wrap’类名
        this.isTableWrap = document.querySelector(".table-wrap");
      }
    });
  },
  // mixins: [scrollFunction],
  methods: {
    /* 刷新，只能刷新当前的表格 */
    handleTableReresh(name) {
      const value = this.$store.getters[name];
      const tableId =  String(this._uid);
      if(name === "isTableRefresh"){
        if(!value) return;
        // 当是拥有自己的刷新字段的话，只能用自己的字段控制哦，showCurrentTable
        if (this.showCurrentTable && value !== this.showCurrentTable) return;
        if (value !== tableId) return;
      }else if(value){
        return;
      }
      if (name === "isHandlerRest") {
        this.handleSearchRest();
        this.handleAsyncSearch();
      }
      // console.log('执行1', name, value)
      setTimeout(() => {
        this.getTableList();
      }, 200);
    },
    /** 单选表单时触发 */
    handleRadioChange(val) {
      this.$emit("row-selection", val);
    },
    // 给行的添加,class
    tableRowClassName({ row, rowIndex }) {
      // console.log('我在努力变色')
      if (row.id && this.tempIds.indexOf(row.id) >= 0) {
        return "warning-row";
      }
      return "";
    },
    // 新增按钮
    handleAdd() {
      this.$emit("handleAdd", true);
    },
    /* 条件筛选 */
    handleSearch(searchData) {
      // 重置页码和页数
      this.searchQuery = { ...this.searchQuery, ...searchData };
      // 如果是空的话，删除
      for (const key in this.searchQuery) {
        if (this.searchQuery[key] === "" || this.searchQuery[key] === " ") {
          delete this.searchQuery[key];
        }
      }
      this.$emit("handleSearch", this.searchQuery);

      this.getTableList();
    },
    getCacheSearch() {
      const cacheKey = `xy-table-search:${this.$route.fullPath}`;
      const cacheData = localStorage.getItem(cacheKey);
    },
    // 请求表格数据
    getTableList() {
      // this.rowData.data = []
      return new Promise((resolve, reject) => {
        this.loading = true;
        // 清除筛选条件中空数组
        for (const key in this.searchQuery) {
          if (this.basics.isArray(this.searchQuery[key]) && this.searchQuery[key].length < 1) {
            delete this.searchQuery[key];
          }
        }
        // 缓存搜索条件
        let _searchData = {  };
        if(!this.defaultLoadingPage){
          const cacheKey = `xy-table-search:${this.$route.fullPath}`;
          _searchData = JSON.parse(localStorage.getItem(cacheKey));
        }

        this.request
          .getListUrl({ ..._searchData, ...this.query, ...this.searchQuery })
          .then(res => {
            if (res) {
              const data = (this.basics.isArray(res) && res) || res.data || res.records || [];
              // 当需要节点展开，却没有唯一键的时候，自定义唯一键
              const data_r = this.treeProps.isNeedFile ? this.loopArry(data, "id") : data;
              this.rowData = {
                data: data_r,
                total: res.total || res.totalNumber,
              };
              return resolve(this.rowData);
            } else {
              // ? 当传入了 tableData 时，则直接将 tableData 赋值给 rowData
              if (this.tableData.data.length > 0) {
                this.rowData = this.tableData;
                return resolve(this.tableData);
              }

              // 返回的没有data的时候
              this.restDataTable();

              return resolve({
                data: [],
                total: 0,
              });
            }
          })
          .finally(() => {
            this.loading = false;
          });
      });
    },
    /* 重置 */
    handleSearchRest() {
      this.renderSearch();
      this.restDatas({});
    },
    /* 重置的数据 */
    restDatas(restData) {
      // console.log('restData:', restData)
      this.searchQuery = { currentPage: 1, pageSize: 10, ...restData };
      for (const key in this.restablequery) {
        this.restablequery[key] = "";
      }
      this.$emit("handleRest", {
        ...this.searchQuery,
        ...this.restablequery
      });
    },
    /* 手动点击重置 */
    clickHandleSearchRest(restData) {
      this.restDatas(restData);
      this.getTableList();
    },
    /* 通过ref主动调用刷新表格 */
    refreshTable(restData) {
      this.searchQuery = { ...this.searchQuery, ...restData };
      this.getTableList();
    },
    /* 渲染表格 */
    renderTables() {
      this.key++;
    },
    /* 渲染筛选 */
    renderSearch() {
      this.sourceKey++;
    },

    // 返回的没有data的时候
    restDataTable() {
      this.rowData = {
        data: [],
        total: 0,
      };
    },

    /* 条数切换触发 */
    handleSizeChange(val) {
      this.searchQuery.pageSize = val;
      this.getTableList();
      this.$emit("handleSizeChange");
    },
    /* 页数切换触发 */
    handleCurrentChange(val) {
      this.searchQuery.currentPage = val;
      this.getTableList();
      this.$emit("handleCurrentChange");
    },
    /* 处理筛选中异步数据 */
    handleAsyncSearch() {
      this.column.forEach(item => {
        if (item.syncData && item.syncData.url && this.basics.isFunction(item.syncData.url)) {
          const request = item.syncData.url;
          const params = this.basics.isNull(item.syncData.params) ? {} : item.syncData.params;
          request(params).then(res => {
            this.$set(item.syncData, "data", res.records || res);
          });
        }
      });
    },
    // 排序开始----------------------------
    deleteObj(data, keys) {
      keys.forEach(key => {
        this.$delete(data, key);
      });
      return data;
    },
    // 服务端的排序
    handlesortChange(row) {
      const field = this.sortFildCustom(row.prop);
      const obj = row.order
        ? {
          field,
          way: row.order === "ascending" ? "ASC" : "DESC",
				  }
        : {};

      this.searchQuery = row.order
        ? Object.assign({}, this.searchQuery, obj)
        : this.deleteObj(this.searchQuery, ["field", "way"]);
      this.getTableList();
      console.log("第四");
    },
    /* 过滤出需要的item*/
    filterCustomItem(list, key, data) {
      let propData = {};
      for (const item of list) {
        if (item.children) {
          propData = this.filterCustomItem(item.children, key, data);
          if (!this.basics.isObjNull(propData)) {
            return propData;
          }
        }
        if (item[key] === data) {
          propData = item;
          break;
        }
      }
      return this.basics.isObj(propData) ? propData : {};
    },
    // 自定义排序字段查找 自定义字段时需准确
    sortFildCustom(fieldProp) {
      const propItem = this.filterCustomItem(this.tableColumn, "prop", fieldProp);
      return propItem && propItem.sortProps
        ? propItem.sortProps
        : this.isSortUnderlineQuery
          ? humpChange(fieldProp)
          : fieldProp;
    },
    // 排序结束----------------------------

    // 字段释义开始----------------------------
    // 字段释义的展示
    handleParaphrase() {
      this.paraphrase = true;
    },
    // 字段筛选的展示部分
    handleChangeBox(val) {
      const data = val.map(res => res.label);
      this.tableColumn = this.column.filter(i => (data.includes(i.label) && !i.tableHidden) || i.isSelectCheck === false);
      this.renderTables();
    },
    // 字段释义结束----------------------------

    // 表格的多选，跨页面的多选开始----------------------------
    /**
		 * 当也页数切换的时候，表示页面已经确定了选择
		 *
		 */
    selectionChange(val) {
      console.log(val, "我在选择");
      // 传入当前的页面选中
      this.$emit("selectionChange", val);
    },
    // 当页全选
    selectionAll(selection) {
      selection.forEach(res => {
        res.check = true;
      });
      this.$emit("selectionChangeAll", [...selection]);
    },
    // 表格的多选，跨页面的多选结束----------------------------

    // 循环数组
    loopArry(tree = [], lab) {
      const self = this;
      const arr = [];
      const child = this.treeProps.children;
      if (!!tree && tree.length !== 0) {
        tree.forEach(item => {
          let obj = {};
          obj = item;
          obj[lab] = +new Date() + ((Math.random() * 1000).toFixed(0) + "");
          if (item[child] && item[child].length > 0) obj[child] = self.loopArry(item[child], lab); // 递归调用
          arr.push(obj);
        });
      }
      return arr;
    },
    getSummaries() {
      return this.summaryData;
    },
  },
};
</script>

<style lang="scss" scoped>
// .table-scroll-wrap{
//   overscroll-behavior-x: contain;
//   scroll-snap-type: x mandatory;
//   scrollbar-width: none;
// }
.xy-table {
	.success,
	.warning {
		padding-left: 12px;
		position: relative;

		&:before {
			content: '';
			position: absolute;
			left: 0;
			top: 6px;
			width: 6px;
			height: 6px;
			border-radius: 100%;
		}
	}

	.success {
		&:before {
			background: $primary-color;
		}
	}

	.warning {
		&:before {
			background: #ff8c0b;
		}
	}

	/*.tip{
    margin-left: 5px;
    color: #cccccc;
    font-size: 16px;
    cursor: pointer;
    !*position: absolute;*!
    top: 4px;
    &:hover{
      color: $warning-color;
    }
  }*/
	.empty {
		padding: 72px 0;
		img {
			width: 280px;
		}
		&-text {
			font-size: 16px;
			font-weight: 400;
			color: #666666;
		}
	}
}

.xy-table /deep/ .warning-row td {
	background: oldlace !important;
}
</style>
