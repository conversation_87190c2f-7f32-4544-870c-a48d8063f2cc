<template>
	<div class="tabs m-b-15">
		<el-button
			:type="(Number(value) === i && 'primary') || ''"
			v-for="(item,i) in lists"
			:key="item"
			@click="handleButton(item,i)"
		>{{item}}</el-button>
	</div>
</template>

<script>
export default {
  name: "Tabs",
  props: {
    lists: {
      type: Array,
      default: () => {
        return [];
      }
    },
    value: {
      type: [String, Number],
      default: 0
    }
  },
  data() {
    return {
      typeValue: ""
    };
  },
  methods: {
    handleButton(item, i) {
      this.$emit("input", i);

      // 方法
      this.$emit("tab-click");
    }
  }
};
</script>

<style lang="scss" scoped>
.tabs {
}
</style>
