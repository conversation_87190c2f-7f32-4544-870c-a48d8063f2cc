import global from "@/utils/constant";
import {
  deleteClueFollowLabel,
  saveClueFollow,
  saveClueFollowLabel,
  updateClueFollowUser,
} from "@/api/clues";
import ClueDetailTextareaTag from "views/leadManagement/leadFollowUp/leadFollowUpPage/ClueDetailTextareaTag.vue";
import { dataDetailLabelList, dataDetailList } from "@/api/common";
import { Message } from "element-ui";
import store from "store";
import { lcInstitutionStaffList } from "@/api/system";
import { grabType } from "@/enum/staffing";

const searchForm = [
  {
    prop: "followStatus",
    label: "跟进状态",
    tableHidden: true,
    search: true,
    filterUrl: true,
    type: global.formItemType.select,
    syncData: {
      url: dataDetailList,
      value: "value",
      label: "label",
      params: { groupCode: "CLUE_FOLLOW_EXPORT" },
    },
  },
  {
    prop: "staffId",
    label: "跟进人",
    tableHidden: true,
    search: true,
    filterUrl: true,
    type: global.formItemType.select,
    syncData: {
      url: lcInstitutionStaffList,
      value: "id",
      label: "userName",
    },
  },
  {
    prop: "a1",
    propKey: ["publicTimeStart", "publicTimeEnd"],
    label: "发布时间",
    search: true,
    tableHidden: true,
    type: global.formItemType.datetimerange,
    valueFormat: "yyyy/MM/dd HH:mm:ss",
  },
  {
    prop: "smsTemplateTitle",
    label: "发布地区",
    tableHidden: true,
    search: true,
    multiple: true,
    type: global.formItemType.regioncascader,
  },
  {
    prop: "keyWords",
    label: "搜索",
    tableHidden: true,
    search: true,
    type: global.formItemType.input,
    placeholder: "关键字模糊匹配",
  },
  {
    prop: "todo",
    propKey: ["beginDate", "endDate"],
    label: "抢单时间",
    search: true,
    tableHidden: true,
    type: global.formItemType.daterange,
    valueFormat: "yyyyMMdd",
  },
];

export const tableColumn = [
  ...searchForm,
  {
    prop: "userName",
    label: "姓名",
    width: "142px",
    fixed: true,
    renderComponentName: "InputText",
    renderComponentObj: {
      request: updateClueFollowUser,
      params: {
        serverId: "serverId",
        userName: "userName",
      },
      props: {
        maxlength: 5
      }
    },
  },
  {
    prop: "businessId",
    label: "线索ID",
    width: "120px",
  }, {
    prop: "clueInfo",
    label: "线索描述",
    width: "448px",
  },
  /* {
    prop: "amountGradeValue",
    label: "涉案金额",
    width: "135px",
  },*/{
    prop: "userPhone",
    label: "发布人手机号",
    width: "135px",
    search: true,
    type: global.formItemType.input,
  },
  {
    prop: "provinceName/regionName",
    label: "发布人地区",
    render: (h, { row }) => {
      return (
        <span>{row.provinceName}{row.regionName}</span>
      );
    },
    width: "120px",
  },
  {
    prop: "serverWay",
    label: "抢单方式",
    search: true,
    filterUrl: true,
    type: global.formItemType.select,
    width: "120px",
    syncData: {
      data: grabType,
    },
  },
  {
    prop: "lcLawClueLabel",
    label: "跟进标签",
    minWidth: "480px",
    filterUrl: true,
    truncate: false,
    hideToolTip: true,
    syncData: {
      url: () => dataDetailLabelList({
        groupCode: "LC_LAW_CLUE_LABLE",
      })
    },
    render: (h, { row, column }) => {
      const syncData = column.syncData.data || {};
      return (
        <ClueDetailTextareaTag flatList={syncData.flatData}  tagsList={syncData.data} class="pt-[8px]"  labelValues={row.lcLawClueLabel} onDelete={(val) => {
          const labelValues = val.map(item => item.value).join(",");

          deleteClueFollowLabel({
            labelValues,
            serverId: row.serverId,
          }).then(() => {
            Message.success("删除成功");
            store.dispatch("setIsTableRefresh");
          });

        }} onAdd={(val) => {
          const labelValues = val.map(item => item.value).join(",");

          saveClueFollowLabel({
            labelValues,
            serverId: row.serverId,
          }).then(() => {
            Message.success("添加成功");
            store.dispatch("setIsTableRefresh");
          });
        }}/>
      );
    },
  },
  {
    prop: "consumeType",
    label: "抢单类型",
    width: "140px",
    search: true,
    type: global.formItemType.select,
    filterUrl: true,
    syncData: {
      url: dataDetailList,
      value: "value",
      label: "label",
      params: { groupCode: "LC_CONSUME_ACCOUNT_TYPE" },
    },
  },
  {
    prop: "qdInstitutionStaffName",
    label: "跟进人",
    width: "90px",
  },
  {
    prop: "typeLabel",
    label: "线索类型",
    width: "100px",
  },
  {
    prop: "lastFollowInfo",
    label: "最新跟进记录",
    width: "180px",
    hideToolTip: true,
    renderComponentProps: "followInfo",
    renderComponentName: "InputText",
    renderComponentObj: {
      request: saveClueFollow,
      params: {
        serverId: "serverId",
        followInfo: "followInfo",
      },
    },
  },
  {
    prop: "followStatus",
    label: "跟进状态",
    width: "118px",
    filterUrl: true,
    type: global.formItemType.select,
    syncData: {
      url: dataDetailList,
      value: "value",
      label: "label",
      params: { groupCode: "CLUE_FOLLOW_EXPORT" },
    },
    render: (h, { row, column }) => {
      return (
        <p>
          {row.followStatus === 1 ? (
            <div class="flex items-center text-[#666666]">
              <i class="mr-[8px] w-[6px] h-[6px] bg-[#3887F5] rounded-full opacity-100" />
              <span>{column.syncData?.data?.find?.(item => Number(item.value) === 1)?.label}</span>
            </div>
          ) : null}
          {row.followStatus === 2 ? (
            <div class="flex items-center text-[#666666]">
              <i class="mr-[8px] w-[6px] h-[6px] bg-[#EB4738] rounded-full opacity-100" />
              <span>{column.syncData?.data?.find?.(item => Number(item.value) === 2)?.label}</span>
            </div>
          ) : null}
        </p>
      );
    },
  },
  {
    prop: "publishTime",
    label: "发布时间",
    width: "180px",
  },
  {
    prop: "createTime",
    label: "抢单时间",
    width: "180px",
  },
  {
    prop: "clueType",
    label: "线索分类",
    search: true,
    filterUrl: true,
    tableHidden: true,
    type: global.formItemType.select,
    syncData: {
      url: dataDetailList,
      value: "value",
      label: "label",
      params: { groupCode: "LC_CLUE_TYPE" }
    },
  },
  {
    prop: "clueType",
    label: "线索分类",
    width: "100px",
  },
  {
    prop: "sortType",
    label: "排序",
    tableHidden: true,
    search: true,
    filterUrl: true,
    type: global.formItemType.select,
    syncData: {
      data: global.adminConfig.clueSortType,
    }
  },
];

