<template>
  <div class="min-h-[390px] mt-[16px] bg-[#FFFFFF] rounded-[2px]">
    <workbench-card-title>
      成单喜报
      <template #append>
        <app-button size="medium" icon="!text-[16px] icon-wodexibao !mr-0"  @click="goGoodPage">
          上传喜报
        </app-button>
      </template>
    </workbench-card-title>

    <div class="list-box">
				<div
					v-for="item in list"
          class="flex"
          @click.stop="handleClick(item)"
					:key="item.id">
            <img
              class="w-[14px] h-[164px] block mr-[10px] shrink-0"
              src="@/views/goodNewsManagement/GoodNewsSquare/img/Frame938.png"
              alt=""
            />
            <div class="lists">
              <div class="text-[14px] text-[#666666]">{{ item.pubTime }}</div>
              <div
                class="bg-[#F5F5F7] rounded-[4px] p-[24px] box-border  cursor-pointer mt-[16px] box-border"
              >
                <div class="flex">
                  <img class="w-[36px] h-[36px] rounded-[4px] mr-[12px] shrink-0" alt="" :src="item.logo||require('@/views/goodNewsManagement/GoodNewsSquare/img/defalut_logo.png')" />
                  <div class="text-[16px] text-[#333333] mr-[24px] lists-content">
                    <p>{{ item.content }}</p>
                    <div class="flex mt-[20px] ">
                      <p class="text-[12px] text-[#666666]">成案金額：<label class="text-[16px] text-[#333333]">{{ priceYuan(item.amount)}}</label></p>
                      <p class="text-[12px] theAmountOfTheOrder text-[#666666]">委托金额：<label class="text-[16px] text-[#333333]">{{ priceYuan(item.caseEntrustAmount)}}</label></p>
                    </div>
                  </div>
                </div>
              </div>
        </div>
      </div>
		</div>

    <good-news-square-detail v-model="show" :data="detailInfo" />
  </div>
</template>

<script>
import WorkbenchCardTitle from "views/workbenchManagement/workbench/components/CardTitle.vue";
import { lcGoodNewsV2SquareDetailById, lcGoodNewsV2SquareList } from "@/api/goodNewsManagement";
import { priceYuan } from "utils/toolMethods";
import AppButton from "components/appButton/index.vue";
import GoodNewsSquareDetail from "views/goodNewsManagement/GoodNewsSquare/components/GoodNewsSquareDetail.vue";
import { turnToUploadGoodNews } from "utils/turnPage";

export default {
  name: "WorkbenchGoodNews",
  components: { GoodNewsSquareDetail, AppButton, WorkbenchCardTitle },
  data() {
    return {

      show: false,
      list: [],
      detailInfo: {},
    };
  },
  mounted() {
    lcGoodNewsV2SquareList().then(data => {
      /* 只要10条数据*/
      data = data.slice(0, 10);
      this.list = data.map(res => {
        let _content = " ";
        if(res.type === 1){
          _content = `恭喜【${res.userName}】 律师在${res.regionName}成案！`;
        }else if(res.type === 2){
          _content = `恭喜 ${res.institutionName || "****"} 【${res.userName}】 律师在${res.regionName}成案！`;
        }
        return {
          ...res,
          content: _content,
        };
      });

      console.log(this.list, 888);
    });
  },
  methods: {
    priceYuan,
    jump(index){
      this.$set(this.list[index], "read", 1);
      this.$router.push({
        path: "/goodNews",
        query: {
          id: this.list[index].id
        }
      });
    },
    /* 更多*/
    goGoodPage() {
      turnToUploadGoodNews();
    },
    handleClick({ id, type }) {
      lcGoodNewsV2SquareDetailById({
        id,
        type,
      }).then(data => {
        this.detailInfo = data;
        this.show = true;
      });
    },
  }
};
</script>

<style scoped lang="scss">
.list-box{
  padding-left: 24px;
  padding-right: 14px;
  margin-top: 20px;
  max-height: 891px;
  overflow-y: scroll;

  .lists{
    width: calc(100% - 32px);
  }
  .lists-content{
   flex: 0 0 calc(100% - 50px);
  }
}
.upload-btn{
  width: 91px;
  height: 28px;
  background: #3887F5;
  border-radius: 4px 4px 4px 4px;
  font-weight: 400;
  font-size: 12px;
  color: #FFFFFF;
  text-align: center;
  line-height: 28px;
  img{
    width: 14px;
    height: 14px;
    vertical-align: middle;
    position: relative;
    top: -1px;
  }

}
.theAmountOfTheOrder{
  padding-left: pxToPer(44);
}
</style>
