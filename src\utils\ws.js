import basics from "utils/basicsMethods.js";

export const CMD = {
  /* 心跳*/
  HEART: 1,
  /* 心跳返回*/
  HEART_REPLY: 2,
  /* 律客云案源推送*/
  CASE_PUSH: 1001,
  /* 案源剩余不足推送*/
  CASE_PUSH_NOT_ENOUGH: 1002,
  /* 无登录信息*/
  NO_LOGIN_INFO: 400,
  /* 登录失效*/
  LOGIN_INVALID: 401,
  /* 新账号登录,被迫下线*/
  NEW_LOGIN: 402,
};

/* 退出登录状态*/
const LOGIN_OUT_STATE = [CMD.NO_LOGIN_INFO, CMD.LOGIN_INVALID];

/* ws状态*/
export const WS_STATE = {
  CONNECTING: 0,
  OPEN: 1,
  CLOSING: 2,
  CLOSED: 3,
};

class WS {
  wsInstance  = {
    onopen(){},
    onclose(){},
    onerror(){},
    onmessage(){},
    send(){},
    close(){}
  }
  options  = {
    heartTimeout: 5000,
    /* 重连间隔*/
    reconnectTimeout: 5000,
    /* 重连最大次数*/
    reconnectMaxTimes: 5,
    onmessage(){},
    onopen(){},
    onclose(){},
    /* 所有消息的回调*/
    onallmessage(){}
  }
  /* 当前重连次数*/
  reconnectTimes = 1
  /* 重连锁 防止极限问题 重复多次重连*/
  reconnectLock = false
  /* 重连定时器*/
  reconnectTimer = undefined
  /* 心跳计时器*/
  heartTimer = undefined
  /* 手动关闭ws状态*/
  closeState = false

  constructor(options) {
    this.options = { ...this.options, ...options };
  }
  /* 链接*/
  connect(url) {
    this.wsInstance = new WebSocket(url);
    this.closeState = false;
    this.wsInstance.onopen = () => {
      /* 开启ws后重置重连次数*/
      this.reconnectTimes = 1;
      this.optionsCallback("onopen");
      this.heartCheck();
    };
    this.wsInstance.onclose = (e) => {
      console.log("ws closed", e);
      this.optionsCallback("onclose", e);
      this.closeHeart();
      // 重连
      this.reconnect(url);
    };
    this.wsInstance.onerror = (e) => {
      console.log("ws error", e);
      this.optionsCallback("onerror", e);
      this.closeHeart();
      // 重连
      this.reconnect(url);
    };
    this.wsInstance.onmessage = ({ data }) => {
      let message = {};
      try {
        message = JSON.parse(data);
      }catch (e) {
        console.log(e);
      }
      this.heartCheck();
      /* 处理message*/
      this.handleMessage(message);
    };
    return this;
  }
  /* cmd 0 连接加授权成功 cmd 1 心跳 */
  sendMessage(data, cmd = CMD.HEART) {
    /* 只有链接成功才能发送消息*/
    if(this.getWsState() !== WS_STATE.OPEN){
      return;
    }
    this.wsInstance.send(JSON.stringify({ cmd, data }));
  }
  /* 关闭ws*/
  close(){
    this.closeState = true;
    /* 手动关闭*/
    this.wsInstance.close();
  }
  /* 获取ws的状态*/
  getWsState(){
    return this.wsInstance.readyState;
  }
  /* 处理消息*/
  handleMessage({ cmd, data, pcVersion }){
    this.optionsCallback("onallmessage", { cmd, data, pcVersion });
    /* 处理服务器心跳 回复*/
    if(CMD.HEART === cmd){
      this.sendHeart(CMD.HEART_REPLY);
    }else if(LOGIN_OUT_STATE.indexOf(cmd) > -1){
      /* 这里做关闭im功能*/
      this.close();
    }else if(!basics.isNull(cmd) && CMD.HEART_REPLY !== cmd && data){
      this.optionsCallback("onmessage", {
        cmd,
        data
      });
    }else{
      console.log("heart reply", data);
    }
  }
  /* 发送心跳*/
  sendHeart(CMD) {
    this.sendMessage("heart check", CMD);
  }
  /* 心跳检测*/
  heartCheck() {
    clearInterval(this.heartTimer);
    this.heartTimer = setInterval(() => {
      console.log("heart check");
      this.sendHeart(CMD.HEART);
    }, this.options.heartTimeout);
  }
  /* 关闭心跳*/
  closeHeart(){
    clearInterval(this.heartTimer);
  }
  /* 重连*/
  reconnect(url) {
    console.log("reconnect", this.closeState);
    /* 手动关闭的不需要重连*/
    if(this.closeState){
      return;
    }
    /* 达到最大重连次数 或者正在重连就禁止重连*/
    if (this.reconnectTimes >= this.options.reconnectMaxTimes || this.reconnectLock) {
      return;
    }
    this.reconnectLock = true;
    clearTimeout(this.reconnectTimer);
    this.reconnectTimer = setTimeout(() => {
      console.log("重连");
      this.connect(url);
      this.reconnectTimes++;
      this.reconnectLock = false;
    }, this.options.reconnectTimeout);
  }
  /* 处理options回调方法*/
  optionsCallback(callbackKey, data){
    this.options[callbackKey] && this.options[callbackKey](data);
    return this;
  }
}


export default WS;
