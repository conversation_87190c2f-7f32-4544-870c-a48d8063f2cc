<template>
  <div class="flex">
    <div class="left-container flex-1">
      <!-- banner -->
       <banner-swiper />
      <!--      常用工具-->
      <commonly-used-tools />
      <!--      数据概览-->
      <data-at-a-glance />
      <!--      抢单排名-->
      <!-- <ranking-of-order-grabbing /> -->
       <!-- 本月线索 -->
       <trend-grabbing-leads-month />
    </div>
    <div class="right-container ">
      <!-- 账号信息 -->
       <account-balance />
      <!--      更新公告-->
      <!-- <workbench-update-announcements /> -->
      <!--      成单喜报-->
      <workbench-good-news />
      <!--      律所动态-->
      <!-- <law-firm-dynamics /> -->
    </div>
  </div>
</template>

<script>
import CommonlyUsedTools from "views/workbenchManagement/workbench/components/CommonlyUsedTools.vue";
import DataAtAGlance from "views/workbenchManagement/workbench/components/DataAtAGlance.vue";
import WorkbenchGoodNews from "views/workbenchManagement/workbench/components/GoodNews.vue";
import BannerSwiper from "views/workbenchManagement/workbench/components/BannerSwiper.vue";
import TrendGrabbingLeadsMonth from "views/workbenchManagement/workbench/components/TrendGrabbingLeadsMonth.vue";
import AccountBalance from "views/workbenchManagement/workbench/components/AccountBlance.vue";

export default {
  name: "Workbench",
  components: {
    // WorkbenchUpdateAnnouncements,
    // LawFirmDynamics,
    WorkbenchGoodNews,
    // RankingOfOrderGrabbing,
    DataAtAGlance,
    CommonlyUsedTools,
    BannerSwiper,
    TrendGrabbingLeadsMonth,
    AccountBalance
  }

};
</script>

<style scoped lang="scss">
.left-container {
}

.right-container {
  margin-left: pxToPer(24);
  width: pxToPer(536);
}
</style>
