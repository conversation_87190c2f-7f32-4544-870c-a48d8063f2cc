<template>
	<div class="px-[4px] h-full bg-[#091627] pt-[24px] relative box-border">
		<div  class="mb-[24px]">
      <img
			class="w-[44px] h-[44px] mx-auto block"
			src="@/assets/images/<EMAIL>"
			alt=""
		/>
		<div class="text-[16px] text-[#FFFFFF] text-center font-bold">{{title}}</div>
    </div>
		<div v-if="isCollapse">
			<function-menu-collapse :data="menusList" />
		</div>
		<div v-else>
			<function-menu-item
				@click="handleClick"
				class="mt-[4px]"
				v-for="item in menusList"
				:data="item"
				:key="item.name"
			/>
<!--      <function-menu-tools-box />-->
		</div>
		<div class="absolute left-0 bottom-[26px] w-full">
			<deposit-entrance>
        <div
          class="mx-auto"
          :class="['recharge-icon left-0 w-full flex cursor', isShowRecharge && 'checked']"
          @click="handlePayShow"
        >
          <div class="mx-auto tc ">
            <i class="iconfont icon-chongzhi" />
            <p class="text-[14px] text-[#FFFFFF] mt-[4px] text-center">充值</p>
          </div>
        </div>
      </deposit-entrance>
      <div v-if="!getIsSpecialCustomerService" class="mx-auto w-full flex flex-col cursor items-center pt-[24px] tc" @click="serviceQR = true">
        <img class="w-[32px] h-[32px] block" src="@/assets/images/header.png" alt="" />
        <p class="text-[14px] text-[#FFFFFF] !mt-[4px] text-center">客服</p>
      </div>
		</div>
    <function-menu-service-q-r v-model="serviceQR" />
<!--		<good-news-update-guide />-->
	</div>
</template>

<script>
import FunctionMenuItem from "components/xy-treeMenu/components/FunctionMenuItem.vue";
import FunctionMenuCollapse from "components/xy-treeMenu/components/FunctionMenuCollapse.vue";
import FunctionMenuServiceQR from "components/xy-treeMenu/components/FunctionMenuServiceQR.vue";
import DepositEntrance from "components/DepositEntrance/index.vue";
import {  getDictValueByOrgDescKey } from "utils/tools";
// import GoodNewsUpdateGuide from "components/xy-treeMenu/components/GoodNewsUpdateGuide.vue";

export default {
  name: "FunctionMenu",
  components: {
    DepositEntrance,
    FunctionMenuServiceQR,
    // GoodNewsUpdateGuide,
    // FunctionMenuToolsBox,
    FunctionMenuCollapse,
    FunctionMenuItem,
  },
  data() {
    return {
      serviceQR: false,
      title: "律客云",
      getIsSpecialCustomerService: false
    };
  },
  computed: {
    menusList() {
      return this.$store.getters.getMenusList;
    },
    isCollapse() {
      return this.$store.getters.getIsCollapsed;
    },
    // 支付弹窗是否显示
    isShowRecharge() {
      return this.$store.getters.isPayPc;
    }
  },
  watch: {
    menusList: {
      handler(val) {
        if (val?.length > 0) {
          this.$emit("selected", val[0]);
        }
      },
      immediate: true,
      deep: true,
    },
  },
  mounted() {
    getDictValueByOrgDescKey({ key: "top" }).then(data => {
      /* 只展示4个字 超出截取*/
      this.title =  data.slice(0, 4);
    });
    getDictValueByOrgDescKey({ key: "showType" }).then(data => {
      this.getIsSpecialCustomerService = data === 1;
    });
  },
  methods: {
    handleClick(item) {
      console.log(item, "[0].children[0].children");

      this.$emit("selected", item);
    },
    handlePayShow() {
      this.$store.commit("SET_IS_PAY_PC", true);
    },
  },
};
</script>

<style scoped lang="scss">
.recharge-icon {
	width: 64px;
	border-radius: 4px 4px 4px 4px;
	left: 0;
	right: 0;
	padding: 8px 0;
	box-sizing: border-box;

	i {
		color: #ffffff;
		font-size: 20px;
	}
}
.checked {
	background: #3887f5;
}
</style>
