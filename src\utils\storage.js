
import basics from "utils/basicsMethods";
const TokenKey = "token";
/* 触发时间*/
const TimeKey = "triggerTime";
/** token **/
export function getToken() {
  return localStorage.getItem(TokenKey);
}

export function setToken(token) {
  return localStorage.setItem(TokenKey, token);
}

export function removeToken() {
  return localStorage.removeItem(TokenKey);
}
export function getImToken() {
  return localStorage.getItem("imToken");
}

export function setImToken(token) {
  return localStorage.setItem("imToken", token);
}

export function removeImToken() {
  return localStorage.removeItem("imToken");
}

export function setStorage(key, val) {
  if (!basics.isNull(val) && typeof val === "object") {
    sessionStorage.setItem(key, JSON.stringify(val));
  } else {
    sessionStorage.setItem(key, val);
  }
}

export function getStorage(key) {
  return JSON.parse(sessionStorage.getItem(key));
}

export function removeStorage(key) {
  return sessionStorage.removeItem(key);
}

export function clearStorage() {
  return sessionStorage.clear();
}

export function setTriggerTime(key, val) {
  const data = getTriggerTime(TimeKey);
  data[key] = val;
  localStorage.setItem(TimeKey, JSON.stringify(data));
}
export function getTriggerTime() {
  let data = {};
  try {
    data = JSON.parse(localStorage.getItem(TimeKey))||{};
  } catch (error) {
    data = {};
  }
  return data;
}

export function getTriggerTimeByKey(key) {
  const data = getTriggerTime();
  return data[key] || "";
}

export function removeTriggerTime(key = "") {
  if(basics.isNull(key)){
    return localStorage.removeItem(TimeKey);
  }
  const data = getTriggerTime();
  delete data[key];
  localStorage.setItem(TimeKey, JSON.stringify(data));
}
