<template>
	<div class="bg-[#FFFFFF]">
		<div class="px-[24px] pt-[24px]">
      <certification-banner />
			<app-select
				:select-list="goodAtType"
				@change="handleSelect"
         type-way="multiple"
				:default-value="query.typeValues"
			/>
		</div>
		<div class="px-[24px] pt-[16px] pb-[24px]">
			<xy-page
				:is-margin="false"
				ref="page"
        :default-loading-page="false"
				:column="tableColumn"
				:request="request"
				:query="query"
        :show-head-screen="true"
			>
				<template #pageMiddleContainer>
            <div class="flex">
              <lead-package-display>
                <div class="text-[14px] text-[#333333] mb-[16px]  pr-[40px]">
                  律所剩余可抢案源：<span class="text-[#3887F5]">{{
                    remainingGrabTimes.remainCaseCount
                  }}</span
                >条
                </div>
              </lead-package-display>
              <div v-if="!basics.isObjNull(expire)" class="text-[14px] text-[#333333] mb-[16px]  pr-[40px]">
                <span class="text-[#3887F5]">{{expire.remainCaseSourceGrabbingCount}}</span>条将在<span class="text-[#EB4738]">{{expire.remainDay}}</span>天过期
              </div>
              <div class="text-[14px] text-[#333333] mb-[16px]">
                剩余可使用法临币：<span class="text-[#3887F5]">{{
                priceNumber(~~remainingGrabTimes.remainAmount + ~~remainingGrabTimes.giftRemainAmount)
                }}</span
              >个
              </div>
            </div>
				</template>
				<template #handleButton>
					<el-table-column width="146px" fixed="right" label="操作">
						<template #default="{ row }">
							<div v-if="row.way === 1" class="text-[14px] text-[#999999]">该案源已被独享</div>
							<div v-else class="flex">
								<app-button
									v-if="row.status !== 3"
									style-type="text"
									type="primary"
									size="medium"
									@click="
										handleGrabOrder({
											id: row.caseSourceV2Id,
											amount: row.serverAmount,
											isCaseSource: true
										})
									"
									>抢单
								</app-button>
								<app-button
									v-if="row.status === 1"
									style-type="text"
									type="success"
									size="medium"
									@click="
										handleGrabOrder({
											id: row.caseSourceV2Id,
											way: 1,
											amount: row.ykjAmount,
											isCaseSource: true
										})
									"
									>独享
								</app-button>
                <app-button
                  v-if="row.way === 1"
                  style-type="text"
                  type="info"
                  class="!pr-[0]"
                  disabled
                  size="medium"
                >该案源已被独享
                </app-button>
							</div>
						</template>
					</el-table-column>
				</template>
			</xy-page>
		</div>
		<app-popup
			@confirm="grabOrder"
			title="温馨提示"
			show-cancel
			width="400px"
      top="40vh"
			:visible.sync="isShowDeductNum"
		>
			<div>
				<div class="ml-[8px] py-[16px] px-[24px] text-[14px] text-[#666666] flex items-center">
					<i
						class="text-[24px] w-[24px] h-[24px] mr-[8px] iconfont icon-xianxingtubiao-3 text-[#ff851f]"
					/>
					独享抢单将扣除{{ deductNum }}次权益哦~
				</div>
			</div>
		</app-popup>
		<app-popup
			@confirm="grabOrder"
			title="温馨提示"
			show-cancel
			width="400px"
			:visible.sync="isShowGrab"
			confirm-text="继续抢"
      top="40vh"
		>
			<div>
				<div class="ml-[8px] py-[16px] px-[24px] text-[14px] text-[#666666] flex items-center">
					<i
						class="text-[24px] w-[24px] h-[24px] mr-[8px] iconfont icon-xianxingtubiao-3 text-[#ff851f]"
					/>
					{{ showGrabMessage }}
				</div>
			</div>
		</app-popup>

    <!--  1.0.9抢单流程  -->
    <grab-order-pop :remaining-grab-times="remainingGrabTimes" :show.sync="dialogVisible" :detail="currentParams" @confirmPop="submitPop" />
	</div>
</template>

<script>
import XyPage from "components/xy-page/index.vue";
import { tableColumn } from "./index.js";
import AppSelect from "components/AppSelect/index.vue";
import AppButton from "components/appButton/index.vue";
import { caseGrabV3, caseRemainTimes, caseSourceClues, lcLawFirmsInstitutionOrderTimeOutOrder } from "@/api/clues";
import { commonConfigKey, dataDetailList, lcNewCaseSourceClueCount } from "@/api/common";
import AppPopup from "components/appPopup/index.vue";
import refreshTheDatatipPeriodically from "@/minixs/refreshTheDatatipPeriodically.js";
import LeadPackageDisplay from "components/leadPackageDisplay/index.vue";
import { priceNumber } from "../../../../utils/toolMethods";
import GrabOrderPop from "views/leadManagement/clueSquare/cluesToTheSourceOfTheCase/grabOrderPop.vue";
import handleGrabOrder from "@/minixs/handleGrabOrder";
import CertificationBanner from "views/leadManagement/clueSquare/cluesToTheSourceOfTheCase/certificationBanner.vue";


export default {
  name: "CluesToTheSourceOfTheCase",
  components: { CertificationBanner, GrabOrderPop, LeadPackageDisplay, AppPopup, AppButton, AppSelect, XyPage },
  mixins: [refreshTheDatatipPeriodically({
    updateDataApi: lcNewCaseSourceClueCount,
    msg: "线索广场更新num条新数据，请立即刷新查看",
    key: "newCaseSourceCount" }), handleGrabOrder],
  data() {
    return {
      request: {
        getListUrl: data => this._updateData(caseSourceClues, data),
      },
      tableColumn,
      query: {
        typeValues: [""],
      },
      /** 剩余可抢单次数 */
      remainingGrabTimes: {},
      /** 擅长类型 */
      goodAtType: [],
      /** 扣除次数 */
      deductNum: 0,
      /** 显示是否被同律所抢单 */
      isShowGrab: false,
      /** 显示扣除次数弹窗 */
      isShowDeductNum: false,
      /** 当前点击 */
      currentParams: {},
      /** 显示是否被同律所抢单提示信息 */
      showGrabMessage: "",
      dialogVisible: false,
      /* 剩余过期条数*/
      expire: {
        /* remainCaseSourceGrabbingCount   案源剩余可抢次数
remainDay  剩余天数*/
      },
    };
  },
  created() {
    this.getGoodAtType();
    this.getCacheData();
  },
  activated() {
    this.getDeductNum();
    this.getRemainingGrabTimes();
  },
  methods: {
    priceNumber,
    /** 获取扣除次数 */
    getDeductNum() {
      commonConfigKey({
        paramName: "lc_grab_case_deduct_num",
      }).then(res => {
        this.deductNum = res;
      });
    },
    /** 查询可抢剩余次数 */
    getRemainingGrabTimes() {
      lcLawFirmsInstitutionOrderTimeOutOrder().then(res => {
        this.expire = res || {};
      });
      caseRemainTimes().then(res => {
        this.remainingGrabTimes = res;
        console.log( this.remainingGrabTimes, 66);
      });
    },
    /** 确定选中的支付方式 1 线索包 3 赠送 2 充值  **/
    submitPop(item){
      this.currentParams.accountType = Number(item);
      this.submitGrabOrder();
    },
    /** 点击抢单 */

    submitGrabOrder(){
      // // 如果是独享
      // if (this.currentParams.way === 1) {
      //   // this.isShowDeductNum = true;
      //   this.grabOrder();
      //   return;
      // }

      // caseGrabCheck({ id: this.currentParams.id }).then(res => {
      //   if (res.code === 1) {
      //     this.isShowGrab = true;
      //     this.showGrabMessage = res.message;
      //   } else {
      //     this.grabOrder();
      //   }
      // });

      this.grabOrder();
    },
    /** 抢单 */
    grabOrder() {
      caseGrabV3(this.currentParams).then((res) => {
        console.log(res, 999);

        if(res.code === 1){
          // 弹出已经被抢单的弹框
          this.isShowGrab = true;
          this.currentParams.confirmId = res.confirmId;
          this.showGrabMessage = res.message;
          return;
        }else{
          this.$message.success("抢单成功，快去跟进吧~");
          this.getRemainingGrabTimes();
          this.$store.dispatch("setIsTableRefresh");
          this.isShowDeductNum = false;
          this.currentParams.confirmId = null;
          this.isShowGrab = false;
        }
      });
    },
    /** 获取擅长类型 */
    getGoodAtType() {
      dataDetailList({
        groupCode: "LAWYER_SPECIALITY",
      }).then(res => {
        this.goodAtType = res;

        this.goodAtType.unshift({
          label: "全部",
          value: "",
        });
      });
    },
    /** 选择了擅长类型 */
    handleSelect(selectList) {
      const itemStr = selectList.map(item => item.value);
      this.query.typeValues = itemStr;
      this.$store.dispatch("setIsTableRefresh");
    },
    /** 获取缓存的数据 */
    getCacheData() {
      const cacheKey = `xy-table-search:${this.$route.fullPath}`;
      const cacheData = localStorage.getItem(cacheKey);
      if (cacheData) {
        this.query.typeValues = JSON.parse(cacheData).typeValues || [""];
      }
      this.$nextTick(() => {
        this.$store.dispatch("setIsTableRefresh");
      });
    },
  },
};
</script>

<style scoped lang="scss"></style>
