<template>
  <app-popup hide-footer top="30vh" width="520px" class="newcomer-onboarding-notification" :visible.sync="visible">
    <div class="noc-co px-[32px] pb-[36px] relative">
      <i @click="close" class="cursor-pointer absolute iconfont icon-xianxingtubiao-13 text-[24px] top-[16px] right-[24px] text-[#666666]" />
      <p class="font-bold text-[24px] text-[#222222] !pt-[36px]">新人引导通知</p>
      <p class="text-[14px] text-[#222222] w-[266px] !pt-[8px] !pb-[32px]">
        为了更好地了解及使用律客云系统，您可前往新人引导了解律客云使用流程哦~
      </p>
      <app-button type="publicize" @click="handleTo">
        立即前往
      </app-button>
    </div>
  </app-popup>
</template>

<script>
import AppPopup from "components/appPopup/index.vue";
import AppButton from "components/appButton/index.vue";

export default {
  name: "NewcomerOnboardingNotification",
  components: { AppButton, AppPopup },
  computed: {
    visible: {
      get() {
        return this.$store.getters["getFirstLoginGuide"];
      },
      set(val) {
        this.$store.commit("SET_FIRST_LOGIN_GUIDE", val);
      },
    },
  },
  methods: {
    handleTo(){
      this.$store.commit("SET_FIRST_LOGIN_GUIDE", false);
      this.$store.commit("SET_GUIDE", true);
    },
    close(){
      this.$store.commit("SET_FIRST_LOGIN_GUIDE", false);
    }
  }
};
</script>

<style scoped lang="scss">
.newcomer-onboarding-notification{
  .noc-co{
    background: url("~@/assets/images/<EMAIL>");
    background-size: 100% 100%;
    border-radius: 8px;
    overflow: hidden;
  }
  ::v-deep{
    .app-popup_body{
      display: none;
    }
    .app-popup_class{
      background: none;
    }
  }
}
</style>
<style  lang="scss">
.newcomer-onboarding-notification{

  .app-popup_class{
    background: none;
    box-shadow: none;
  }
}
</style>
