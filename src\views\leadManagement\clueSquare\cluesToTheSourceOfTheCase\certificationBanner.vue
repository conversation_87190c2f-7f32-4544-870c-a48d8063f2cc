<template>
	<!--       认证宣传      -->
	<div
    v-if="!$store.getters['lawyerCertStatus']&&show"
		class="certification relative bg-[linear-gradient(_94deg,_#F2F9FE_0%,_#BDE0FC_100%)]  pl-[40px] mb-[24px] h-[64px] flex items-center pr-[80px]"
	>
		<div class="flex h-full items-end">
			<img
				class="w-[100px]"
				src="@/views/leadManagement/clueSquare/cluesToTheSourceOfTheCase/imgs/<EMAIL>"
				alt=""
			/>
		</div>
		<p class="pl-[22px] text-ellipsis flex-1 font-bold text-[20px] text-[#3887F5]">
			完成实名认证，提升接案成功率，抢占优质订单先机！
		</p>
    <app-button v-if="$store.getters.getCertStatus===3" @click="turnToLawyerAuth">审核失败 重新认证</app-button>
		<app-button v-else class="flex-shrink-0" @click="turnToLawyerAuth">立即认证</app-button>
		<i @click.stop="handleClose" class="!text-[16px] cursor-pointer iconfont icon-guanbi absolute top-[16px] right-[16px]" />
	</div>
</template>

<script>
import AppButton from "components/appButton/index.vue";
import { turnToLawyerAuth } from "utils/turnPage";

export default {
  name: "CertificationBanner",
  components: { AppButton },
  data() {
    return {
      show: false,
    };
  },
  created() {
    this.getBeforeCloseTime();
  },
  methods: {
    turnToLawyerAuth,
    getBeforeCloseTime() {
      const time = localStorage.getItem("CertificationBannerTime");

      if (time) {
        // 于上次关闭时间是否差了一个自然日
        this.show = false;
        return;
      }

      this.show = true;
    },
    handleClose() {
      localStorage.setItem("CertificationBannerTime", "true");

      this.show = false;
    }
  },
};
</script>

<style scoped lang="scss"></style>
