<template>
  <div class="case-box">
    <div class="h-[70px] flex items-center justify-between">
      <div class="flex items-center">
        <p class="!pl-[12px] title font-bold text-[16px] !pr-[16px] text-[#333333]">案源自动推荐计划</p>
        <el-switch
          v-model="status"
          :inactive-text="inactiveText"
          @change="handleSwitch"
          :active-text="activeText" />
      </div>
      <div class="flex items-center" v-if="status">
        <el-tooltip :visible-arrow="false" popper-class="p-[0] !bg-[rgba(0,0,0,0.7)] rounded-[8px] px-[24px] pt-[16px] w-[365px] box-border" placement="bottom-end">
          <div slot="content">
            <div class="flex pb-[16px]" v-for="i in tipTextList" :key="i.id">
              <p class="text-[14px] text-[#DDDDDD] w-[98px] text-right flex-shrink-0">{{i.label}}</p>
              <p class="text-[14px] text-[#FFFFFF]">{{ i.text }}</p>
            </div>
          </div>
          <div class="flex items-center cursor-pointer text-[14px] text-[#999999]">
            <i class="iconfont icon-liulan pr-[4px]" />
            查看
          </div>
        </el-tooltip>

        <div class="pl-[24px] flex items-center cursor-pointer text-[14px] text-[#3887F5]" @click="stepRevise()">
          <i class="iconfont icon-xianxingtubiao-16 pr-[4px]" />
          修改
        </div>
      </div>
    </div>
    <!--    <div class="desc-box">
          <p><label>当事人所在地：</label> <span>{{locationText.length ? locationText.toString()  : '未选择'}}</span> <el-button @click="handleLocation" type="text">修改</el-button> </p>
          <p><label>涉案金额：</label> <span>{{caseGradeText}}</span> <el-button @click="handleAmount" type="text">修改</el-button> </p>
          <p><label>案件类型：</label> <span>{{caseTypeText}}</span> <el-button @click="handleCase" type="text">修改</el-button> </p>
        </div>-->

    <app-popup hide-footer  width="560px" title="案源自动推荐计划配置" @close="handleClose" :visible.sync="planConfigState">
      <div class="p-[24px]">
        <div class="bg-[#F5F5F7] rounded-[8px] py-[16px]">
          <app-steps :active="stepActive" :step-list="stepList" />
        </div>
      </div>
      <div class="h-[388px]" v-if="planConfigState">
        <div v-for="(i,index) in stepList" :key="index">
          <component ref="stepList"  v-show="(stepActive-1)===index" :is="i.componentName" v-bind="{detail:detail,title:i.title,...(i.componentOptions?i.componentOptions:{})}" />
        </div>
      </div>
      <div class="flex items-center border-0 border-solid border-t-[1px] border-[#EEEEEE]  justify-end pt-[8px] pb-[24px] px-[24px]">
        <app-button  size="large" type="info" v-show="stepActive>1" @click="previousPlanConfig">上一步</app-button>
        <app-button class="ml-[16px]"  v-show="stepActive<stepList.length" size="large" @click="nextPlanConfig">下一步</app-button>
        <app-button class="ml-[16px]"   v-show="stepActive>=stepList.length" size="large" @click="finishPlanConfig">完成</app-button>
      </div>
    </app-popup>

  </div>
</template>

<script>
import {
  caseSourceCluesPushDetail,
  caseSourceCluesPushDetailSwitchLcClue,
  caseSourceCluesPushDetailUpdate
} from "@/api/clues";
import CaseLocation from "views/systemAdministration/caseSourcePush/components/caseLocation.vue";
import CaseTypes from "views/systemAdministration/caseSourcePush/components/caseTypes.vue";
import { dataDetailList } from "@/api/common";
import AppPopup from "components/appPopup/index.vue";
import AppSteps from "components/appSteps/index.vue";
import AppButton from "components/appButton/index.vue";
import CaseKey from "views/systemAdministration/caseSourcePush/components/caseKeys.vue";

export default {
  name: "CaseUpdate",
  components: { AppButton, AppSteps, AppPopup, CaseTypes, CaseLocation, CaseKey },
  data() {
    return {
      stepActive: 1,
      status: false,
      showTip: localStorage.getItem("CASEPSHSTATUS") ? !!~~(localStorage.getItem("CASEPSHSTATUS")) : true,
      /** 选中公共值 */
      checkList: [],
      maxCheckedNum: 10,
      /** 案件类型 */
      goodAtType: [],
      /** 选中案件类型的值 **/
      goodAtTypeCheckedVal: [],
      /** 选中案件类型的文字 **/
      checkListText: [],
      /** 涉案金额 **/
      amountList: [],
      /** 选中涉案金额 **/
      amountListVal: [],
      /** 涉案金额 **/
      amountTextList: [],
      /** 详情id **/
      detailId: null,
      detail: {},
      textDefault: [
        {
          typeLabel: "不限",
          typeValue: "-1",
        }
      ],
      /* 案源自动推荐计划配置弹窗状态*/
      planConfigState: false,
      /* 0表示是点击开关弹起的 1表示直接点修改弹起的*/
      stepReviseState: 1
    };
  },
  computed: {
    stepList(){
      return  [{
        title: "当事人所在地",
        state: false,
        componentName: "CaseLocation"
      }, {
        title: "涉案金额",
        state: false,
        componentName: "CaseTypes",
        componentOptions: {
          popupType: "amount",
          list: this.amountList
        }
      }, {
        title: "案件类型",
        state: false,
        componentName: "CaseTypes",
        componentOptions: {
          popupType: "caseType",
          list: this.goodAtType
        }
      }, {
        title: "关键词推送",
        state: false,
        componentName: "CaseKey",
      }, ];
    },
    /** 涉案金额 **/
    caseGradeText() {
      let amount = [];
      this.amountList.forEach(a => {
        if (this.detail.amountGrades && this.detail.amountGrades.includes(Number(a.value))) {
          amount.push({
            typeValue: a.value,
            typeLabel: a.label,
          });
        }
      });
      const amountTextList = Number(this.detail.amountGradeLimit) === 1 ? this.textDefault : (amount || []);

      return (amountTextList.map(t => {
        return t.typeLabel;
      })).toString() || "未选择";
    },
    /** 案件类型 **/
    caseTypeText() {
      // 案件类型
      const checkListText = Number(this.detail.caseTypeLimit) === 1 ? this.textDefault : (this.detail.caseTypes || []);
      return (checkListText.map(t => {
        return t.typeLabel;
      })).toString() || "未选择";
    },
    /** 地区 **/
    locationText() {
      return this.detail.cityV2Lists ? this.detail.cityV2Lists.map(s => {
        return s.regionName;
      }) : [];
    },
    /* 提示文案的list*/
    tipTextList(){
      return [{
        label: "当事人所在地：",
        text: this.locationText.length ? this.locationText.toString() : "未选择",
        id: 1
      }, {
        label: "涉案金额：",
        text: this.caseGradeText,
        id: 2
      }, {
        label: "案件类型：",
        text: this.caseTypeText,
        id: 3
      }, {
        label: "关键词：",
        text: this.detail.keyWords ? this.detail.keyWords.toString() : "未填写",
        id: 4
      },
      ];
    },
    inactiveText(){
      return !this.status ? "关" : " ";
    },
    activeText(){
      return !this.status ? " " : "开";
    },
    /* 是不是点击开关唤起的弹窗*/
    isSwitchPopup(){
      return this.stepReviseState === 0;
    }
  },
  created() {
    setTimeout(() => {
      this.showTip = false;
      localStorage.setItem("CASEPSHSTATUS", 0);
    }, 3000);
    this.getAmount();
    this.getGoodAtType();
  },
  mounted() {
    this.getDetail();
  },
  methods: {
    getDetail() {
      return caseSourceCluesPushDetail().then(r => {
        console.log(r);
        this.detail = r;
        this.detailId = r?.id;
        this.status = !!r.status || false;
        // 当开关打开的时候不弹粗提示
        if (this.status) {
          this.showTip = false;
          localStorage.setItem("CASEPSHSTATUS", 0);
        }
        return r;
      });
    },
    /** switch打开开关 */
    handleSwitch(val) {
      /* 开启的时候并且之前没有设置过配置 不调用接口 配置完成后调用*/
      if(val && (this.basics.isArray(this.locationText) && this.locationText.length < 1 )){
        /* 打开配置*/
        this.stepRevise(0);
      }else{
        /* 关闭在调用*/
        this.turnCaseSourcePushOnOrOff();
      }
    },
    /* 开启或者关闭案源推送*/
    turnCaseSourcePushOnOrOff(){
      const val = this.status;
      const data = {
        status: ~~val,
      };
      return  caseSourceCluesPushDetailSwitchLcClue(data).then((res) => {
        this.$message.success(data.status ? "开启成功，系统将按照你设置的案源条件为您推送案源" : "关闭成功，系统将停止推送案源");
        this.getDetail();
        return res;
      }).catch(r => {
        if (r.data.code !== 200) {
          this.status = !this.status;
        }
        console.log(r, "错误");
        return Promise.reject(r);
      });
    },
    getAmount() {
      dataDetailList({
        groupCode: "AMOUNT_GRADE"
      }).then(res => {
        this.amountList = res;

        this.amountList.unshift({
          label: "不限",
          value: "-1",
        });
        console.log(this.amountList, 8989);
      });

    },
    getGoodAtType() {
      dataDetailList({
        groupCode: "LAWYER_SPECIALITY",
      }).then(res => {
        this.goodAtType = res;

        this.goodAtType.unshift({
          label: "不限",
          value: "-1",
        });
      });

    },
    /* 修改配置点击 state 0表示是点击开关弹起的 1表示直接点修改弹起的*/
    stepRevise(state = 1){
      this.stepReviseState = state;
      this.planConfigState = true;
      this.stepActive = this.$options.data().stepActive;
    },
    /* 获取配置对应进度是否需要验证*/
    getStepVerify(){
      /* 是否需要验证 验证只是判空而已 只有下一步才需要验证*/
      const index = this.stepActive;
      /* 获取需要验证的组件 然后调用组件内统一的判空验证*/
      const component = this.$refs.stepList[index - 1];
      if (component && component.getVerify) {
        return component.getVerify();
      }
      return true;
    },
    /* 案源自动推荐计划配置上一步*/
    previousPlanConfig(){
      this.stepActive--;
    },
    /* 案源自动推荐计划配置下一步*/
    nextPlanConfig(){
      if( this.getStepVerify()){
        this.stepActive++;
      }
    },
    /* 弹窗点击关闭事件*/
    handleClose(){
      if(this.isSwitchPopup){
        this.status = false;
      }
    },
    /* 案源自动推荐计划配置完成*/
    async finishPlanConfig(){
      /* 最后一个进度的验证*/
      if(this.getStepVerify()){
        let data  = {};
        /* 获取每一个 组件里面统一方法 返回保存数据  caseSure是个方法，是确定每个组件最后输出确认的数据*/
        this.$refs.stepList.forEach(r => {
          /* 组件内的获取数据方法*/
          data = { ...data,  ...r.caseSure()  };
          console.log(data);

        });



        /* 只有点击开关唤醒的才去请求开关状态接口*/
        try {
          // if(this.isSwitchPopup){
          //   await this.turnCaseSourcePushOnOrOff();
          // }
          await caseSourceCluesPushDetailUpdate(data);
          this.planConfigState = false;

          if(!this.isSwitchPopup){
            this.getDetail();
            this.$message.success("操作成功");
          }else {
            await this.turnCaseSourcePushOnOrOff();
          }
        }catch (e) {
          console.log(e);
        }
      }
    }
  }
};
</script>

<style lang="scss" scoped>
.case-box {
  ::v-deep .el-switch .el-switch__core {
    background-color: #CCCCCC !important;
  }

  ::v-deep .el-switch.is-checked .el-switch__core {
    background-color: #3887F5 !important;
  }

  ::v-deep .el-switch__core:after {
    z-index: 999 !important;
  }

  ::v-deep .el-switch__label--right {
    position: absolute;
    left: -4px;
    color: #FFFFFF;
    z-index: 1 !important;
    font-size: 11px;
  }

  ::v-deep .el-switch__label--left {
    position: absolute;
    left: 21px;
    color: #FFFFFF;
    z-index: 1;
    font-size: 11px;
  }

  ::v-deep .el-switch__label * {
    font-size: 12px;
  }

  .title {
    position: relative;

    &:after {
      position: absolute;
      content: "";
      width: 4px;
      height: 16px;
      background: #3887F5;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
    }
  }
}
</style>
