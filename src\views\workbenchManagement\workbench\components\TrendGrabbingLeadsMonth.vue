<template>
  <div class="mt-[16px] min-h-[362px] bg-[#FFFFFF] rounded-[2px]">
    <h2>本月抢线索趋势</h2>
    <div class="box">
        <the-total-distribution-of-leads-echarts :api="privateGrabClueStatistics" ref="echarts1" />
    </div>
  </div>
</template>

<script>
import { privateGrabClueStatistics } from "@/api/workbenchManagement.js";
import TheTotalDistributionOfLeadsEcharts
  from "views/leadManagement/statistics/leadAnalysis/components/theTotalDistributionOfLeadsEcharts.vue";

export default {
  name: "TrendGrabbingLeadsMonth",
  components: {
    TheTotalDistributionOfLeadsEcharts
  },
  methods: {
    privateGrabClueStatistics
  },
};
</script>

<style scoped lang="scss">
.table-c{
  position: relative;
  &:after {
    position: absolute;
    right: 0;
    top: 0;
    content: " ";
    border-right: 1px dashed #DDDDDD;
    height: 100%;
  }
  &:last-of-type {
    &:after {
      display: none;
    }
  }
}

h2{
  font-weight: bold;
  font-size: 16px;
  color: #333333;
  padding: 15px 23px;
  box-shadow: inset 0px -1px 0px 0px #EEEEEE;
}
.box{
  width: 100%;
}
</style>
