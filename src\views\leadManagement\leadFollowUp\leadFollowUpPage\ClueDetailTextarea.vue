<template>
	<div class="relative">
		<el-input
			type="textarea"
			:autosize="{ minRows: 2, maxRows: 6 }"
			placeholder="请输入跟进记录"
			maxlength="500"
			show-word-limit
			v-model="text"
		/>
	</div>
</template>

<script>

export default {
  name: "ClueDetailTextarea",
  props: {
    input: {
      type: String,
      default: "",
      required: true,
    },
    /** 标签的值 */
    labelValues: {
      type: String,
      default: "",
    },
    /** 可以添加的标签 */
    tagsList: {
      type: Array,
      default: () => [],
    },
  },
  computed: {
    text: {
      get() {
        return this.input;
      },
      set(val) {
        this.$emit("update:input", val);
      },
    },
  },
};
</script>

<style scoped lang="scss">
::v-deep .el-textarea {
	.el-textarea__inner {
		height: 100px !important;
	}
}
</style>
