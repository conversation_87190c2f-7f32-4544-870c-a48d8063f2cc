<template>
	<div class="flex h-full">
		<function-menu @selected="handleClick" />
		<div
      v-show="isShowMenu"
			class="bg-[#FFFFFF] overflow-hidden duration-500 transition-all"
			:class="[isCollapse ? 'w-0' : 'w-[180px]']"
		>
			<el-menu
				class="xy-menu"
				:default-active="$route.path"
				:show-timeout="200"
				mode="vertical"
				:unique-opened="true"
				:background-color="defaultColors.menuBgColor"
				:text-color="defaultColors.menuFontColor"
				:active-text-color="defaultColors.menuActiveFontColor"
			>
				<sidebar-item
					v-for="(route) in secondRoutes"
					:key="route.path"
					:item="route"
					:base-path="route.path"
				/>
			</el-menu>
		</div>
	</div>
</template>

<script>
import defaultColors from "@/assets/css/var.scss";
import sidebarItem from "components/xy-treeMenu/sidebarItem";
import FunctionMenu from "components/xy-treeMenu/components/FunctionMenu.vue";
import basics from "utils/basicsMethods";

export default {
  name: "XyTreeMenu",
  components: {
    FunctionMenu,
    sidebarItem,
  },
  data() {
    return {
      /** 二级路由 */
      secondRoutes: [],
    };
  },
  computed: {
    isCollapse() {
      return this.$store.getters.getIsCollapsed;
    },
    defaultColors() {
      return defaultColors;
    },
    /* 只有一个子级 或者没的子级就不展示*/
    isShowMenu(){
      const secondRoutes = this.secondRoutes || [];
      return !basics.isArrNull(secondRoutes);
    }
  },
  methods: {
    handleClick(item) {
      this.secondRoutes = item.children;
    },
  },
};
</script>

<style lang="scss" scoped>
.xy-menu:not(.el-menu--collapse) {
	width: 180px;
}
.menu-logo {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 167px;
	img {
		width: 80px;
		height: auto;
	}
	&.small {
		padding: 0 10px;
		img {
			width: 52px;
		}
	}
}
.fade-enter-active,
.fade-leave-active {
	transition: opacity 0.5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
	opacity: 0;
}
</style>
