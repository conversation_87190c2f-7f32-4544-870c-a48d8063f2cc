<template>
  <div class="wrap">
    <div class="wrap-tit flex items-center">
      <p class="!pl-[12px] title font-bold text-[16px] !pr-[16px] text-[#333333]">默认支付方式</p>
      <app-switch
        v-model="status"
        @change="handleSwitch" />
    </div>
    <div  class="texts">
      <p>1、开启默认支付设置，则手动抢单不再二次弹窗确认扣款方式，且自动抢案源计划也将按照默认支付进行自动扣款；</p>
      <p>2、关闭默认支付设置，则手动抢单将会二次弹窗确认扣款方式，且自动抢案源计划无法生效；</p>
    </div>
    <div v-if="status" class="texts-lists">
      <el-checkbox-group class="select-box" v-model="way" @change="handleCheckboxChange">
        <el-checkbox class="block"  v-if="isLeadPackageDisplay()" :label="1"> 线索包支付 </el-checkbox>
        <el-checkbox class="block" :label="3">赠送余额支付</el-checkbox>
        <el-checkbox class="block" :label="2">充值余额支付</el-checkbox>
      </el-checkbox-group>
      <div class="button">
        <el-button class="block"  @click="handleSubmit()"  type="primary">保存</el-button>
      </div>
    </div>


  </div>
</template>

<script>
import { lcInstitutionStaffGetPayWay, lcInstitutionStaffUpdatePayWay } from "@/api/clues";
import { isLeadPackageDisplay } from "components/leadPackageDisplay";
import AppSwitch from "@/components/appSwitch";

export default {
  name: "DefaultCaseGrabOrder",
  components: {
    AppSwitch
  },
  data(){
    return{
      status: false,
      isEdit: false,
      way: [] // 默认支付方式
    };
  },
  activated() {
    lcInstitutionStaffGetPayWay().then(s => {
      this.way = [s.accountType];
      this.status = !!s.accountType;
    });
  },
  methods: {
    isLeadPackageDisplay,
    /** switch打开开关 */
    handleSwitch(val) {
      if(!val){
        this.way = [];
        this.handleSubmit("关闭");
      }
    },
    // 修改
    handleEdit(){
      this.isEdit = true;
    },
    // 只获取1个值
    handleCheckboxChange(val) {
      this.way = val.length ? [val[val.length - 1]] : [];
    },
    // 确定
    handleSubmit(val){
      if(this.way.length < 1 && !val) return this.$message.warning("请选择支付方式!");
      lcInstitutionStaffUpdatePayWay({ accountType: this.status ? this.way[0] : 0 }).then(s => {
        this.$message.success("操作成功");
        this.isEdit = false;
      });
    },

  }
};
</script>

<style lang="scss" scoped>
.wrap{
  background-color: #FFFFFF;
  height: calc(100vh - 180px);
  padding: 24px;



  .wrap-tit{
    button{
      margin-left: 56px;
    }
  }
}
.texts{
  border-radius: 4px 4px 4px 4px;
 font-weight: 400;
  font-size: 12px;
  color: #999999;
  margin-top: 16px;
}
.texts-lists{
  // border: 1px solid #EEEEEE;
  padding: 10px 0;
  margin-top: 24px;
  // height: (calc(100% - 110px));
  position: relative;
  .select-box{
    padding: 0 24px;
  }

  .block{
    margin-bottom: 28px;


    &:last-child{
      margin-bottom: 0;
    }
  }

  .button{
    margin-top: 28px;
    padding-top: 8px;
    height: 64px;
    background: #FFFFFF;
    // box-shadow: inset 0px 1px 0px 0px #EEEEEE;
    border-radius: 0px 0px 0px 0px;
    // position: absolute;
    bottom: 20px;
    left: 0;
    right: 0;
    button{
      margin-left: 24px;
    }
  }
}
.title {
  position: relative;

  &:after {
    position: absolute;
    content: "";
    width: 4px;
    height: 16px;
    background: #3887F5;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
  }
}
</style>
