/* 延迟点击*/
export const delayClick = (fn, delay = 500) => {
  let timer = null;
  let state = false;
  const click = function () {
    if (state) return;
    state = true;
    fn.apply(this, arguments);
    timer = setTimeout(() => {
      state = false;
    }, delay);
  };
  return {
    click,
    cancel() {
      clearTimeout(timer);
      state = false;
    }
  };
};


export const delayClickMixins = () => {
  return {
    props: {
      /* 按钮延迟时间*/
      delay: {
        type: Number,
        default: 1000
      },
      /* 是否开启延迟*/
      delayClick: {
        type: Boolean,
        default: false
      }
    },
    created() {
      if(this.delayClick){
        const { click, cancel } = delayClick((callback) => {
          callback();
        }, this.delay);
        this.delayClickCallback = click;
        this.$on("hook:destroyed", cancel);
        /* 监听keep-alive缓存的销毁生命周期*/
        this.$on("hook:deactivated", cancel);
      }
    },
    methods: {
      /* 执行delayClickCallback*/
      delayClickCallback(){},
      /* 包装delayClickCallback*/
      delayClickCallbackWrapper(callback){
        if(this.delayClick){
          this.delayClickCallback(callback);
          return;
        }
        callback();
      }
    }
  };
};

/* 指令式延迟*/
export const delayClickDirective = (delay = 1000) => {
  return{
    delayClickDirective: {
      bind(el, { arg = "click", value }, vnode){
        const vm = vnode.context;
        const { click, cancel } = delayClick((e) => {
          value(e);
        }, delay);
        el._cancelClick = () => {
          cancel();
        };
        el._addEventListenerFn = (e) => {
          click(e);
        };
        el.addEventListener(arg, el._addEventListenerFn);
        // 监听组件的 deactivated 钩子
        if (vm) {
          vm.$on("hook:deactivated", el._cancelClick);
        }
      },
      unbind(el, { arg = "click" }, vnode){
        const vm = vnode.context;
        el._cancelClick && el._cancelClick();
        if(el._addEventListenerFn){
          el.removeEventListener(arg, el._addEventListenerFn);
        }
        // 在指令解绑时，务必移除监听器
        if (vm && el._cancelClick) {
          vm.$off("hook:deactivated", el._cancelClick);
          delete el._cancelClick; // 从 el 上移除处理器
        }

      }
    }
  };
};
