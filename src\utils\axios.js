import axios from "axios";
import CryptoJS from "crypto-js";
import { Message } from "element-ui";
import { getToken } from "utils/storage";
import store from "store";
import router from "router";
import { wsClose } from "utils/creat-ws.js";
import { axiosBaseHeadersConfig } from "utils/config";

let isShow401 = false;


/* 请求参数*/
const requestConfig = {
  key: "f47ac10b-58cc-4372-a567-0e02b2c3d479",
  secret: "LcK6Jd0lI9QaE7vB",
  iv: "cdkjytgsrj754921",
};

/* MD5加密*/
const getMD5 = rawStr => {
  // encrypt
  return CryptoJS.MD5(rawStr);
};

/* 加密*/
export const cryptoEncrypt = word => {
  let key = requestConfig.secret;
  let iv = requestConfig.iv;

  key = CryptoJS.enc.Utf8.parse(key);
  iv = CryptoJS.enc.Utf8.parse(iv);

  // 加密模式为CBC，补码方式为PKCS5Padding（也就是PKCS7）
  let encrypted = CryptoJS.AES.encrypt(word, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  // 返回base64
  return CryptoJS.enc.Base64.stringify(encrypted.ciphertext);
};

let loading = {
  clear() {},
};

export const middlewareAxios = baseUrl => {
  const creatAxios = axios.create({
    baseURL: baseUrl,
  });
  creatAxios.interceptors.request.use(
    config => {
      Object.keys(axiosBaseHeadersConfig).forEach(key => {
        config.headers[key] = axiosBaseHeadersConfig[key];
      });

      // 加token
      if (store.getters.token) {
        config.headers.token = getToken();
      }

      /* post 需要验签*/
      if (config.method === "post") {
        console.log(config.data, config.url);
        config.headers["Content-Type"] =
					config.headers["Content-Type"] || "application/x-www-form-urlencoded";
        let stamp = new Date().getTime();
        const params = { ...(config.data || {}), stamp };
        // 数据+签名加密
        const base64 = params ? encodeURI(cryptoEncrypt(JSON.stringify({ ...params, stamp }))) : "";
        config.headers["sign"] = getMD5(`${base64}${requestConfig.key}${stamp}`);
        if (params) {
          config.data = "data=" + base64;
        }
        return config;
      }
      return config;
    },
    error => {
      return Promise.reject(error);
    },
  );
  creatAxios.interceptors.response.use(
    response => {
      loading.clear();

      const responseData = response.data;

      switch (responseData.code) {
      case 0:
        return response.data.data;
      case 401:
        if (!isShow401) {
          isShow401 = true;
          Message({
            message: "登录已失效，请重新登录!",
            type: "error",
            duration: 2000
          });
          setTimeout(() => {
            isShow401 = false;
          }, 2000);
          store.dispatch("FedLogOut").then(() => {
            router.replace("/login");
            wsClose();
            /* 页面刷新*/
            setTimeout(() => {
              location.reload();
            }, 500);
          }).catch(() => {});
        }
        break;
      case 20501:
        return Promise.reject(response);
      default:
        Message.error(responseData.message);
        break;
      }
      return Promise.reject(response);
    },
    error => {
      loading.clear();
      return Promise.reject(error);
    },
  );
  return creatAxios;
};


const service = middlewareAxios(process.env.VUE_APP_BASE_URL);

export const request = service;
