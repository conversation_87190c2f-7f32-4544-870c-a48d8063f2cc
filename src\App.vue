<template>
  <div id="app">
    <app-the-list-updates-the-data />
    <!-- 案源不足提示   -->
    <insufficient-source-of-the-case />
    <!-- 新用户 新手指南引导   -->
    <newcomer-onboarding-notification />
    <!-- 新手引导   -->
    <beginner-guide  />
    <router-view  />
    <!--  支付  -->
    <pay-pop />
    <!--  嵌入H5链接  -->
    <iframe-preview
      :title="getIframePreviewInfo.title||''"
      :url="getIframePreviewInfo.previewUrl"
      :size="getIframePreviewInfo.size||'720px'"
      :tab-bar-list="getIframePreviewInfo.tabBarList||[]"
      :active-tab-bar-index="getIframePreviewInfo.activeTabBarIndex||0"
      :is-file-preview="getIframePreviewInfo.isFilePreview||false"
      :nest-app="getIframePreviewInfo.nestApp||false" />
    <calculator-drawer />
  </div>
</template>
<script>


import { createWs, wsClose } from "utils/creat-ws.js";
import PayPop from "components/payPop/index.vue";
import { mapGetters }  from "vuex";
import CalculatorDrawer from "components/CalculatorDrawer/index.vue";

export default {
  components: {
    CalculatorDrawer,
    IframePreview: () => import("components/iframePreview/index.vue"),
    PayPop,
    BeginnerGuide: () => import("components/BeginnerGuide/index.vue"),
    NewcomerOnboardingNotification: () => import("components/SystemNotifications/newcomerOnboardingNotification/index.vue"),
    InsufficientSourceOfTheCase: () => import("components/SystemNotifications/insufficientSourceOfTheCase/index.vue"),
    AppTheListUpdatesTheData: () => import("components/SystemNotifications/appTheListUpdatesTheData/index.vue"),
  },
  data() {
    return {
      wsTimer: ""
    };
  },
  computed: {
    ...mapGetters([
      "getIframePreviewInfo"
    ])
  },
  // 监听路由变化
  watch: {
    // "$route"(to, from) {
    //   // 更新派单状态
    //   if (getToken()) {
    //     this.$store.dispatch("getPdStatus");
    //   }
    // }
  },
  mounted() {
    document.removeEventListener("visibilitychange", this.fallback);
    document.addEventListener("visibilitychange", this.fallback);
    this.loginIM();
    //
    // /* 认证失败重新登录*/
    // onAuthenticationFailed(() => {
    //   imLogin(true);
    // });

  },
  methods: {
    fallback() {
      this.loginIM();
    },
    loginIM() {
      /* if (getToken()) {
        // imLogin();
        // this.$store.dispatch("getUserInfo");
      }*/
      clearTimeout(this.wsTimer);
      /* 只有页面激活的时候才链接im*/
      if (document.visibilityState !== "hidden") {
        /* 这里加个延时器 是以防标签切的过快 导致链接频繁*/
        this.wsTimer = setTimeout(() => {
          createWs();
        }, 2000);
      }else{
        wsClose();
      }
    }
  }
};
</script>

<style lang="scss">
  html, body, #app{
    width: 100%;
    height: 100%;
  }
#app {
  font-family: Microsoft YaHei, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /*text-align: center;*/
  color: $text-color-main;
  font-size: $font-size-small;
  background:#F3F4F8 ;

  -webkit-user-select: none; /* Safari */
  -moz-user-select: none;    /* Firefox */
  -ms-user-select: none;     /* IE/Edge */
  user-select: none;         /* 标准语法 */
}
</style>
