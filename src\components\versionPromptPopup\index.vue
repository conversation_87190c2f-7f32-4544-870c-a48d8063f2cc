<template>
  <div  class="w-[264px] h-[36px] box-border px-[16px] py-[6px] bg-[rgba(0,0,0,0.7)] rounded-[8px] flex items-center justify-between fixed top-[40px] right-[24px]">
    <p class="text-[14px] text-[#FFFFFF]">发现新的版本，请立即刷新</p>
    <app-button type="success" size="small" @click="refresh">刷新</app-button>
  </div>
</template>

<script>
import AppButton from "components/appButton/index.vue";
import axios from "axios";

export default {
  name: "VersionPromptPopup",
  components: { AppButton },
  data() {
    return {
    };
  },
  methods: {
    refresh(){
      const loading = this.$loading({
        lock: true,
        text: "请求最新资源中，请求完成后自动刷新",
        spinner: "el-icon-loading",
        background: "rgba(0, 0, 0, 0.7)"
      });
      axios.get("/", {
        headers: {
          "Cache-Control": "no-cache",
          "Pragma": "no-cache"
        }
      }).then(() => {
        location.reload();
      }).finally(() => {
        loading.close();
      });
    }
  }
};
</script>

<style scoped lang="scss">

</style>
