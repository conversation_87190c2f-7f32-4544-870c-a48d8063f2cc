// 金额输入的展示
<template>
  <div class="money">
      <el-input
        :id="idName"
        v-model="inputBindValue"
        :disabled="disabled"
        :maxlength="maxlength"
        :placeholder="placeholder"
        :readonly="readonly"
        autocomplete="off"
        class="input-item-"
        type="text"
        @blur="$_onBlur"
        @focus="$_onFocus"
        @input="$_onInput" />
  </div>
</template>

<script>
import { getCursorsPosition, setCursorsPosition } from "./curosr";

export default {
  name: "InputMoney",
  props: {
    value: {
      type: [String, Number],
      default: 0
    },
    idName: {
      type: String,
      default: "iptMoney",
      desc: "不同组件的id"
    },
    placeholder: {
      type: String,
      default: "",
      desc: "输入提示"
    },
    maxlength: {
      type: [String, Number],
      default: ""
    },
    KeepDecimal: {
      type: String,
      default: "",
      desc: "保留小数位"
    },
    readonly: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: false
    }

  },
  data() {
    return {
      inputBindValue: "",
      inputValue: ""
    };
  },
  watch: {
    value(val) {
      // Filter out two-way binding
      if (val !== this.$_trimValue(this.inputValue)) {
        this.inputValue = this.$_formateValue(this.$_subValue(val + "")).value;
      }
    },
    inputValue(val) {
      this.inputBindValue = val;
      val = this.$_trimValue(val);
      // console.log(val)
      if (val !== this.value) {
        this.$emit("input", val);
        // this.$emit('change', this.name, val)
      }
    }
  },
  created() {
    if (this.value) this.inputValue = this.$_formateValue(this.$_subValue(this.value + "")).value;
  },
  methods: {
    $_onFocus(e) {
      // console.log(e)
      // this.isInputFocus = true
      // this.$emit('focus', this.inputValue)
    },
    $_onBlur() {
      setTimeout(() => {
        this.isInputFocus = false;
        this.$emit("blur", this.name);
      }, 100);
    },
    // 去除金额的,
    $_trimValue(val) {
      return this.trimValue(val, "\\s|,");
    },
    $_formateValue(curValue, curPos = 0) {
      // console.log(curValue)
      const oldValue = this.inputValue;
      const isAdd = oldValue.length > curValue.length ? -1 : 1;
      let formateValue = { value: curValue, range: curPos };

      // default format by component
      const gap = ",";
      // 去除空格和数字以为的字符
      curValue = this.$_subValue(this.trimValue(curValue.replace(/[^\d.]/g, "")));
      // curValue = curValue.replace(/\D/g, '')
      const dotPos = curValue.indexOf(".");

      // 只允许有一个小数点
      const moneyCurValue = curValue.split(".")[0];
      let moneyCurDecimal = ~dotPos ? `.${curValue.split(".")[1]}` : "";
      // 可以输入，小数点后面的几位数
      if (this.KeepDecimal) {
        moneyCurDecimal = moneyCurDecimal.substr(0, ~~this.KeepDecimal + 1);
      }
      // 可以输入，整数的时候
      if (this.KeepDecimal === "0") {
        moneyCurDecimal = moneyCurDecimal.substr(0, ~~this.KeepDecimal);
      }

      formateValue = this.formatValueByGapStep(
        3,
        this.trimValue(moneyCurValue, gap),
        gap,
        curPos,
        isAdd,
        oldValue.split(".")[0]
      );
      formateValue.value += moneyCurDecimal;
      // console.log(formateValue)
      return formateValue;
    },
    // 最大能输入多少长度
    $_subValue(val) {
      const len = this.maxlength;
      if (len !== "") {
        return val.substring(0, len);
      } else {
        return val;
      }
    },

    // 输入框输入
    $_onInput(value, e) {
      const formateValue = this.$_formateValue(value, getCursorsPosition(document.getElementById(this.idName)));
      this.inputValue = formateValue.value;
      this.inputBindValue = formateValue.value;

      // console.log(this.$refs['iptMoney'], '节点', document.getElementById('iptMoney'))
      this.$nextTick(() => {
        setCursorsPosition(document.getElementById(this.idName), formateValue.range);
      });
    },
    // 正则匹配，不能有空格
    trimValue(value, gap = " ") {
      // console.log(value)
      value = typeof value === "undefined" ? "" : value;
      const reg = new RegExp(gap, "g");
      value = value.toString().replace(reg, "");
      // console.log(value)

      return value;
    },

    formatValueByGapStep(step, value, gap = " ", range, isAdd = 1, oldValue = "") {
      if (value.length === 0) {
        return { value, range };
      }

      const arr = value && value.split("");
      // console.log(arr, 1)
      let _range = range;
      let showValue = "";

      for (let j = arr.length - 1, k = 0; j >= 0; j--, k++) {
        const m = arr[j];
        showValue = k > 0 && k % step === 0 ? m + gap + showValue : m + "" + showValue;
      }
      // console.log(oldValue.length, showValue.length)
      if (isAdd === 1) {
        // 在添加的情况下，如果添加前字符串的长度减去新的字符串的长度为2，说明多了一个间隔符，需要调整range
        if (oldValue.length - showValue.length === -2) {
          _range = range + 1;
        }
      } else {
        // 在删除情况下，如果删除前字符串的长度减去新的字符串的长度为2，说明少了一个间隔符，需要调整range
        if (oldValue.length - showValue.length === 2) {
          _range = range - 1;
        }
        // 删除到最开始，range 保持 0
        if (_range <= 0) {
          _range = 0;
        }
      }

      // console.log(_range, 99999)

      return { value: showValue, range: _range };
    }

  }
};
</script>

<style lang="scss" scoped>
  .money{

  }
</style>
