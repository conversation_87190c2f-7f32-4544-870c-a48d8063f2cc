<template>

  <div class="tabs">
    <el-tabs v-model="active" @tab-click="handleClick">
      <el-tab-pane v-for="i in listTag" :key="i.id"  :name="i.id">
       <span slot="label">
					{{i.label}}
					<el-popover v-if="i.img" placement="top-start" width="200" trigger="hover">
						<i class="el-icon-info" slot="reference" />
						<el-image
							style="width: 100px; height: 100px"
							class="m-b-15"
							fit="cover"
							:src="i.img"
							:preview-src-list="[i.img]"
						/>
					</el-popover>
				</span>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  name: "Tabs",
  props: {
    listTag: {
      type: Array,
      default: () => {
        return [];
      }
    },
    value: {
      type: String,
      default: "1"
    }
  },
  data() {
    return {
      active: this.value
    };
  },
  computed: {
    tag() {
      return this.listTag.length || 0;
    }
  },
  methods: {
    handleClick() {
      console.log(this.active);
      this.$emit("input", this.active);
    }
  }
};
</script>

<style lang="scss" scoped>
  .tabs{

  }
</style>
